



















































# Substrait
- The /space/ repo contains a substrait submodule that links to the actual substrait GitHub repo rather than containing the code within it.

# SDK
- The SDK currently only works without filters and only with prebuilt kernels/internal functions - the filter step in test_geoimagecollection_operations.py fails.

# Task Management
- User has a Notion workspace 'Terrafloww HQ' with a 'Projects Tracker' document for managing project tasks and wants tasks from new_tasks.md to be transferred there.
- User prefers Linear workflow with smaller, granular tickets organized by milestones rather than large descriptions, and wants frequent ticket movement for better progress tracking.
- User prefers to update Jira tickets and subtasks after completing development work before proceeding to next tasks.
- User prefers creating markdown files to document Jira ticket comments and updates rather than using direct API comment calls.
- User prefers accurate status reporting and wants Jira ticket verification before making completion percentage claims.
- User prefers sequential planning approach for complex work with dependent subtasks and codebase changes.

# Troubleshooting
- User prefers proactive troubleshooting approach: always check status, understand why things are working/not working using kubectl/doctl commands, and ask for manual help when needed rather than forcing solutions.

# Container Registry
- User has Dock<PERSON> daemon active on Arch Linux EndeavourOS system and prefers using doctl registry commands for container registry operations with pre-configured authentication.

# Ray Clusters on DigitalOcean
- For Ray clusters on DigitalOcean: use 4-6GB memory for head nodes with --object-store-memory=536870912, increase probe timeouts to 10s, and prefer 2vCPU/4GB droplets ($48/mo) for baseline workers with dedicated CPU for production.
- KubeRay versions 1.3.1 and below have bugs; should use version 1.3.2+ which includes important fixes for Ray cluster deployments.

# Data Storage
- STAC delta tables should be written to DigitalOcean storage (append-only, no upserts needed) and codebase needs cloud-native modifications for proper end-to-end testing.

# Environment Variables
- User prefers using python-dotenv library (load_dotenv) for loading environment variables instead of manual string parsing operations.
`