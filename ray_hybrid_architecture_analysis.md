# Ray Hybrid Architecture: Stateless Tasks + Persistent HTTP Actors
**Date**: 2025-06-26  
**Approach**: Keep Ray tasks stateless, use Ray actors only for HTTP connection pooling

## 🎯 **Hybrid Architecture Overview**

### **Current Flow (Inefficient)**
```
Ray Task → Create HTTP Client → Fetch COG → Process → Destroy Client
Ray Task → Create HTTP Client → Fetch COG → Process → Destroy Client
Ray Task → Create HTTP Client → Fetch COG → Process → Destroy Client
```
**Problem**: New HTTP client per task = no connection pooling

### **Proposed Hybrid Flow (Efficient)**
```
Ray Task → HTTP Actor Pool → Reuse Connection → Return Data → Process in Task
Ray Task → HTTP Actor Pool → Reuse Connection → Return Data → Process in Task  
Ray Task → HTTP Actor Pool → Reuse Connection → Return Data → Process in Task
```
**Solution**: Stateless tasks + persistent HTTP connection pool

## 🏗️ **Architecture Design**

### **1. Stateless Ray Tasks (Unchanged Logic)**
```python
@ray.remote(**DEFAULT_WORKER_RESOURCES)
def process_batch_on_worker(
    window_specs_batch: List[WindowSpec],
    plan_apply_steps_serializable: List[Dict[str, Any]],
    execution_id: str,
    flight_address: str
) -> bool:
    """
    Ray task remains stateless - just delegates HTTP to actor pool
    """
    async def _run_async_logic():
        # NEW: Get HTTP actor pool reference
        http_pool = ray.get_actor("http_connection_pool")
        
        # Delegate HTTP fetching to persistent actors
        raw_data = await http_pool.fetch_batch.remote(window_specs_batch)
        
        # Process data in task (existing logic unchanged)
        processed_data = process_cog_data(raw_data, plan_apply_steps_serializable)
        
        # Upload to Flight (existing logic unchanged)
        return upload_to_flight(processed_data, execution_id, flight_address)
    
    return asyncio.run(_run_async_logic())
```

### **2. Persistent HTTP Actor Pool (New Component)**
```python
@ray.remote
class HTTPConnectionPool:
    def __init__(self, pool_size: int = 4):
        self.pool_size = pool_size
        self.http_clients = []
        self.round_robin_index = 0
    
    async def initialize(self):
        """Create persistent HTTP clients"""
        for i in range(self.pool_size):
            client = httpx.AsyncClient(
                limits=httpx.Limits(
                    max_keepalive_connections=50,
                    max_connections=100,
                    keepalive_expiry=300.0
                ),
                http2=True,
                timeout=httpx.Timeout(30.0, connect=10.0)
            )
            self.http_clients.append(client)
        logger.info(f"Initialized {self.pool_size} persistent HTTP clients")
    
    async def fetch_batch(self, window_specs: List[WindowSpec]) -> Dict[str, bytes]:
        """Fetch COG data using persistent connections"""
        # Group requests by URL for connection reuse
        url_groups = self._group_by_url(window_specs)
        
        results = {}
        for url, specs in url_groups.items():
            # Get next available HTTP client (round-robin)
            client = self._get_next_client()
            
            # Merge byte ranges for efficiency
            merged_ranges = self._merge_byte_ranges(specs)
            
            # Fetch with persistent connection
            for start, end in merged_ranges:
                headers = {"Range": f"bytes={start}-{end}"}
                response = await client.get(url, headers=headers)
                results[f"{url}:{start}-{end}"] = response.content
        
        return results
    
    def _get_next_client(self):
        """Round-robin client selection"""
        client = self.http_clients[self.round_robin_index]
        self.round_robin_index = (self.round_robin_index + 1) % len(self.http_clients)
        return client
```

### **3. Driver Initialization (Minimal Changes)**
```python
class RayDriver:
    async def _ensure_ray_initialized(self, address: str):
        """Ensure Ray is initialized with HTTP pool"""
        if not ray.is_initialized():
            ray.init(address=address, ignore_reinit_error=True)
            
            # NEW: Initialize HTTP connection pool once
            await self._initialize_http_pool()
    
    async def _initialize_http_pool(self):
        """Initialize persistent HTTP connection pool"""
        try:
            # Try to get existing pool
            http_pool = ray.get_actor("http_connection_pool")
            logger.info("Retrieved existing HTTP connection pool")
        except ValueError:
            # Create new pool if doesn't exist
            http_pool = HTTPConnectionPool.options(
                name="http_connection_pool",
                lifetime="detached"  # Survives driver restarts
            ).remote(pool_size=4)
            await http_pool.initialize.remote()
            logger.info("Created new HTTP connection pool")
    
    async def execute_workflow(self, execution_id: str, plan):
        """Execute workflow - no changes to task submission logic"""
        # Existing task submission logic unchanged
        for spatial_key, window_data in windows_to_process:
            task_futures.append(
                process_batch_on_worker.remote(  # Same Ray task
                    specs_for_chunk,
                    plan_apply_steps_serializable,
                    execution_id,
                    flight_address
                )
            )
```

## ✅ **Benefits of Hybrid Approach**

### **1. Minimal Code Changes**
- **Ray tasks remain stateless** - existing logic unchanged
- **Driver logic unchanged** - just adds HTTP pool initialization
- **Worker function signature unchanged** - internal implementation optimized
- **Flight server unchanged** - no integration changes needed

### **2. Clean Separation of Concerns**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Ray Tasks     │    │  HTTP Actor Pool │    │  Flight Server  │
│   (Stateless)   │    │   (Persistent)   │    │   (Unchanged)   │
├─────────────────┤    ├──────────────────┤    ├─────────────────┤
│ • COG Processing│───▶│ • HTTP Clients   │    │ • Result Cache  │
│ • NDVI Kernels  │    │ • Connection     │    │ • Data Serving  │
│ • Flight Upload │    │   Pooling        │    │ • Status Mgmt   │
│ • Error Handling│    │ • Range Merging  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### **3. Optimal Resource Usage**
- **Tasks**: CPU-intensive processing (NDVI, reprojection, kernels)
- **Actors**: I/O-intensive operations (HTTP connections, range requests)
- **Automatic scaling**: Ray tasks scale with workload, HTTP pool stays constant

### **4. Fault Tolerance**
- **Task failures**: Ray automatically retries failed tasks
- **Actor failures**: HTTP pool can restart without affecting task logic
- **Connection health**: Actors monitor and reset unhealthy connections
- **Graceful degradation**: Tasks can fallback to direct HTTP if pool unavailable

## 🔧 **Implementation Strategy**

### **Phase 1: Add HTTP Actor Pool (Zero Breaking Changes)**
```python
# NEW FILE: runtime_ray/http_pool.py
class HTTPConnectionPool:
    # Implementation as shown above

# MODIFY: driver.py (add pool initialization)
async def _ensure_ray_initialized(self, address: str):
    # Existing logic + HTTP pool initialization

# MODIFY: worker.py (delegate HTTP to pool)
async def _run_async_logic():
    # Replace direct HTTP with pool delegation
```

### **Phase 2: Feature Flag Rollout**
```python
# Environment variable control
USE_HTTP_POOL = os.environ.get("TFW_USE_HTTP_POOL", "false").lower() == "true"

if USE_HTTP_POOL:
    # Use HTTP actor pool
    http_pool = ray.get_actor("http_connection_pool")
    raw_data = await http_pool.fetch_batch.remote(window_specs_batch)
else:
    # Use existing direct HTTP (fallback)
    async with httpx.AsyncClient() as client:
        raw_data = await fetch_directly(client, window_specs_batch)
```

### **Phase 3: Performance Optimization**
```python
# Add advanced features to HTTP pool
class HTTPConnectionPool:
    async def fetch_batch_with_optimization(self, window_specs):
        # Range merging
        # Connection health monitoring  
        # Request batching
        # Caching layer
```

## 📊 **Performance Impact Prediction**

### **Current (Direct HTTP per Task)**
- **Connection overhead**: 100-200ms per task
- **No connection reuse**: TCP handshake every request
- **No range merging**: Individual byte range requests
- **Result**: 123 seconds for NDVI processing

### **Hybrid (HTTP Pool + Stateless Tasks)**
- **Connection reuse**: 4 persistent HTTP clients
- **Range merging**: Combine nearby byte ranges
- **HTTP/2 multiplexing**: Multiple requests per connection
- **Predicted result**: 6-15 seconds for NDVI processing

## 🎯 **Why This Approach is Superior**

### **1. Preserves Your Architecture**
- **No changes to task logic** - existing processing code unchanged
- **No changes to driver flow** - task submission logic unchanged  
- **No changes to Flight integration** - result handling unchanged
- **Minimal risk** - HTTP optimization is isolated component

### **2. Natural Ray Patterns**
- **Tasks for computation** - CPU-intensive processing
- **Actors for state** - HTTP connection pooling
- **Automatic scaling** - Ray handles task distribution
- **Resource efficiency** - Optimal CPU vs I/O allocation

### **3. Easy Rollback**
```python
# Simple feature flag controls entire optimization
if USE_HTTP_POOL:
    # Optimized path
else:
    # Original path (unchanged)
```

## 🚀 **Implementation Timeline**

### **Week 1**: HTTP Pool Foundation
- Create `HTTPConnectionPool` actor class
- Add pool initialization to driver
- Add feature flag to worker
- Deploy with pool **disabled**

### **Week 2**: Gradual Enablement  
- Enable HTTP pool in staging
- Performance testing and tuning
- Monitor connection reuse metrics
- Optimize pool size and configuration

### **Week 3**: Production Rollout
- Enable HTTP pool in production
- Monitor 6-second performance target
- Remove fallback code
- Add advanced optimizations

This hybrid approach gives you **all the performance benefits** with **minimal architectural changes** and **zero risk** to your existing stateless task logic!
