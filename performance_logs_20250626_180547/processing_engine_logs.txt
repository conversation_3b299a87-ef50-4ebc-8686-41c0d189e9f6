[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2771, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,348 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,357 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,359 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,363 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,363 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,364 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,412 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,412 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,422 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/6/S2B_43PGQ_20240611_0_L2A/B04.tif with 1 requests.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,422 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/6/S2B_43PGQ_20240611_0_L2A/B08.tif with 1 requests.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,422 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching data for 2 URLs concurrently...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,423 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:28,549 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f3418e3e480>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:28,549 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f3418e93550> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,684 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad411ab440>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,684 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7fad4119ae50> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:28,733 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'VtjIDWK9/YvJd0ALgof8kcRaTB7SF1H6qT2Zov0Rf7v111jw4TdG2pGBy4am4yES2iYJpmJmmIIJBr9Tgl3Tiw=='), (b'x-amz-request-id', b'DBNVCC5X22MGA235'), (b'Date', b'Thu, 26 Jun 2025 12:23:29 GMT'), (b'Last-Modified', b'Sat, 29 Jun 2024 03:38:59 GMT'), (b'ETag', b'"ecaa2848e9f95837b8ef43ffb8bc496f-27"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 146995298-148470678/223780392'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1475381'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:28,733 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/6/S2B_43PGQ_20240621_0_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:28,733 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:28,782 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f3418e3d790>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:28,782 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:28,783 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:28,783 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:28,783 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:28,783 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,930 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad411a8ce0>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,931 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,931 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,931 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,931 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,931 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:28,932 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:29,179 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'+bImqR3OxN/pR5vxYB/G7LSWmA361I3Lu2m/DRzmy98wUDoAQWFAAG47C8SG+/T6qeHxtK4P4xA='), (b'x-amz-request-id', b'DBNMSGS820RGHZD1'), (b'Date', b'Thu, 26 Jun 2025 12:23:29 GMT'), (b'Last-Modified', b'Sat, 29 Jun 2024 03:41:46 GMT'), (b'ETag', b'"daf51435765aad8dcf8e2f2df10a32b8-26"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 141475360-142877897/213905601'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1402538'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:29,179 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/6/S2B_43PGQ_20240621_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:29,179 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,171 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad411ab680>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,171 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7fad4119ae50> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,343 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'x5NXSL6AgWhB4tkYbiGeO4qwKS07r+tP8/z6C+6HY8fVD+RsRLBNzJWkOW7zjQ0tWBj8AAgnG3tuwhgW6msb8g=='), (b'x-amz-request-id', b'W4SN6VKJ78CZ49MH'), (b'Date', b'Thu, 26 Jun 2025 12:23:30 GMT'), (b'Last-Modified', b'Tue, 11 Jun 2024 10:06:20 GMT'), (b'ETag', b'"a8782bc4fa32e10df36c6761f96c33b1-22"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 120131346-121115334/184263030'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'983989'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,343 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/6/S2B_43PGQ_20240611_0_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,343 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,405 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad411aaff0>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,406 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,406 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,406 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,406 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,406 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:29,658 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:29,663 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:29,664 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2644, ip=************)[0m 2025-06-26 05:23:29,639 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3248316 bytes total
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,801 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'0UpuWc+cZQYhwn1MCqCruzIjJji56L8el7yLAidgewMwEcG14gGBxaov3rjXrz0gR5AcFq+nhrTPtDHN3/2yLQ=='), (b'x-amz-request-id', b'W4SJWH1TEGRP68ER'), (b'Date', b'Thu, 26 Jun 2025 12:23:30 GMT'), (b'Last-Modified', b'Tue, 11 Jun 2024 10:08:30 GMT'), (b'ETag', b'"1a77af85db621703799b249572febd7a-22"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 115871450-116839075/176249853'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'967626'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,801 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/6/S2B_43PGQ_20240611_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:29,801 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:29,910 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:29,911 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:29,911 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:30,080 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:30,085 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:30,085 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2680, ip=************)[0m 2025-06-26 05:23:30,067 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2087609 bytes total
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,550 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,550 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,550 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,958 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,958 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,959 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,960 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,960 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,960 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,960 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,960 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,977 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,996 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,999 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:30,999 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,000 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,000 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,000 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,000 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,000 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(HTTPConnectionPool pid=2716, ip=************)[0m 2025-06-26 05:23:31,206 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 1581694 bytes total
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:31,218 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:31,224 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:31,224 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,312 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,328 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2B_43PGQ_20240611_0_L2A_T0507_9a18
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,328 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,328 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,328 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7fad4ac051c0>, Params: {'nir_band': 'nir', 'red_band': 'red'}, Type of Params: <class 'dict'>
INFO:app.flight_server:Received do_put data for execution: job_03aabe667e5342f3b3e00c5e0fd49527
INFO:app.flight_server:Finished receiving data for job_03aabe667e5342f3b3e00c5e0fd49527 from one worker.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,623 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,623 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,623 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,623 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,623 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,623 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:31,672 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(HTTPConnectionPool pid=2600, ip=************)[0m 2025-06-26 05:23:32,117 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 1566109 bytes total
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,123 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2771, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2771, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2771, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2771, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2771, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2771, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,154 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,154 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,155 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,155 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,155 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2771, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2771, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2771, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2771, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2771, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2771, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,155 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,163 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,165 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,169 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,169 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,170 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:32,130 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:32,137 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:32,137 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,218 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,218 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,228 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/7/S2A_43PGQ_20240716_0_L2A/B04.tif with 1 requests.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,229 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/7/S2A_43PGQ_20240716_0_L2A/B08.tif with 1 requests.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,229 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching data for 2 URLs concurrently...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,229 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,472 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad411ac410>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,472 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7fad4117bf50> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(HTTPConnectionPool pid=2644, ip=************)[0m 2025-06-26 05:23:32,782 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 1623091 bytes total
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,707 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad410eb920>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,707 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,708 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,708 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,708 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,708 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,709 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:32,794 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:32,801 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:32,801 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,955 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad414117f0>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:32,956 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7fad4117bf50> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,104 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'4YjZ6OB96M9wwFMDyoM7xfymr+2GGAWUTySqV8PCKGHxU3UxnAtHQ1FM2UYfqTYOuSrDqKCMPkbhaBy3610ULw=='), (b'x-amz-request-id', b'6J7H5KQD42T5CK6B'), (b'Date', b'Thu, 26 Jun 2025 12:23:33 GMT'), (b'Last-Modified', b'Tue, 16 Jul 2024 10:59:30 GMT'), (b'ETag', b'"1583afa0aa60eb0aebb039d53ed5a453-19"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 102562211-103561589/152279710'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'999379'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,105 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/7/S2A_43PGQ_20240716_0_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,105 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,188 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad411ab9e0>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,188 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,189 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,189 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,189 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,189 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:33,236 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(HTTPConnectionPool pid=2680, ip=************)[0m 2025-06-26 05:23:33,223 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2249048 bytes total
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:33,241 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:33,241 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2716, ip=************)[0m 2025-06-26 05:23:33,431 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3191119 bytes total
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:33,448 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:33,454 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:33,454 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,600 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'r9xi5fuQim1/gslKVBEqGuqvllEx1NOen8S7EKNdYnYPu8NqCr3PWRvAltUjNjmUaZ2jyEQRWBo='), (b'x-amz-request-id', b'N25AH44WDYAXN2FB'), (b'Date', b'Thu, 26 Jun 2025 12:23:34 GMT'), (b'Last-Modified', b'Tue, 16 Jul 2024 11:01:50 GMT'), (b'ETag', b'"0e14819bc0b4e64ea16e94cf03646dc3-19"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 102645184-103637222/152251867'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'992039'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,601 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/7/S2A_43PGQ_20240716_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:33,601 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(HTTPConnectionPool pid=2600, ip=************)[0m 2025-06-26 05:23:34,333 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2649535 bytes total
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:34,262 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:34,262 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:34,262 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:34,346 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:34,352 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:34,352 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2716, ip=************)[0m 2025-06-26 05:23:34,403 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2055185 bytes total
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:34,415 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:34,421 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:34,421 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2680, ip=************)[0m 2025-06-26 05:23:34,907 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2204188 bytes total
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:34,920 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:34,929 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:34,929 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2644, ip=************)[0m 2025-06-26 05:23:35,370 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3305393 bytes total
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:35,390 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:35,396 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:35,396 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,632 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,632 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,632 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,633 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,633 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,634 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,634 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,634 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,652 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,676 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,679 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,679 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,680 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,680 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,680 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,681 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,681 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(HTTPConnectionPool pid=2600, ip=************)[0m 2025-06-26 05:23:36,055 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2127268 bytes total
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:35,997 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,015 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2B_43PGQ_20240621_0_L2A_T0507_6716
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,015 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,015 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,015 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f341f44de40>, Params: {'nir_band': 'nir', 'red_band': 'red'}, Type of Params: <class 'dict'>
INFO:app.flight_server:Received do_put data for execution: job_03aabe667e5342f3b3e00c5e0fd49527
INFO:app.flight_server:Finished receiving data for job_03aabe667e5342f3b3e00c5e0fd49527 from one worker.
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:36,071 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:36,078 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:36,078 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2716, ip=************)[0m 2025-06-26 05:23:36,208 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 1713661 bytes total
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:36,221 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:36,229 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:36,229 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,355 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,355 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,355 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,355 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,355 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,355 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,408 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,877 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2770, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2770, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2770, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2770, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2770, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2770, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
[36m(HTTPConnectionPool pid=2680, ip=************)[0m 2025-06-26 05:23:36,890 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2746273 bytes total
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:36,938 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:36,946 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:36,946 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:36,907 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:36,916 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:36,916 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2644, ip=************)[0m 2025-06-26 05:23:36,918 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3234020 bytes total
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,906 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,906 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,906 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,907 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,907 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2770, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2770, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2770, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2770, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2770, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2770, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,907 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,913 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,916 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,920 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,920 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:36,925 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,022 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,023 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,039 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/11/S2B_43PGQ_20241108_0_L2A/B04.tif with 1 requests.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,040 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/11/S2B_43PGQ_20241108_0_L2A/B08.tif with 1 requests.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,040 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching data for 2 URLs concurrently...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,041 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,300 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f3418cc9e80>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,300 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f3418e6c2d0> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,534 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f3418cc9790>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,534 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,535 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,535 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,535 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,535 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,535 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:37,649 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:37,655 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:37,656 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2600, ip=************)[0m 2025-06-26 05:23:37,635 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 1812545 bytes total
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,787 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f3418e3f890>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,787 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f3418e6c2d0> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,912 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'nVLROHF/NTBCelYnR7wK9cPwx45Fq//EjaDapMtybPEccoi4K3pd6rjt+gt0S+iFXmUMGA5U2cAaBWWEHKUNNQ=='), (b'x-amz-request-id', b'EAFS41B2VF6EYK3K'), (b'Date', b'Thu, 26 Jun 2025 12:23:38 GMT'), (b'Last-Modified', b'Fri, 08 Nov 2024 09:33:10 GMT'), (b'ETag', b'"4898ae78261ec9dc70cbbc3468ce9957-28"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 149167478-150765019/232229315'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1597542'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,912 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/11/S2B_43PGQ_20241108_0_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:37,912 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:38,049 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:38,055 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:38,055 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2716, ip=************)[0m 2025-06-26 05:23:38,035 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2229272 bytes total
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:38,030 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f3418e3c0e0>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:38,031 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:38,032 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:38,032 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:38,032 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:38,032 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:38,422 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'b68O+V1t0jFihtKPDta6sv9G+KoA/Spo+n10HBLN7Qpa2ENjPl6P6KrS27lZLBrjUxgANJjiPxqRwP0KBPgxhun+XIAra+w2'), (b'x-amz-request-id', b'65V3R2DFAQXFXDF0'), (b'Date', b'Thu, 26 Jun 2025 12:23:39 GMT'), (b'Last-Modified', b'Fri, 08 Nov 2024 09:35:43 GMT'), (b'ETag', b'"66aa9273a4bcebb3d34540a16526baa8-30"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 155910323-157552186/243527130'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1641864'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:38,423 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/11/S2B_43PGQ_20241108_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:38,423 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(HTTPConnectionPool pid=2644, ip=************)[0m 2025-06-26 05:23:38,500 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3237920 bytes total
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:38,517 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:38,522 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:38,522 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2680, ip=************)[0m 2025-06-26 05:23:38,722 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3207317 bytes total
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:38,742 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:38,747 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:38,747 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,304 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,305 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,305 - httpcore.http11 - DEBUG - response_closed.complete
[36m(HTTPConnectionPool pid=2600, ip=************)[0m 2025-06-26 05:23:39,345 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 1353715 bytes total
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:39,367 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:39,373 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:39,373 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:39,359 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:39,367 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:39,367 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2716, ip=************)[0m 2025-06-26 05:23:39,356 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 1483350 bytes total
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,567 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,567 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,568 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,568 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,568 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,568 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,569 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,569 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,582 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,600 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,603 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,603 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,605 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,605 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,606 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,606 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,606 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:39,814 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:39,822 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:39,822 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2644, ip=************)[0m 2025-06-26 05:23:39,799 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 1442605 bytes total
[36m(HTTPConnectionPool pid=2680, ip=************)[0m 2025-06-26 05:23:39,886 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3122982 bytes total
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,864 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,865 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,865 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,865 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,865 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,866 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,866 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,866 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,884 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,914 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,921 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,921 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,922 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,922 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,922 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,923 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:39,923 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,954 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:39,945 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:39,957 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:39,957 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,971 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2A_43PGQ_20240716_0_L2A_T0507_be73
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,971 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,971 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:39,971 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7fad4ac051c0>, Params: {'nir_band': 'nir', 'red_band': 'red'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,266 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,282 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2B_43PGQ_20241108_0_L2A_T0507_467b
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,282 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,283 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,283 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f341f44de40>, Params: {'nir_band': 'nir', 'red_band': 'red'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:40,328 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:40,328 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:40,328 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:40,329 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:40,329 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:40,329 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:40,409 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,646 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,646 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,646 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,646 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,646 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,646 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:40,751 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(HTTPConnectionPool pid=2716, ip=************)[0m 2025-06-26 05:23:40,731 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 1797536 bytes total
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:40,708 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:40,759 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:40,759 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2600, ip=************)[0m 2025-06-26 05:23:40,862 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3208714 bytes total
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:40,886 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:40,902 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:40,902 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:40,963 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2771, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2771, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2771, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2771, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2771, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2771, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
INFO:app.flight_server:Received do_put data for execution: job_03aabe667e5342f3b3e00c5e0fd49527
INFO:app.flight_server:Finished receiving data for job_03aabe667e5342f3b3e00c5e0fd49527 from one worker.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,007 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,007 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,008 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,009 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,009 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2771, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2771, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2771, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2771, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2771, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2771, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,009 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,023 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,026 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,030 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,030 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,031 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,125 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,125 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,135 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/11/S2A_43PGQ_20241123_0_L2A/B04.tif with 1 requests.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,135 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/11/S2A_43PGQ_20241123_0_L2A/B08.tif with 1 requests.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,135 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching data for 2 URLs concurrently...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,136 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,272 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2770, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2770, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2770, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2770, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2770, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2770, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,301 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,302 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,303 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,303 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,303 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2770, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2770, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2770, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2770, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2770, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2770, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,303 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
INFO:app.flight_server:Received do_put data for execution: job_03aabe667e5342f3b3e00c5e0fd49527
INFO:app.flight_server:Finished receiving data for job_03aabe667e5342f3b3e00c5e0fd49527 from one worker.
[36m(HTTPConnectionPool pid=2644, ip=************)[0m 2025-06-26 05:23:41,379 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2715390 bytes total
[36m(HTTPConnectionPool pid=2680, ip=************)[0m 2025-06-26 05:23:41,357 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3119707 bytes total
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,324 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,332 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,335 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,335 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,337 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,381 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad411a8fe0>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,381 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7fad4117bed0> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:41,381 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:41,386 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:41,386 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:41,404 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:41,411 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:41,411 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,428 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,428 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,446 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2B_43PGQ_20241208_0_L2A/B04.tif with 1 requests.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,447 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2B_43PGQ_20241208_0_L2A/B08.tif with 1 requests.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,447 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching data for 2 URLs concurrently...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,448 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,622 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad411a8f20>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,623 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,623 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,623 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,623 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,623 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,624 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,696 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f3418cc5eb0>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,696 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f3418e92c50> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,935 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f3418cc5430>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,861 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad41131940>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:41,861 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7fad4117bed0> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,935 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,937 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,937 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,937 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,937 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:41,937 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:42,073 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(HTTPConnectionPool pid=2600, ip=************)[0m 2025-06-26 05:23:42,053 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2284194 bytes total
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,091 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7fad411a8b60>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,092 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,092 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,092 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,092 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,092 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,112 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'Z8JU7m/MUTKOGgu1oFtoGyhYTacntOpYro4HZWiWzJ0wKmP6h+xN9KmgYhggHS6tAKxokBkY2Ud6pcLJ0uX1yw=='), (b'x-amz-request-id', b'6NB029FWDVTT7DYH'), (b'Date', b'Thu, 26 Jun 2025 12:23:42 GMT'), (b'Last-Modified', b'Sat, 23 Nov 2024 10:06:05 GMT'), (b'ETag', b'"c90435669c1efd34c2cb65464ac270de-25"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes *********-*********/*********'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1420754'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,113 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/11/S2A_43PGQ_20241123_0_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,113 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:42,080 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:42,080 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,195 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f3418cc7050>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,195 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f3418e92c50> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,310 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'Q0T6Dtx4w72ZBXyI0S9h+xEPsTtWtxfFkV8K7y54wfBtW9tGQ10QocJhFVYr+qVXoInaLFfreStjQblg/4EX1g=='), (b'x-amz-request-id', b'K7HWSFN6R33DGYSV'), (b'Date', b'Thu, 26 Jun 2025 12:23:43 GMT'), (b'Last-Modified', b'Sun, 08 Dec 2024 09:07:52 GMT'), (b'ETag', b'"8c26b4ed548a3fe1a858b3f82e3836e8-28"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 149945635-151553915/233207135'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1608281'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,310 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2B_43PGQ_20241208_0_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,310 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,439 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f3418cc65d0>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,439 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,439 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,440 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,440 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,440 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,495 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'0LM7PV45P1P1esSOPgdpHakuyqIx4LV55FxsSPLwurvhQwlNZuG2c5csTIMz73GBuJR+QOOCcJk='), (b'x-amz-request-id', b'K7HR2T0BHS20Y6RR'), (b'Date', b'Thu, 26 Jun 2025 12:23:43 GMT'), (b'Last-Modified', b'Sat, 23 Nov 2024 10:08:21 GMT'), (b'ETag', b'"4d9641677de088373b94f3bc4071cd93-25"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 137674752-139078279/203331989'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1403528'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,495 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/11/S2A_43PGQ_20241123_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:42,495 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(HTTPConnectionPool pid=2716, ip=************)[0m 2025-06-26 05:23:42,690 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3063015 bytes total
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:42,707 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:42,715 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:42,715 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2644, ip=************)[0m 2025-06-26 05:23:42,795 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2669296 bytes total
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,853 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'TK4XWGxvDlKeayurnNst2kUNg2pbnNsWrV8sy6MgUN4SCqFpHCki2s/Oi7i51hkCRnibI9tMSmM='), (b'x-amz-request-id', b'K7HMJ26BGNYYDGKY'), (b'Date', b'Thu, 26 Jun 2025 12:23:43 GMT'), (b'Last-Modified', b'Sun, 08 Dec 2024 09:09:12 GMT'), (b'ETag', b'"359726a3239f34daaaf38f6467be6864-30"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 156511071-158159320/244483641'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1648250'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,853 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2B_43PGQ_20241208_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:42,853 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:42,809 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:42,815 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:42,815 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2680, ip=************)[0m 2025-06-26 05:23:43,095 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3169783 bytes total
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:43,111 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:43,118 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:43,118 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:43,538 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:43,538 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:43,538 - httpcore.http11 - DEBUG - response_closed.complete
[36m(HTTPConnectionPool pid=2600, ip=************)[0m 2025-06-26 05:23:43,781 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 2917136 bytes total
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:43,729 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:43,729 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:43,730 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:43,796 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:43,802 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:43,802 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(HTTPConnectionPool pid=2716, ip=************)[0m 2025-06-26 05:23:43,944 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 3097043 bytes total
[36m(process_batch_on_worker pid=85, ip=************)[0m 2025-06-26 05:23:43,961 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,369 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,370 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,370 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,371 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,371 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,371 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,372 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,372 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,391 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 05:23:44,411 - terrafloww.engine_core.runtime_ray.worker - INFO - HTTP actor pool fetched 2 results
[36m(HTTPConnectionPool pid=2644, ip=************)[0m 2025-06-26 05:23:44,397 - terrafloww.engine_core.runtime_ray.http_pool - DEBUG - Fetched 2 ranges, 1865799 bytes total
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,419 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,422 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,422 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,422 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,422 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,422 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,423 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,423 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,745 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,760 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2B_43PGQ_20241208_0_L2A_T0507_8a6c
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,761 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,761 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:44,761 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f341f44de40>, Params: {'nir_band': 'nir', 'red_band': 'red'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,083 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,083 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,083 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,083 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,083 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,083 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,138 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,619 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2770, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2770, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2770, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2770, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2770, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2770, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
INFO:app.flight_server:Received do_put data for execution: job_03aabe667e5342f3b3e00c5e0fd49527
INFO:app.flight_server:Finished receiving data for job_03aabe667e5342f3b3e00c5e0fd49527 from one worker.
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,646 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,646 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,646 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,647 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,647 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2770, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2770, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2770, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2770, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2770, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2770, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2770, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2770, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2770, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2770, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2770, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2770, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,647 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,652 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,654 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,658 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,658 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=2770, ip=************)[0m 2025-06-26 05:23:45,659 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,009 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,009 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,009 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,010 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,010 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,011 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,011 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,011 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,030 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,051 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,055 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,056 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,056 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,056 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,056 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,057 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,057 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
INFO:app.flight_server:Received do_put data for execution: job_03aabe667e5342f3b3e00c5e0fd49527
INFO:app.flight_server:Finished receiving data for job_03aabe667e5342f3b3e00c5e0fd49527 from one worker.
INFO:terrafloww.engine_core.runtime_ray.driver:All 72 workers for job_03aabe667e5342f3b3e00c5e0fd49527 completed successfully.
INFO:app.flight_server:Received COMPLETION signal for execution: job_03aabe667e5342f3b3e00c5e0fd49527
INFO:terrafloww.engine_core.runtime_ray.driver:Signaled completion to Flight server for execution job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,356 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,372 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2A_43PGQ_20241123_0_L2A_T0507_8961
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,373 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,373 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,373 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7fad4ac051c0>, Params: {'nir_band': 'nir', 'red_band': 'red'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,664 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,664 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,664 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,665 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,665 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,665 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:49,715 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,160 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2771, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2771, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2771, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2771, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2771, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2771, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,188 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,188 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,190 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,190 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_03aabe667e5342f3b3e00c5e0fd49527
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,190 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=2771, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=2771, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=2771, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=2771, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=2771, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=2771, ip=************)[0m label: string
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=2771, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=2771, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=2771, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=2771, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=2771, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=2771, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,190 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,201 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,203 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,206 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,206 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=2771, ip=************)[0m 2025-06-26 05:23:50,207 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
