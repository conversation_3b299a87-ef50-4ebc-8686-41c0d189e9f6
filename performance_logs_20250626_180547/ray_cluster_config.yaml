apiVersion: v1
items:
- apiVersion: ray.io/v1
  kind: RayCluster
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"ray.io/v1alpha1","kind":"RayCluster","metadata":{"annotations":{},"labels":{"app":"terrafloww","component":"ray-cluster"},"name":"terrafloww-ray-cluster","namespace":"terrafloww-platform"},"spec":{"enableInTreeAutoscaling":true,"headGroupSpec":{"rayStartParams":{"block":"true","dashboard-host":"0.0.0.0","dashboard-port":"8265","num-cpus":"0","object-store-memory":"536870912"},"serviceType":"ClusterIP","template":{"metadata":{"labels":{"app":"terrafloww","component":"ray-head"}},"spec":{"containers":[{"env":[{"name":"RAY_DISABLE_IMPORT_WARNING","value":"1"},{"name":"RAY_DEDUP_LOGS","value":"0"},{"name":"RAY_DASHBOARD_METRICS_COLLECTION_ENABLED","value":"0"},{"name":"RAY_USAGE_STATS_ENABLED","value":"0"},{"name":"TFW_USE_HTTP_POOL","value":"false"},{"name":"FLIGHT_HOST","value":"************"},{"name":"FLIGHT_PORT","value":"50052"}],"image":"registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657","imagePullPolicy":"Always","lifecycle":{"preStop":{"exec":{"command":["/bin/sh","-c","ray stop"]}}},"name":"ray-head","ports":[{"containerPort":6379,"name":"gcs-server"},{"containerPort":8265,"name":"dashboard"},{"containerPort":10001,"name":"client"}],"readinessProbe":{"exec":{"command":["bash","-c","wget -T 4 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success \u0026\u0026 wget -T 10 -q -O- http://localhost:8265/api/gcs_healthz | grep success"]},"failureThreshold":12,"periodSeconds":5,"timeoutSeconds":10},"resources":{"limits":{"cpu":"2","memory":"6Gi"},"requests":{"cpu":"1","memory":"4Gi"}},"volumeMounts":[{"mountPath":"/tmp/ray","name":"ray-logs"},{"mountPath":"/dev/shm","name":"shared-mem"}]}],"imagePullSecrets":[{"name":"registry-terrafloww-dev"}],"nodeSelector":{"doks.digitalocean.com/node-pool":"data-pool-e0y1e6npd"},"tolerations":[{"effect":"NoSchedule","key":"workload","operator":"Equal","value":"data"}],"volumes":[{"emptyDir":{},"name":"ray-logs"},{"emptyDir":{"medium":"Memory","sizeLimit":"4Gi"},"name":"shared-mem"}]}}},"rayVersion":"2.47.1","workerGroupSpecs":[{"groupName":"worker-group","maxReplicas":5,"minReplicas":1,"rayStartParams":{"block":"true","num-cpus":"2"},"replicas":1,"template":{"metadata":{"labels":{"app":"terrafloww","component":"ray-worker"}},"spec":{"containers":[{"env":[{"name":"RAY_DISABLE_IMPORT_WARNING","value":"1"},{"name":"RAY_DEDUP_LOGS","value":"0"},{"name":"RAY_USAGE_STATS_ENABLED","value":"0"},{"name":"TFW_USE_HTTP_POOL","value":"false"},{"name":"FLIGHT_HOST","value":"terrafloww-processing-engine-svc"},{"name":"FLIGHT_PORT","value":"50052"}],"image":"registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657","imagePullPolicy":"Always","lifecycle":{"preStop":{"exec":{"command":["/bin/sh","-c","ray stop"]}}},"name":"ray-worker","resources":{"limits":{"cpu":"2","memory":"4Gi"},"requests":{"cpu":"1","memory":"2Gi"}},"volumeMounts":[{"mountPath":"/tmp/ray","name":"ray-logs"},{"mountPath":"/dev/shm","name":"shared-mem"}]}],"imagePullSecrets":[{"name":"registry-terrafloww-dev"}],"nodeSelector":{"doks.digitalocean.com/node-pool":"data-pool-e0y1e6npd"},"restartPolicy":"Never","tolerations":[{"effect":"NoSchedule","key":"workload","operator":"Equal","value":"data"}],"volumes":[{"emptyDir":{},"name":"ray-logs"},{"emptyDir":{"medium":"Memory","sizeLimit":"2Gi"},"name":"shared-mem"}]}}}]}}
    creationTimestamp: "2025-06-26T12:08:29Z"
    generation: 10
    labels:
      app: terrafloww
      component: ray-cluster
    name: terrafloww-ray-cluster
    namespace: terrafloww-platform
    resourceVersion: "33696513"
    uid: 787d0fab-9ac7-49d3-a171-cd790539370f
  spec:
    enableInTreeAutoscaling: true
    headGroupSpec:
      rayStartParams:
        block: "true"
        dashboard-host: 0.0.0.0
        dashboard-port: "8265"
        num-cpus: "0"
        object-store-memory: "536870912"
      serviceType: ClusterIP
      template:
        metadata:
          labels:
            app: terrafloww
            component: ray-head
        spec:
          containers:
          - env:
            - name: TFW_USE_HTTP_POOL
              value: "true"
            name: ray-head
          imagePullSecrets:
          - name: registry-terrafloww-dev
          nodeSelector:
            doks.digitalocean.com/node-pool: data-pool-e0y1e6npd
          tolerations:
          - effect: NoSchedule
            key: workload
            operator: Equal
            value: data
          volumes:
          - emptyDir: {}
            name: ray-logs
          - emptyDir:
              medium: Memory
              sizeLimit: 4Gi
            name: shared-mem
    rayVersion: 2.47.1
    workerGroupSpecs:
    - groupName: worker-group
      maxReplicas: 5
      minReplicas: 1
      numOfHosts: 1
      rayStartParams:
        block: "true"
        num-cpus: "2"
      replicas: 1
      scaleStrategy:
        workersToDelete: []
      template:
        metadata:
          labels:
            app: terrafloww
            component: ray-worker
        spec:
          containers:
          - env:
            - name: RAY_DISABLE_IMPORT_WARNING
              value: "1"
            - name: RAY_DEDUP_LOGS
              value: "0"
            - name: RAY_USAGE_STATS_ENABLED
              value: "0"
            - name: TFW_USE_HTTP_POOL
              value: "true"
            - name: FLIGHT_HOST
              value: terrafloww-processing-engine-svc
            - name: FLIGHT_PORT
              value: "50052"
            image: registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657
            imagePullPolicy: Always
            lifecycle:
              preStop:
                exec:
                  command:
                  - /bin/sh
                  - -c
                  - ray stop
            name: ray-worker
            resources:
              limits:
                cpu: "2"
                memory: 4Gi
              requests:
                cpu: "1"
                memory: 2Gi
            volumeMounts:
            - mountPath: /tmp/ray
              name: ray-logs
            - mountPath: /dev/shm
              name: shared-mem
          imagePullSecrets:
          - name: registry-terrafloww-dev
          nodeSelector:
            doks.digitalocean.com/node-pool: data-pool-e0y1e6npd
          restartPolicy: Never
          tolerations:
          - effect: NoSchedule
            key: workload
            operator: Equal
            value: data
          volumes:
          - emptyDir: {}
            name: ray-logs
          - emptyDir:
              medium: Memory
              sizeLimit: 2Gi
            name: shared-mem
  status:
    availableWorkerReplicas: 1
    conditions:
    - lastTransitionTime: "2025-06-26T12:09:00Z"
      message: ""
      reason: HeadPodRunningAndReady
      status: "True"
      type: HeadPodReady
    - lastTransitionTime: "2025-06-26T12:08:30Z"
      message: All Ray Pods are ready for the first time
      reason: AllPodRunningAndReadyFirstTime
      status: "True"
      type: RayClusterProvisioned
    - lastTransitionTime: "2025-06-26T12:08:30Z"
      message: ""
      reason: RayClusterSuspended
      status: "False"
      type: RayClusterSuspended
    - lastTransitionTime: "2025-06-26T12:08:30Z"
      message: ""
      reason: RayClusterSuspending
      status: "False"
      type: RayClusterSuspending
    desiredCPU: "1"
    desiredGPU: "0"
    desiredMemory: 2Gi
    desiredTPU: "0"
    desiredWorkerReplicas: 1
    endpoints:
      client: "10001"
      dashboard: "8265"
      gcs-server: "6379"
      metrics: "8080"
    head:
      podIP: ************
      podName: terrafloww-ray-cluster-head-hhftc
      serviceIP: ************
      serviceName: terrafloww-ray-cluster-head-svc
    lastUpdateTime: "2025-06-26T12:27:23Z"
    maxWorkerReplicas: 5
    minWorkerReplicas: 1
    observedGeneration: 9
    readyWorkerReplicas: 1
    state: ready
    stateTransitionTimes:
      ready: "2025-06-26T12:08:30Z"
kind: List
metadata:
  resourceVersion: ""
