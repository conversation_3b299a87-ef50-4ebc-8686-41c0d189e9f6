[2025-06-26 05:09:08,590 W 1 1] global_state_accessor.cc:435: Retrying to get node with node ID 152862de6940efa864fed9b585bd350bc09a4e774b589d0979a7fb1b
[2025-06-26 05:09:09,592 W 1 1] global_state_accessor.cc:435: Retrying to get node with node ID 152862de6940efa864fed9b585bd350bc09a4e774b589d0979a7fb1b
2025-06-26 05:09:08,434	INFO scripts.py:1152 -- [37mLocal node IP[39m: [1m10.108.6.149[22m
2025-06-26 05:09:10,602	SUCC scripts.py:1168 -- [32m--------------------[39m
2025-06-26 05:09:10,603	SUCC scripts.py:1169 -- [32mRay runtime started.[39m
2025-06-26 05:09:10,603	SUCC scripts.py:1170 -- [32m--------------------[39m
2025-06-26 05:09:10,603	INFO scripts.py:1172 -- To terminate the Ray runtime, run
2025-06-26 05:09:10,603	INFO scripts.py:1173 -- [1m  ray stop[22m
2025-06-26 05:09:10,603	INFO scripts.py:1181 -- [36m[1m--block[22m[39m
2025-06-26 05:09:10,604	INFO scripts.py:1182 -- This command will now block forever until terminated by a signal.
2025-06-26 05:09:10,604	INFO scripts.py:1185 -- Running subprocesses are monitored and a message will be printed if any of them terminate unexpectedly. Subprocesses exit with SIGTERM will be treated as graceful, thus NOT reported.
