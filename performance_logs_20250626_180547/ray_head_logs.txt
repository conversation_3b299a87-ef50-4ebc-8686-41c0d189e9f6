2025-06-26 05:08:49,858	INFO usage_lib.py:441 -- Usage stats collection is disabled.
2025-06-26 05:08:49,858	INFO scripts.py:971 -- [37mLocal node IP[39m: [1m************[22m
2025-06-26 05:08:58,125	SUCC scripts.py:1007 -- [32m--------------------[39m
2025-06-26 05:08:58,125	SUCC scripts.py:1008 -- [32mRay runtime started.[39m
2025-06-26 05:08:58,125	SUCC scripts.py:1009 -- [32m--------------------[39m
2025-06-26 05:08:58,125	INFO scripts.py:1011 -- [36mNext steps[39m
2025-06-26 05:08:58,125	INFO scripts.py:1014 -- To add another node to this Ray cluster, run
2025-06-26 05:08:58,125	INFO scripts.py:1017 -- [1m  ray start --address='************:6379'[22m
2025-06-26 05:08:58,125	INFO scripts.py:1026 -- To connect to this Ray cluster:
2025-06-26 05:08:58,125	INFO scripts.py:1028 -- [35mimport[39m[26m ray
2025-06-26 05:08:58,125	INFO scripts.py:1029 -- ray[35m.[39m[26minit()
2025-06-26 05:08:58,125	INFO scripts.py:1041 -- To submit a Ray job using the Ray Jobs CLI:
2025-06-26 05:08:58,125	INFO scripts.py:1042 -- [1m  RAY_ADDRESS='http://************:8265' ray job submit --working-dir . -- python my_script.py[22m
2025-06-26 05:08:58,125	INFO scripts.py:1051 -- See https://docs.ray.io/en/latest/cluster/running-applications/job-submission/index.html 
2025-06-26 05:08:58,125	INFO scripts.py:1055 -- for more information on submitting Ray jobs to the Ray cluster.
2025-06-26 05:08:58,125	INFO scripts.py:1060 -- To terminate the Ray runtime, run
2025-06-26 05:08:58,126	INFO scripts.py:1061 -- [1m  ray stop[22m
2025-06-26 05:08:58,126	INFO scripts.py:1064 -- To view the status of the cluster, use
2025-06-26 05:08:58,126	INFO scripts.py:1065 --   [1mray status[22m[26m
2025-06-26 05:08:58,126	INFO scripts.py:1069 -- To monitor and debug Ray, view the dashboard at 
2025-06-26 05:08:58,126	INFO scripts.py:1070 --   [1m************:8265[22m[26m
2025-06-26 05:08:58,126	INFO scripts.py:1077 -- [4mIf connection to the dashboard fails, check your firewall settings and network configuration.[24m
2025-06-26 05:08:58,126	INFO scripts.py:1181 -- [36m[1m--block[22m[39m
2025-06-26 05:08:58,126	INFO scripts.py:1182 -- This command will now block forever until terminated by a signal.
2025-06-26 05:08:58,126	INFO scripts.py:1185 -- Running subprocesses are monitored and a message will be printed if any of them terminate unexpectedly. Subprocesses exit with SIGTERM will be treated as graceful, thus NOT reported.
