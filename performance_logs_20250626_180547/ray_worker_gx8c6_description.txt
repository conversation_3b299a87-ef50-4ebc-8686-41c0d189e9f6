Name:             terrafloww-ray-cluster-worker-group-worker-gx8c6
Namespace:        terrafloww-platform
Priority:         0
Service Account:  default
Node:             data-pool-e0y1e6npd-l3xcq/10.122.0.18
Start Time:       Thu, 26 Jun 2025 17:38:35 +0530
Labels:           app=terrafloww
                  app.kubernetes.io/created-by=kuberay-operator
                  app.kubernetes.io/name=kuberay
                  component=ray-worker
                  ray.io/cluster=terrafloww-ray-cluster
                  ray.io/group=worker-group
                  ray.io/identifier=terrafloww-ray-cluster-worker
                  ray.io/is-ray-node=yes
                  ray.io/node-type=worker
Annotations:      <none>
Status:           Running
IP:               ************
IPs:
  IP:           ************
Controlled By:  RayCluster/terrafloww-ray-cluster
Init Containers:
  wait-gcs-ready:
    Container ID:  containerd://658f17b2c5faadb2f816e0c7163a7da591d2f0ad0fb195d27a2988873458e6bb
    Image:         registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-********-173657
    Image ID:      registry.digitalocean.com/terrafloww-dev/ray-custom@sha256:****************************************************************
    Port:          <none>
    Host Port:     <none>
    Command:
      /bin/bash
      -lc
      --
    Args:
      
                            SECONDS=0
                            while true; do
                              if (( SECONDS <= 120 )); then
                                if ray health-check --address terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379 > /dev/null 2>&1; then
                                  echo "GCS is ready."
                                  break
                                fi
                                echo "$SECONDS seconds elapsed: Waiting for GCS to be ready."
                              else
                                if ray health-check --address terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379; then
                                  echo "GCS is ready. Any error messages above can be safely ignored."
                                  break
                                fi
                                echo "$SECONDS seconds elapsed: Still waiting for GCS to be ready. For troubleshooting, refer to the FAQ at https://github.com/ray-project/kuberay/blob/master/docs/guidance/FAQ.md."
                              fi
                              sleep 5
                            done
                          
    State:          Terminated
      Reason:       Completed
      Exit Code:    0
      Started:      Thu, 26 Jun 2025 17:38:46 +0530
      Finished:     Thu, 26 Jun 2025 17:39:02 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     200m
      memory:  256Mi
    Requests:
      cpu:     200m
      memory:  256Mi
    Environment:
      RAY_DISABLE_IMPORT_WARNING:  1
      RAY_DEDUP_LOGS:              0
      RAY_USAGE_STATS_ENABLED:     0
      TFW_USE_HTTP_POOL:           false
      FLIGHT_HOST:                 terrafloww-processing-engine-svc
      FLIGHT_PORT:                 50052
      FQ_RAY_IP:                   terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local
      RAY_IP:                      terrafloww-ray-cluster-head-svc
    Mounts:
      /dev/shm from shared-mem (rw)
      /tmp/ray from ray-logs (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-5qn77 (ro)
Containers:
  ray-worker:
    Container ID:  containerd://5af1e7eb024f36cf26fd16df25f1332b9e779e703ffc1910308ccaab616a1b33
    Image:         registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-********-173657
    Image ID:      registry.digitalocean.com/terrafloww-dev/ray-custom@sha256:****************************************************************
    Port:          8080/TCP
    Host Port:     0/TCP
    Command:
      /bin/bash
      -lc
      --
    Args:
      ulimit -n 65536; ray start  --address=terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379  --block  --dashboard-agent-listen-port=52365  --memory=**********  --metrics-export-port=8080  --num-cpus=2 
    State:          Running
      Started:      Thu, 26 Jun 2025 17:39:05 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     2
      memory:  4Gi
    Requests:
      cpu:      1
      memory:   2Gi
    Liveness:   exec [bash -c wget -T 2 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success] delay=30s timeout=2s period=5s #success=1 #failure=120
    Readiness:  exec [bash -c wget -T 2 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success] delay=10s timeout=2s period=5s #success=1 #failure=10
    Environment:
      RAY_DISABLE_IMPORT_WARNING:           1
      RAY_DEDUP_LOGS:                       0
      RAY_USAGE_STATS_ENABLED:              0
      TFW_USE_HTTP_POOL:                    false
      FLIGHT_HOST:                          terrafloww-processing-engine-svc
      FLIGHT_PORT:                          50052
      FQ_RAY_IP:                            terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local
      RAY_IP:                               terrafloww-ray-cluster-head-svc
      RAY_CLUSTER_NAME:                      (v1:metadata.labels['ray.io/cluster'])
      RAY_CLOUD_INSTANCE_ID:                terrafloww-ray-cluster-worker-group-worker-gx8c6 (v1:metadata.name)
      RAY_NODE_TYPE_NAME:                    (v1:metadata.labels['ray.io/group'])
      KUBERAY_GEN_RAY_START_CMD:            ray start  --address=terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379  --block  --dashboard-agent-listen-port=52365  --memory=**********  --metrics-export-port=8080  --num-cpus=2 
      RAY_PORT:                             6379
      RAY_ADDRESS:                          terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379
      RAY_USAGE_STATS_KUBERAY_IN_USE:       1
      RAY_DASHBOARD_ENABLE_K8S_DISK_USAGE:  1
    Mounts:
      /dev/shm from shared-mem (rw)
      /tmp/ray from ray-logs (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-5qn77 (ro)
Conditions:
  Type                        Status
  PodReadyToStartContainers   True 
  Initialized                 True 
  Ready                       True 
  ContainersReady             True 
  PodScheduled                True 
Volumes:
  ray-logs:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     
    SizeLimit:  <unset>
  shared-mem:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     Memory
    SizeLimit:  2Gi
  kube-api-access-5qn77:
    Type:                    Projected (a volume that contains injected data from multiple sources)
    TokenExpirationSeconds:  3607
    ConfigMapName:           kube-root-ca.crt
    Optional:                false
    DownwardAPI:             true
QoS Class:                   Burstable
Node-Selectors:              doks.digitalocean.com/node-pool=data-pool-e0y1e6npd
Tolerations:                 node.kubernetes.io/not-ready:NoExecute op=Exists for 300s
                             node.kubernetes.io/unreachable:NoExecute op=Exists for 300s
                             workload=data:NoSchedule
Events:
  Type    Reason     Age   From               Message
  ----    ------     ----  ----               -------
  Normal  Scheduled  27m   default-scheduler  Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-gx8c6 to data-pool-e0y1e6npd-l3xcq
  Normal  Pulling    27m   kubelet            Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-********-173657"
  Normal  Pulled     27m   kubelet            Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-********-173657" in 9.773s (9.773s including waiting). Image size: 1339258566 bytes.
  Normal  Created    27m   kubelet            Created container: wait-gcs-ready
  Normal  Started    27m   kubelet            Started container wait-gcs-ready
  Normal  Pulling    26m   kubelet            Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-********-173657"
  Normal  Pulled     26m   kubelet            Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-********-173657" in 1.943s (1.943s including waiting). Image size: 1339258566 bytes.
  Normal  Created    26m   kubelet            Created container: ray-worker
  Normal  Started    26m   kubelet            Started container ray-worker
