LAST SEEN   TYPE      REASON                    OBJECT                                                 MESSAGE
11m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cloudprovider.kubernetes.io/uninitialized: true}, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match <PERSON><PERSON>'s node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
11m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-55rxx   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-55rxx to data-pool-e0y1e6npd-l3gdo
13m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-przmc   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-przmc to data-pool-e0y1e6npd-l3xcq
13m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   0/13 nodes are available: 1 Insufficient cpu, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/13 nodes are available: 11 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
11m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
11m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cilium.io/agent-not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
24m         Normal    Scheduled                 pod/terrafloww-processing-engine-b4bb546df-ldlk6       Successfully assigned terrafloww-platform/terrafloww-processing-engine-b4bb546df-ldlk6 to data-pool-e0y1e6npd-l3xcq
11m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-bgjbx to data-pool-e0y1e6npd-l3xcq
11m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cilium.io/agent-not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
11m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
11m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cloudprovider.kubernetes.io/uninitialized: true}, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
11m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-vb7g2 to data-pool-e0y1e6npd-l3gdo
13m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   0/13 nodes are available: 1 Insufficient cpu, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/13 nodes are available: 11 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
11m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-55rxx   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cilium.io/agent-not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
11m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-55rxx   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
11m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-55rxx   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cloudprovider.kubernetes.io/uninitialized: true}, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
27m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-gx8c6   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-gx8c6 to data-pool-e0y1e6npd-l3xcq
13m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-55rxx   0/13 nodes are available: 1 Insufficient cpu, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/13 nodes are available: 11 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
21m         Normal    Scheduled                 pod/terrafloww-processing-engine-5974d9c9c8-nfqpg      Successfully assigned terrafloww-platform/terrafloww-processing-engine-5974d9c9c8-nfqpg to data-pool-e0y1e6npd-l3xcq
27m         Normal    Scheduled                 pod/terrafloww-ray-cluster-head-hhftc                  Successfully assigned terrafloww-platform/terrafloww-ray-cluster-head-hhftc to data-pool-e0y1e6npd-t0isx
27m         Normal    Killing                   pod/terrafloww-ray-cluster-head-2lndk                  Stopping container ray-head
27m         Normal    Killing                   pod/terrafloww-ray-cluster-head-2lndk                  Stopping container autoscaler
27m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-gp5jp   Stopping container ray-worker
27m         Normal    CreatedServiceAccount     raycluster/terrafloww-ray-cluster                      Created service account terrafloww-platform/terrafloww-ray-cluster
27m         Normal    CreatedRole               raycluster/terrafloww-ray-cluster                      Created role terrafloww-platform/terrafloww-ray-cluster
27m         Normal    CreatedRoleBinding        raycluster/terrafloww-ray-cluster                      Created role binding terrafloww-platform/terrafloww-ray-cluster
27m         Normal    CreatedService            raycluster/terrafloww-ray-cluster                      Created service terrafloww-platform/terrafloww-ray-cluster-head-svc
27m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-head-2lndk                  Readiness probe failed:
27m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-gp5jp   Readiness probe failed:
27m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-gp5jp; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 08:21:01 +0000 UTC,FinishedAt:2025-06-26 12:08:34 +0000 UTC,ContainerID:containerd://2443df7283962b6681c8d52846b2fa74c55041b0b22947cf4990868c8dc26629,}
27m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-gx8c6
27m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-gx8c6   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657"
27m         Normal    CreatedHeadPod            raycluster/terrafloww-ray-cluster                      Created head Pod terrafloww-platform/terrafloww-ray-cluster-head-hhftc
27m         Normal    DeletedHeadPod            raycluster/terrafloww-ray-cluster                      Deleted head Pod terrafloww-platform/terrafloww-ray-cluster-head-2lndk; Pod status: Failed; Pod restart policy: Always; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-24 10:02:17 +0000 UTC,FinishedAt:2025-06-26 12:08:35 +0000 UTC,ContainerID:containerd://51a51458589b69ba66bc5f96951e4c560a359e7d6c414faaab0c155abf42494e,}
27m         Normal    Pulling                   pod/terrafloww-ray-cluster-head-hhftc                  Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657"
27m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-gx8c6   Created container: wait-gcs-ready
27m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-gx8c6   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" in 9.773s (9.773s including waiting). Image size: ********** bytes.
27m         Normal    Pulled                    pod/terrafloww-ray-cluster-head-hhftc                  Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" in 10.223s (10.223s including waiting). Image size: ********** bytes.
27m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-gx8c6   Started container wait-gcs-ready
27m         Normal    Pulled                    pod/terrafloww-ray-cluster-head-hhftc                  Container image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" already present on machine
27m         Normal    Started                   pod/terrafloww-ray-cluster-head-hhftc                  Started container ray-head
27m         Normal    Started                   pod/terrafloww-ray-cluster-head-hhftc                  Started container autoscaler
27m         Normal    Created                   pod/terrafloww-ray-cluster-head-hhftc                  Created container: autoscaler
27m         Normal    Created                   pod/terrafloww-ray-cluster-head-hhftc                  Created container: ray-head
27m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-head-hhftc                  Readiness probe failed:
26m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-gx8c6   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657"
26m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-gx8c6   Started container ray-worker
26m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-gx8c6   Created container: ray-worker
26m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-gx8c6   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" in 1.943s (1.943s including waiting). Image size: ********** bytes.
24m         Normal    ScalingReplicaSet         deployment/terrafloww-processing-engine                Scaled up replica set terrafloww-processing-engine-b4bb546df from 0 to 1
24m         Normal    SuccessfulCreate          replicaset/terrafloww-processing-engine-b4bb546df      Created pod: terrafloww-processing-engine-b4bb546df-ldlk6
24m         Normal    Pulling                   pod/terrafloww-processing-engine-b4bb546df-ldlk6       Pulling image "registry.digitalocean.com/terrafloww-dev/processing-engine:e2e3ec5-20250626-173657"
24m         Normal    Created                   pod/terrafloww-processing-engine-b4bb546df-ldlk6       Created container: processing-engine
24m         Normal    Pulled                    pod/terrafloww-processing-engine-b4bb546df-ldlk6       Successfully pulled image "registry.digitalocean.com/terrafloww-dev/processing-engine:e2e3ec5-20250626-173657" in 16.778s (16.778s including waiting). Image size: 657921819 bytes.
24m         Normal    Started                   pod/terrafloww-processing-engine-b4bb546df-ldlk6       Started container processing-engine
24m         Normal    Killing                   pod/terrafloww-processing-engine-d677dccb-m9hzs        Stopping container processing-engine
24m         Normal    SuccessfulDelete          replicaset/terrafloww-processing-engine-d677dccb       Deleted pod: terrafloww-processing-engine-d677dccb-m9hzs
24m         Normal    ScalingReplicaSet         deployment/terrafloww-processing-engine                Scaled down replica set terrafloww-processing-engine-d677dccb from 1 to 0
21m         Normal    ScalingReplicaSet         deployment/terrafloww-processing-engine                Scaled up replica set terrafloww-processing-engine-5974d9c9c8 from 0 to 1
21m         Normal    Pulling                   pod/terrafloww-processing-engine-5974d9c9c8-nfqpg      Pulling image "registry.digitalocean.com/terrafloww-dev/processing-engine:e2e3ec5-20250626-173657"
21m         Normal    SuccessfulCreate          replicaset/terrafloww-processing-engine-5974d9c9c8     Created pod: terrafloww-processing-engine-5974d9c9c8-nfqpg
21m         Normal    Pulled                    pod/terrafloww-processing-engine-5974d9c9c8-nfqpg      Successfully pulled image "registry.digitalocean.com/terrafloww-dev/processing-engine:e2e3ec5-20250626-173657" in 793ms (793ms including waiting). Image size: 657921819 bytes.
21m         Normal    Created                   pod/terrafloww-processing-engine-5974d9c9c8-nfqpg      Created container: processing-engine
21m         Normal    Started                   pod/terrafloww-processing-engine-5974d9c9c8-nfqpg      Started container processing-engine
21m         Normal    ScalingReplicaSet         deployment/terrafloww-processing-engine                Scaled down replica set terrafloww-processing-engine-b4bb546df from 1 to 0
21m         Normal    SuccessfulDelete          replicaset/terrafloww-processing-engine-b4bb546df      Deleted pod: terrafloww-processing-engine-b4bb546df-ldlk6
21m         Normal    Killing                   pod/terrafloww-processing-engine-b4bb546df-ldlk6       Stopping container processing-engine
13m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-55rxx
13m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-vb7g2
13m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-bgjbx
13m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-przmc
13m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-przmc   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657"
13m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-przmc   Created container: wait-gcs-ready
13m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-przmc   Started container wait-gcs-ready
13m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-przmc   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" in 931ms (931ms including waiting). Image size: ********** bytes.
13m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-przmc   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657"
13m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-przmc   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" in 755ms (755ms including waiting). Image size: ********** bytes.
13m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-przmc   Created container: ray-worker
13m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-przmc   Started container ray-worker
13m         Normal    TriggeredScaleUp          pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   pod triggered scale-up: [{916f3b54-4b2b-4165-8a5c-96f060b1f76f 2->3 (max: 3)}]
13m         Normal    TriggeredScaleUp          pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   pod triggered scale-up: [{916f3b54-4b2b-4165-8a5c-96f060b1f76f 2->3 (max: 3)}]
12m         Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-55rxx   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
11m         Normal    UpdatedLoadBalancer       service/terrafloww-processing-engine-lb                Updated load balancer with new hosts
11m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-przmc   Stopping container ray-worker
11m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-przmc
11m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-przmc   Readiness probe failed:
11m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657"
11m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-55rxx   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657"
11m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657"
11m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" in 679ms (679ms including waiting). Image size: ********** bytes.
11m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   Created container: wait-gcs-ready
11m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   Started container wait-gcs-ready
10m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657"
10m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" in 713ms (713ms including waiting). Image size: ********** bytes.
10m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   Created container: ray-worker
10m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   Started container ray-worker
10m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-55rxx   Started container wait-gcs-ready
10m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" in 58.742s (58.742s including waiting). Image size: ********** bytes.
10m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   Created container: wait-gcs-ready
10m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   Started container wait-gcs-ready
10m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-55rxx   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" in 58.775s (58.775s including waiting). Image size: ********** bytes.
10m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-55rxx   Created container: wait-gcs-ready
9m49s       Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" in 751ms (751ms including waiting). Image size: ********** bytes.
9m49s       Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-55rxx   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657" in 753ms (753ms including waiting). Image size: ********** bytes.
9m49s       Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657"
9m49s       Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-55rxx   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-20250626-173657"
9m48s       Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   Created container: ray-worker
9m48s       Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-55rxx   Created container: ray-worker
9m48s       Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-55rxx   Started container ray-worker
9m48s       Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   Started container ray-worker
9m38s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   Stopping container ray-worker
9m37s       Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-bgjbx   Readiness probe failed:
9m34s       Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-bgjbx; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 12:25:18 +0000 UTC,FinishedAt:2025-06-26 12:26:27 +0000 UTC,ContainerID:containerd://2f9c3dbe333634c81cf2f9a836c96f515fd25079de5d312e8010faa401dce324,}, pods "terrafloww-ray-cluster-worker-group-worker-bgjbx" not found
8m42s       Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   Readiness probe failed:
8m42s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-vb7g2   Stopping container ray-worker
8m42s       Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-55rxx   Readiness probe failed:
8m42s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-55rxx   Stopping container ray-worker
8m38s       Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-55rxx; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 12:26:13 +0000 UTC,FinishedAt:2025-06-26 12:27:22 +0000 UTC,ContainerID:containerd://a0297c5855160235b66ad3df792d6363f78e23a79eedccfaf721412abcc09b50,}, pods "terrafloww-ray-cluster-worker-group-worker-55rxx" not found
