Name:             terrafloww-ray-cluster-head-hhftc
Namespace:        terrafloww-platform
Priority:         0
Service Account:  terrafloww-ray-cluster
Node:             data-pool-e0y1e6npd-t0isx/10.122.0.15
Start Time:       Thu, 26 Jun 2025 17:38:36 +0530
Labels:           app=terrafloww
                  app.kubernetes.io/created-by=kuberay-operator
                  app.kubernetes.io/name=kuberay
                  component=ray-head
                  ray.io/cluster=terrafloww-ray-cluster
                  ray.io/group=headgroup
                  ray.io/identifier=terrafloww-ray-cluster-head
                  ray.io/is-ray-node=yes
                  ray.io/node-type=head
Annotations:      ray.io/ft-enabled: false
Status:           Running
IP:               ************
IPs:
  IP:           ************
Controlled By:  RayCluster/terrafloww-ray-cluster
Containers:
  ray-head:
    Container ID:  containerd://e0aa6fdd25b60d24746eb1e0285c29ebd4d3e272d7335a82bb2c4c60c7d18e97
    Image:         registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-********-173657
    Image ID:      registry.digitalocean.com/terrafloww-dev/ray-custom@sha256:****************************************************************
    Ports:         6379/TCP, 8265/TCP, 10001/TCP, 8080/TCP
    Host Ports:    0/TCP, 0/TCP, 0/TCP, 0/TCP
    Command:
      /bin/bash
      -lc
      --
    Args:
      ulimit -n 65536; ray start --head  --block  --dashboard-agent-listen-port=52365  --dashboard-host=0.0.0.0  --dashboard-port=8265  --memory=**********  --metrics-export-port=8080  --no-monitor  --num-cpus=0  --object-store-memory=********* 
    State:          Running
      Started:      Thu, 26 Jun 2025 17:38:47 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     2
      memory:  6Gi
    Requests:
      cpu:      1
      memory:   4Gi
    Liveness:   exec [bash -c wget -T 2 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success && wget -T 10 -q -O- http://localhost:8265/api/gcs_healthz | grep success] delay=30s timeout=5s period=5s #success=1 #failure=120
    Readiness:  exec [bash -c wget -T 4 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success && wget -T 10 -q -O- http://localhost:8265/api/gcs_healthz | grep success] delay=0s timeout=10s period=5s #success=1 #failure=12
    Environment:
      RAY_DISABLE_IMPORT_WARNING:                1
      RAY_DEDUP_LOGS:                            0
      RAY_DASHBOARD_METRICS_COLLECTION_ENABLED:  0
      RAY_USAGE_STATS_ENABLED:                   0
      TFW_USE_HTTP_POOL:                         false
      FLIGHT_HOST:                               ************
      FLIGHT_PORT:                               50052
      RAY_CLUSTER_NAME:                           (v1:metadata.labels['ray.io/cluster'])
      RAY_CLOUD_INSTANCE_ID:                     terrafloww-ray-cluster-head-hhftc (v1:metadata.name)
      RAY_NODE_TYPE_NAME:                         (v1:metadata.labels['ray.io/group'])
      KUBERAY_GEN_RAY_START_CMD:                 ray start --head  --block  --dashboard-agent-listen-port=52365  --dashboard-host=0.0.0.0  --dashboard-port=8265  --memory=**********  --metrics-export-port=8080  --no-monitor  --num-cpus=0  --object-store-memory=********* 
      RAY_PORT:                                  6379
      RAY_ADDRESS:                               127.0.0.1:6379
      RAY_USAGE_STATS_KUBERAY_IN_USE:            1
      RAY_USAGE_STATS_EXTRA_TAGS:                kuberay_version=v1.3.2;kuberay_crd=RayCluster
      RAY_DASHBOARD_ENABLE_K8S_DISK_USAGE:       1
    Mounts:
      /dev/shm from shared-mem (rw)
      /tmp/ray from ray-logs (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-msdf2 (ro)
  autoscaler:
    Container ID:  containerd://d4ffa6763d6e53588e572817c67b734e3faea75d0e4361c8df742ab8a8f34298
    Image:         registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-********-173657
    Image ID:      registry.digitalocean.com/terrafloww-dev/ray-custom@sha256:****************************************************************
    Port:          <none>
    Host Port:     <none>
    Command:
      /bin/bash
      -lc
      --
    Args:
      ray kuberay-autoscaler --cluster-name $(RAY_CLUSTER_NAME) --cluster-namespace $(RAY_CLUSTER_NAMESPACE)
    State:          Running
      Started:      Thu, 26 Jun 2025 17:38:47 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     500m
      memory:  512Mi
    Requests:
      cpu:     500m
      memory:  512Mi
    Environment:
      RAY_CLUSTER_NAME:        (v1:metadata.labels['ray.io/cluster'])
      RAY_CLUSTER_NAMESPACE:  terrafloww-platform (v1:metadata.namespace)
      RAY_HEAD_POD_NAME:      terrafloww-ray-cluster-head-hhftc (v1:metadata.name)
      KUBERAY_CRD_VER:        v1
    Mounts:
      /tmp/ray from ray-logs (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-msdf2 (ro)
Conditions:
  Type                        Status
  PodReadyToStartContainers   True 
  Initialized                 True 
  Ready                       True 
  ContainersReady             True 
  PodScheduled                True 
Volumes:
  ray-logs:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     
    SizeLimit:  <unset>
  shared-mem:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     Memory
    SizeLimit:  4Gi
  kube-api-access-msdf2:
    Type:                    Projected (a volume that contains injected data from multiple sources)
    TokenExpirationSeconds:  3607
    ConfigMapName:           kube-root-ca.crt
    Optional:                false
    DownwardAPI:             true
QoS Class:                   Burstable
Node-Selectors:              doks.digitalocean.com/node-pool=data-pool-e0y1e6npd
Tolerations:                 node.kubernetes.io/not-ready:NoExecute op=Exists for 300s
                             node.kubernetes.io/unreachable:NoExecute op=Exists for 300s
                             workload=data:NoSchedule
Events:
  Type     Reason     Age                From               Message
  ----     ------     ----               ----               -------
  Normal   Scheduled  27m                default-scheduler  Successfully assigned terrafloww-platform/terrafloww-ray-cluster-head-hhftc to data-pool-e0y1e6npd-t0isx
  Normal   Pulling    27m                kubelet            Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-********-173657"
  Normal   Pulled     27m                kubelet            Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-********-173657" in 10.223s (10.223s including waiting). Image size: 1339258566 bytes.
  Normal   Created    27m                kubelet            Created container: ray-head
  Normal   Started    27m                kubelet            Started container ray-head
  Normal   Pulled     27m                kubelet            Container image "registry.digitalocean.com/terrafloww-dev/ray-custom:e2e3ec5-********-173657" already present on machine
  Normal   Created    27m                kubelet            Created container: autoscaler
  Normal   Started    27m                kubelet            Started container autoscaler
  Warning  Unhealthy  26m (x4 over 27m)  kubelet            Readiness probe failed:
