# tile_processor.py
import asyncio
import logging
from typing import Dict, List, Any, Tu<PERSON>, Optional
import httpx
import numpy as np
from collections import defaultdict

from terrafloww.libs.rasteret_core import fetch as rasteret_fetch
from terrafloww.compute.ray_jobs.preprocess.band_utils import normalize_band_name

logger = logging.getLogger(__name__)

async def identify_unique_tiles(batch_data: Dict, chip_indices: List[int] = None) -> Dict[Tuple, Dict]:
    """
    Identifies all unique tiles needed for processing the specified chips.
    
    Args:
        batch_data: Batch data dictionary
        chip_indices: Optional list of specific chip indices to process
        
    Returns:
        Dictionary of unique tile fetch requests
    """
    if chip_indices is None:
        chip_indices = range(len(batch_data.get('chip_cx', [])))
    
    unique_tile_fetches = {}  # Key: (url, offset, size), Value: fetch_info_dict
    
    for i in chip_indices:
        if i >= len(batch_data.get('required_input_tiles', [])):
            continue
            
        required_tiles = batch_data['required_input_tiles'][i]
        for band, tile_fetch_infos in required_tiles.items():
            # Normalize band name to ensure consistent mapping
            std_band, asset_key = normalize_band_name(band)
            
            for fetch_info in tile_fetch_infos:
                tile_key = (fetch_info['url'], fetch_info['offset'], fetch_info['size'])
                if tile_key not in unique_tile_fetches:
                    fetch_info_copy = fetch_info.copy()
                    # Store both standard name and asset key for better error reporting
                    fetch_info_copy['band'] = std_band
                    fetch_info_copy['asset_key'] = asset_key
                    unique_tile_fetches[tile_key] = fetch_info_copy
    
    return unique_tile_fetches

async def fetch_unique_tiles(
    client: httpx.AsyncClient,
    unique_tile_fetches: Dict[Tuple, Dict],
    max_concurrent: int = 20
) -> Dict[Tuple, np.ndarray]:
    """
    Fetches all unique tiles concurrently with optimized error handling and
    groups requests by common URL and parameters.
    
    Args:
        client: httpx.AsyncClient instance
        unique_tile_fetches: Dictionary of unique tile fetch requests
        max_concurrent: Maximum concurrent fetches
        
    Returns:
        Dictionary mapping (band, row, col) tuples to decoded tile arrays
    """
    if not unique_tile_fetches:
        logger.warning("No unique tiles to fetch")
        return {}
    
    # Group fetch requests by common parameters
    grouped_fetches = defaultdict(list)
    for key, info in unique_tile_fetches.items():
        # Create a key for grouping: (url, tile_shape, dtype, predictor)
        fetch_params_key = (
            info['url'], 
            (info.get('expected_shape', (512, 512))),
            info.get('dtype_str', 'uint16'),
            info.get('predictor', 1)
        )
        grouped_fetches[fetch_params_key].append(info)
    
    # Execute fetches for each group
    fetch_tasks = []
    fetch_keys = []
    
    for params_key, requests_for_group in grouped_fetches.items():
        url, tile_shape, dtype_str, predictor = params_key
        
        # Get band info from first request for logging
        band_name = requests_for_group[0].get('band', 'unknown')
        asset_key = requests_for_group[0].get('asset_key', band_name)
        
        logger.info(f"Fetching {len(requests_for_group)} tiles for {band_name} "
                   f"(band {asset_key}) from {url}")
        
        # Create fetch task
        fetch_task = rasteret_fetch.fetch_tiles(
            client=client,
            url=url,
            tile_requests=[{
                'offset': r['offset'],
                'size': r['size'],
                'expected_shape': r.get('expected_shape', tile_shape),
                'tile_info': f"r{r.get('tile_r', '?')}_c{r.get('tile_c', '?')}"
            } for r in requests_for_group],
            tile_shape=tile_shape,
            dtype_str=dtype_str,
            predictor=predictor,
            band_name=asset_key,
            max_concurrent=max_concurrent
        )
        
        fetch_tasks.append(fetch_task)
        fetch_keys.append((params_key, requests_for_group))
    
    # Execute all fetches concurrently
    fetched_results = []
    if fetch_tasks:
        fetched_results = await asyncio.gather(*fetch_tasks, return_exceptions=True)
        logger.info(f"Fetched tile data for {len(fetch_tasks)} groups")
    
    # Process results and organize tiles
    decoded_tiles = {}  # Key: (band, r, c), Value: np.ndarray
    
    for idx, group_result in enumerate(fetched_results):
        if idx >= len(fetch_keys):
            logger.error(f"Fetch result index {idx} is out of range")
            continue
            
        params_key, original_requests = fetch_keys[idx]
        
        # Handle exceptions
        if isinstance(group_result, Exception):
            logger.error(f"Fetch group {idx} failed with exception: {group_result}")
            continue
            
        url, _, _, _ = params_key
        
        if group_result is None:
            logger.warning(f"Failed to fetch tile group for url {url}")
            continue
            
        # Map results back to tile coordinates
        for i, tile_array in enumerate(group_result):
            if i >= len(original_requests):
                logger.warning(f"Received more results ({len(group_result)}) than requests ({len(original_requests)})")
                continue
                
            req = original_requests[i]
            band = req.get('band')
            tile_r = req.get('tile_r')
            tile_c = req.get('tile_c')
            
            if band is not None and tile_r is not None and tile_c is not None:
                tile_coord = (band, tile_r, tile_c)
                decoded_tiles[tile_coord] = tile_array
            elif tile_array is not None:
                logger.warning(f"Found decoded tile but couldn't identify band/coords: "
                              f"url={url}, offset={req['offset']}")
    
    logger.info(f"Organized {len(decoded_tiles)} decoded tiles")
    return decoded_tiles

async def assemble_chip_bands(
    chip_item: Dict[str, Any],
    decoded_tiles: Dict[Tuple, np.ndarray],
    dtype: str = 'float32'
) -> Dict[str, np.ndarray]:
    """
    Assembles all available bands for a chip from decoded tiles.
    
    Args:
        chip_item: Dictionary with chip metadata
        decoded_tiles: Dictionary of decoded tile arrays
        dtype: Data type for assembled chip arrays
        
    Returns:
        Dictionary mapping band names to assembled chip arrays
    """
    assembled_band_chips = {}
    
    # Get chip metadata
    required_input_tiles = chip_item['required_input_tiles']
    chip_start_col_abs = chip_item['chip_cx'] * chip_item['chip_size']
    chip_start_row_abs = chip_item['chip_cy'] * chip_item['chip_size']
    output_chip_shape = chip_item['output_chip_shape']
    grid_def = chip_item['grid_definition']
    
    # Process each band
    for band, tile_infos in required_input_tiles.items():
        # Create empty array for this band's chip with NaN as default
        band_chip_array = np.full(output_chip_shape, np.nan, dtype=dtype)
        
        for tile_info in tile_infos:
            tile_coord = (band, tile_info['tile_r'], tile_info['tile_c'])
            
            if tile_coord not in decoded_tiles:
                logger.debug(f"Missing tile {tile_coord} for band {band}. Using nodata values.")
                continue
                
            full_tile_data = decoded_tiles[tile_coord]
            if full_tile_data is None:
                logger.warning(f"Null data for tile {tile_coord}, band {band}. Skipping.")
                continue
                
            try:
               # Get the window information
               window_col_off = tile_info['window_col_off']
               window_row_off = tile_info['window_row_off']
               window_width = tile_info['window_width']
               window_height = tile_info['window_height']
               
               # Extract window from the full tile
               window_data = full_tile_data[window_row_off:window_row_off+window_height, 
                                           window_col_off:window_col_off+window_width]
               
               # Calculate the position in the chip array
               dest_col_offset = max(0, tile_info['tile_c'] * grid_def['tile_width'] - chip_start_col_abs)
               dest_row_offset = max(0, tile_info['tile_r'] * grid_def['tile_height'] - chip_start_row_abs)
               
               # Copy the window data into the chip array
               # Handle potential shape mismatches
               src_height, src_width = window_data.shape
               dest_height = min(src_height, band_chip_array.shape[0] - dest_row_offset)
               dest_width = min(src_width, band_chip_array.shape[1] - dest_col_offset)
               
               if dest_height > 0 and dest_width > 0:
                   band_chip_array[dest_row_offset:dest_row_offset+dest_height, 
                                  dest_col_offset:dest_col_offset+dest_width] = \
                       window_data[:dest_height, :dest_width]
               
            except Exception as e:
               logger.warning(f"Error processing tile {tile_coord} for band {band}: {e}")
               # Continue with next tile
       
        # Store the assembled band
        assembled_band_chips[band] = band_chip_array
   
    return assembled_band_chips