# sdk.py
import asyncio
from typing import Dict, List, Optional, Union, Tuple, Any
import json
import logging
import os

from terrafloww.libs.metadata_client.client import (
    MetadataClient, 
    JobStatusUpdatePayload, 
    RegisterJobPayload,
    DatasetDefinitionQuery
)

logger = logging.getLogger(__name__)

# Default configuration
DEFAULT_METADATA_SERVICE_URL = os.environ.get("METADATA_SERVICE_URL", "http://localhost:8080")
DEFAULT_FLIGHT_SERVICE_URL = os.environ.get("FLIGHT_SERVICE_URL", "grpc://localhost:8081")

class IndexCalculationBuilder:
    """
    Builder for defining spectral index calculation operations.
    
    Example:
        calculation = IndexCalculationBuilder()\
            .input_dataset("sentinel2-l2a", version=5, bands=["red", "nir"])\
            .chip_size(128)\
            .calculate("ndvi", output_dtype="int16")\
            .output("ndvi_output")
            
        dataset_handle = await calculation.compute()
    """
    
    def __init__(self, metadata_url: str = DEFAULT_METADATA_SERVICE_URL):
        self.metadata_url = metadata_url
        self.input_datasets = {}
        self._chip_size = 256
        self._calculation = "ndvi"
        self._calculation_params = {}
        self._output_name = None
        self._output_type = "derived_chips"
        self._output_dtype = "int16"
    
    def input_dataset(
        self, 
        name: str, 
        version: Union[int, str] = "latest", 
        bands: Optional[List[str]] = None, 
        band_map: Optional[Dict[str, str]] = None
    ) -> "IndexCalculationBuilder":
        """
        Add an input dataset.
        
        Args:
            name: Dataset name
            version: Dataset version (int or "latest")
            bands: List of bands to use (e.g., ["red", "nir"])
            band_map: Optional mapping of standard names to dataset bands
                      (e.g., {"red": "B04", "nir": "B08"})
                      
        Returns:
            Self for chaining
        """
        if band_map:
            # Use explicit mapping
            for std_name, asset_name in band_map.items():
                self.input_datasets[std_name] = (name, version, asset_name)
        elif bands:
            # Assume band names match
            for band in bands:
                self.input_datasets[band] = (name, version, band)
        else:
            # Will be resolved later based on calculation needs
            self.input_datasets["__dataset__"] = (name, version, None)
            
        return self
    
    def chip_size(self, size: int) -> "IndexCalculationBuilder":
        """Set output chip size in pixels."""
        self._chip_size = size
        return self
    
    def calculate(
        self, 
        index: str, 
        output_dtype: str = "int16", 
        **kwargs
    ) -> "IndexCalculationBuilder":
        """
        Set the spectral index to calculate.
        
        Args:
            index: Index name ('ndvi', 'ndwi', 'normalized_diff', etc.)
            output_dtype: Output data type ('int16', 'float32', etc.)
            **kwargs: Additional calculation parameters
            
        Returns:
            Self for chaining
        """
        self._calculation = index
        self._output_dtype = output_dtype
        self._calculation_params = kwargs
        return self
    
    def output(self, name: str, output_type: str = "derived_chips") -> "IndexCalculationBuilder":
        """Set the output dataset name and type."""
        self._output_name = name
        self._output_type = output_type
        return self
    
    async def _resolve_input_datasets(self) -> Dict[str, Tuple[str, Union[str, int]]]:
        """
        Resolve input datasets based on calculation needs.
        
        Returns:
            Dictionary mapping band names to (dataset_name, version) tuples
        """
        # Handle case where dataset was specified without bands
        if "__dataset__" in self.input_datasets:
            ds_name, ds_version, _ = self.input_datasets["__dataset__"]
            
            # Connect to metadata service
            async with MetadataClient(self.metadata_url) as client:
                # Get dataset info to discover available bands
                info = await client.get_dataset_info(ds_name, ds_version)
                
                if not info:
                    raise ValueError(f"Dataset {ds_name} v{ds_version} not found")
                    
                if info.dataset_type != "raw_cog":
                    raise ValueError(f"Dataset {ds_name} v{ds_version} is not of type 'raw_cog'")
                
                # Get available assets
                asset_urls = info.raw_asset_urls or {}
                available_assets = list(asset_urls.keys())
                
                # Try to map standard names to assets based on known patterns
                if self._calculation.lower() == "ndvi":
                    # Look for red and NIR bands
                    red_candidates = [a for a in available_assets if a in ["red", "RED", "B04", "SR_B4"]]
                    nir_candidates = [a for a in available_assets if a in ["nir", "NIR", "B08", "SR_B5"]]
                    
                    if red_candidates and nir_candidates:
                        return {
                            "red": (ds_name, ds_version),
                            "nir": (ds_name, ds_version)
                        }
                        
                # Fallback - use all available assets
                return {asset: (ds_name, ds_version) for asset in available_assets}
        
        # Normal case - use explicitly specified bands
        return {band: (ds[0], ds[1]) for band, ds in self.input_datasets.items() if ds[2] is None}
    
    async def check_if_exists(self) -> Dict[str, Any]:
        """
        Check if a dataset with the same inputs and parameters already exists.
        
        Returns:
            Dictionary with {'exists': bool, 'dataset_id': str, ...}
        """
        if not self._output_name:
            raise ValueError("Output name must be set before checking existence")
            
        # Resolve input datasets
        input_ds_map = await self._resolve_input_datasets()
        
        # Prepare query
        params = {
            "chip_size": str(self._chip_size),
            "calculation": self._calculation,
            "output_dtype": self._output_dtype,
            **self._calculation_params
        }
        
        # Convert dataset tuples to version map
        source_versions = {}
        
        async with MetadataClient(self.metadata_url) as client:
            # Resolve latest versions if needed
            for band, (ds_name, ds_version) in input_ds_map.items():
                if ds_version == "latest":
                    # Get latest version
                    info = await client.get_dataset_info(ds_name, "latest")
                    if info:
                        source_versions[info.dataset_id] = info.version
                else:
                    # Get dataset ID for the specific version
                    info = await client.get_dataset_info(ds_name, ds_version)
                    if info:
                        source_versions[info.dataset_id] = info.version
            
            # Create query
            query = DatasetDefinitionQuery(
                source_artifact_versions=source_versions,
                parameters=params
            )
            
            # Execute query
            result = await client.find_dataset_by_definition(query)
            return result if result else {"exists": False}
    
    async def compute(self, wait_for_completion: bool = True) -> Dict[str, Any]:
        """
        Execute the calculation and return a handle to the result.
        
        Args:
            wait_for_completion: Whether to wait for the job to complete
            
        Returns:
            Dictionary with dataset handle information
        """
        if not self._output_name:
            raise ValueError("Output name must be set before computing")
            
        # Resolve input datasets
        input_ds_map = await self._resolve_input_datasets()
        
        # Check if dataset already exists
        exists_result = await self.check_if_exists()
        
        if exists_result and exists_result.get("exists"):
            logger.info(f"Dataset already exists: {exists_result}")
            return {
                "dataset_id": exists_result.get("dataset_id"),
                "name": exists_result.get("name") or self._output_name,
                "version": exists_result.get("version"),
                "status": "exists"
            }
        
        # Register a job to compute the result
        async with MetadataClient(self.metadata_url) as client:
            # Resolve latest versions and get dataset IDs
            source_artifact_versions = {}
            
            for band, (ds_name, ds_version) in input_ds_map.items():
                # Get dataset info
                info = await client.get_dataset_info(ds_name, ds_version)
                
                if not info:
                    raise ValueError(f"Dataset {ds_name} v{ds_version} not found")
                    
                source_artifact_versions[info.dataset_id] = info.version
                
            # Prepare job parameters
            job_params = {
                "chip_size": str(self._chip_size),
                "output_name": self._output_name,
                "output_type": self._output_type,
                "output_dtype": self._output_dtype,
                "calculation": self._calculation,
                "input_datasets": json.dumps({
                    band: [ds_name, str(ds_version)] 
                    for band, (ds_name, ds_version) in input_ds_map.items()
                })
            }
            
            # Add any additional calculation parameters
            if self._calculation_params:
                job_params["calculation_params"] = json.dumps(self._calculation_params)
                
            # Register the job
            job_payload = RegisterJobPayload(
                job_type="index_calculation",
                input_artifact_versions=source_artifact_versions,
                parameters=job_params
            )
            
            job_response = await client.register_job(job_payload)
            job_id = job_response.job_id
            
            logger.info(f"Registered job {job_id} for {self._calculation} calculation")
            
            # Wait for job completion if requested
            if wait_for_completion:
                return await self._wait_for_job_completion(job_id)
            else:
                return {
                    "job_id": job_id,
                    "status": "submitted"
                }
    
    async def _wait_for_job_completion(self, job_id: str, poll_interval_seconds: int = 5) -> Dict[str, Any]:
        """
        Wait for a job to complete and return the dataset handle.
        
        Args:
            job_id: Job ID to wait for
            poll_interval_seconds: How often to check job status
            
        Returns:
            Dictionary with dataset handle information
        """
        async with MetadataClient(self.metadata_url) as client:
            while True:
                # Get job status
                job_info = await client.get_job_info(job_id)
                
                if not job_info:
                    raise ValueError(f"Job {job_id} not found")
                    
                status = job_info.status
                
                if status == "completed":
                    # Job completed successfully
                    output_dataset_id = job_info.output_dataset_id
                    
                    if not output_dataset_id:
                        raise ValueError(f"Job {job_id} completed but no output dataset ID")
                        
                    # Get dataset info
                    dataset_info = await client.get_dataset_info(self._output_name, "latest")
                    
                    if not dataset_info:
                        raise ValueError(f"Output dataset {self._output_name} not found")
                        
                    return {
                        "dataset_id": output_dataset_id,
                        "name": self._output_name,
                        "version": dataset_info.version,
                        "status": "completed"
                    }
                    
                elif status == "failed":
                    # Job failed
                    error = job_info.error_message
                    raise RuntimeError(f"Job {job_id} failed: {error}")
                    
                # Job still running, wait and check again
                await asyncio.sleep(poll_interval_seconds)


class GeoSpatialDataLoader:
    """
    DataLoader for accessing datasets from the platform.
    
    Handles both Flight Service access and direct S3 access.
    """
    
    def __init__(
        self, 
        dataset_handle: Dict[str, Any],
        metadata_url: str = DEFAULT_METADATA_SERVICE_URL,
        flight_url: str = DEFAULT_FLIGHT_SERVICE_URL,
        direct_access: bool = False
    ):
        self.dataset_handle = dataset_handle
        self.metadata_url = metadata_url
        self.flight_url = flight_url
        self.direct_access = direct_access
        
    async def load(self):
        """
        Load the dataset.
        
        Returns:
            PyArrow Dataset or Dataset Reader depending on access mode
        """
        # Will implement either Flight Service access or direct S3 access
        # For now, just return a placeholder
        if self.direct_access:
            return await self._load_direct()
        else:
            return await self._load_via_flight()
    
    async def _load_via_flight(self):
        """Load dataset via Flight Service."""
        raise NotImplementedError("Flight Service loading not implemented yet")
    
    async def _load_direct(self):
        """Load dataset directly from storage."""
        raise NotImplementedError("Direct loading not implemented yet")