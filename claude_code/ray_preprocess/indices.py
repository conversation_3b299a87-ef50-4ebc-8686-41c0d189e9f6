# indices.py
from typing import Dict, Callable, Tuple, Optional, List, Any
import numpy as np
import logging

logger = logging.getLogger(__name__)

# Type for index calculation functions
IndexCalculationFn = Callable[[Dict[str, np.ndarray], Dict[str, Any]], <PERSON><PERSON>[np.ndarray, List[str]]]

def calculate_normalized_diff(
    band_data: Dict[str, np.ndarray], 
    params: Dict[str, Any] = None
) -> Tuple[np.ndarray, List[str]]:
    """
    Calculate a normalized difference index between two bands.
    
    Args:
        band_data: Dictionary mapping band names to numpy arrays
        params: Additional parameters:
            - numerator_band: Band name for numerator (e.g., 'nir')
            - denominator_band: Band name for denominator (e.g., 'red')
            - output_dtype: Output data type ('float32', 'int16', etc.)
            - scale_factor: Optional scale factor (e.g., 10000 for int16 NDVI)
            - output_band_name: Custom name for output band
            
    Returns:
        Tuple of (output_array, band_names_list)
    """
    params = params or {}
    numerator_band = params.get('numerator_band', 'nir')
    denominator_band = params.get('denominator_band', 'red')
    output_dtype = params.get('output_dtype', 'float32')
    scale_factor = params.get('scale_factor', None)
    output_band_name = params.get('output_band_name', None)
    
    # Check if both required bands exist
    missing_bands = []
    if numerator_band not in band_data:
        missing_bands.append(numerator_band)
    if denominator_band not in band_data:
        missing_bands.append(denominator_band)
        
    if missing_bands:
        available = list(band_data.keys())
        logger.warning(f"Cannot calculate normalized difference index: missing bands {missing_bands}. "
                      f"Need {numerator_band} and {denominator_band}, available: {available}")
        
        # Return empty array if bands are missing
        if not band_data:
            return np.array([[[]]]), ["empty"]
        
        # Use shape from any available band
        sample_band = next(iter(band_data.values()))
        shape = sample_band.shape
        
        # Return zero-filled array
        if output_dtype.startswith('float'):
            empty_array = np.full((1, *shape), np.nan, dtype=np.dtype(output_dtype))
            return empty_array, [output_band_name or f"ND_{numerator_band}_{denominator_band}"]
        else:
            empty_array = np.zeros((1, *shape), dtype=np.dtype(output_dtype))
            return empty_array, [output_band_name or f"ND_{numerator_band}_{denominator_band}"]
    
    # Get the band data
    band1 = band_data[numerator_band]
    band2 = band_data[denominator_band]
    
    # Create combined mask of valid data
    valid_mask = ~(np.isnan(band1) | np.isnan(band2))
    valid_data_percentage = np.mean(valid_mask) * 100.0 if valid_mask.size > 0 else 0
    logger.debug(f"Normalized diff: {valid_data_percentage:.1f}% valid data")
    
    # Calculate the index
    result = np.zeros_like(band1, dtype=np.float32)
    
    # Only calculate where both bands have valid data
    if np.any(valid_mask):
        denominator = band1 + band2
        valid_denominator = (denominator > 0) & valid_mask
        
        if np.any(valid_denominator):
            result[valid_denominator] = (
                (band1[valid_denominator] - band2[valid_denominator]) / 
                denominator[valid_denominator]
            )
    
    # Clip to [-1, 1] range
    result = np.clip(result, -1.0, 1.0)
    
    # Apply scaling if needed
    if scale_factor is not None and output_dtype.startswith(('int', 'uint')):
        result = (result * scale_factor).astype(output_dtype)
    elif not output_dtype.startswith('float'):
        result = result.astype(output_dtype)
    
    # Add channel dimension if needed
    if len(result.shape) == 2:
        result = result[np.newaxis, :, :]
    
    # Return the result and band name
    band_name = output_band_name or f"ND_{numerator_band}_{denominator_band}"
    return result, [band_name]

def calculate_ndvi(
    band_data: Dict[str, np.ndarray],
    params: Dict[str, Any] = None
) -> Tuple[np.ndarray, List[str]]:
    """
    Calculate NDVI index (NIR-Red)/(NIR+Red)
    """
    params = params or {}
    output_dtype = params.get('output_dtype', 'float32')
    
    # Use int16 with scaling by default for NDVI
    if output_dtype.startswith('int'):
        params['scale_factor'] = params.get('scale_factor', 10000.0)
    
    merged_params = {
        'numerator_band': 'nir',
        'denominator_band': 'red',
        'output_band_name': 'NDVI',
        'output_dtype': output_dtype,
        **params
    }
    
    return calculate_normalized_diff(band_data, merged_params)

def calculate_ndwi(
    band_data: Dict[str, np.ndarray],
    params: Dict[str, Any] = None
) -> Tuple[np.ndarray, List[str]]:
    """
    Calculate NDWI (Normalized Difference Water Index)
    (Green-NIR)/(Green+NIR)
    """
    params = params or {}
    
    merged_params = {
        'numerator_band': 'green',
        'denominator_band': 'nir',
        'output_band_name': 'NDWI',
        **params
    }
    
    return calculate_normalized_diff(band_data, merged_params)

def calculate_evi(
    band_data: Dict[str, np.ndarray],
    params: Dict[str, Any] = None
) -> Tuple[np.ndarray, List[str]]:
    """
    Calculate Enhanced Vegetation Index (EVI)
    EVI = G * ((NIR - RED) / (NIR + C1 * RED - C2 * BLUE + L))
    """
    params = params or {}
    output_dtype = params.get('output_dtype', 'float32')
    
    # Default EVI coefficients
    g_factor = params.get('g_factor', 2.5)
    c1 = params.get('c1', 6.0)
    c2 = params.get('c2', 7.5)
    l_factor = params.get('l_factor', 1.0)
    
    # Check if required bands exist
    required_bands = ['nir', 'red', 'blue']
    missing_bands = [band for band in required_bands if band not in band_data]
    
    if missing_bands:
        available = list(band_data.keys())
        logger.warning(f"Cannot calculate EVI: missing bands {missing_bands}. "
                     f"Need {required_bands}, available: {available}")
        
        # Return empty array with correct shape if possible
        if band_data:
            sample_band = next(iter(band_data.values()))
            shape = sample_band.shape
            if output_dtype.startswith('float'):
                return np.full((1, *shape), np.nan, dtype=np.dtype(output_dtype)), ['EVI']
            else:
                return np.zeros((1, *shape), dtype=np.dtype(output_dtype)), ['EVI']
        return np.array([[[]]]), ['EVI']
    
    # Get the band data
    nir = band_data['nir']
    red = band_data['red']
    blue = band_data['blue']
    
    # Create mask of valid data
    valid_mask = ~(np.isnan(nir) | np.isnan(red) | np.isnan(blue))
    
    # Calculate EVI
    result = np.zeros_like(nir, dtype=np.float32)
    
    if np.any(valid_mask):
        # Calculate denominator
        denominator = nir + c1 * red - c2 * blue + l_factor
        valid_denominator = (denominator > 0) & valid_mask
        
        if np.any(valid_denominator):
            result[valid_denominator] = g_factor * (
                (nir[valid_denominator] - red[valid_denominator]) / 
                denominator[valid_denominator]
            )
    
    # Clip to reasonable EVI range
    result = np.clip(result, -1.0, 1.0)
    
    # Apply scaling if needed
    if output_dtype.startswith(('int', 'uint')):
        scale_factor = params.get('scale_factor', 10000.0)
        result = (result * scale_factor).astype(output_dtype)
    elif not output_dtype.startswith('float'):
        result = result.astype(output_dtype)
    
    # Add channel dimension if needed
    if len(result.shape) == 2:
        result = result[np.newaxis, :, :]
    
    return result, ['EVI']

def simple_band_pass(
    band_data: Dict[str, np.ndarray],
    params: Dict[str, Any] = None
) -> Tuple[np.ndarray, List[str]]:
    """
    Simply passes through all bands with optional reordering.
    
    Args:
        band_data: Dictionary mapping band names to numpy arrays
        params: Additional parameters:
            - band_order: Optional list to specify band order
            - output_dtype: Output data type
            
    Returns:
        Tuple of (output_array, band_names_list)
    """
    params = params or {}
    output_dtype = params.get('output_dtype', None)
    band_order = params.get('band_order', None)
    
    if not band_data:
        return np.array([[[]]]), ['empty']
    
    # If band_order not specified, use sorted order of available bands
    if not band_order:
        band_order = sorted(band_data.keys())
    else:
        # Filter to only include bands that exist in data
        band_order = [b for b in band_order if b in band_data]
    
    # If no bands match, return empty
    if not band_order:
        sample_band = next(iter(band_data.values()))
        shape = sample_band.shape
        if output_dtype:
            return np.zeros((1, *shape), dtype=np.dtype(output_dtype)), ['empty']
        else:
            return np.zeros((1, *shape), dtype=sample_band.dtype), ['empty']
    
    # Stack bands in specified order
    arrays = [band_data[band] for band in band_order]
    
    # Handle mixed shapes by ensuring all are 3D
    for i, arr in enumerate(arrays):
        if len(arr.shape) == 2:
            arrays[i] = arr[np.newaxis, :, :]
    
    # Stack along channel dimension
    stacked = np.vstack(arrays)
    
    # Convert dtype if specified
    if output_dtype:
        stacked = stacked.astype(np.dtype(output_dtype))
    
    return stacked, band_order

# Registry of available index calculations
INDEX_REGISTRY = {
    'ndvi': calculate_ndvi,
    'normalized_diff': calculate_normalized_diff,
    'ndwi': calculate_ndwi,
    'evi': calculate_evi,
    'passthrough': simple_band_pass,
    'chip': simple_band_pass,  # Alias for simple passthrough
}