# geometry.py
import logging
from typing import List, Tu<PERSON>, Dict, Any
from affine import Affine
from rasterio.windows import Window

logger = logging.getLogger(__name__)

def calculate_intersecting_tiles_and_windows(
    chip_pixel_col_start: int,
    chip_pixel_row_start: int,
    chip_pixel_col_end: int,
    chip_pixel_row_end: int,
    tile_width: int,
    tile_height: int,
) -> List[Tuple[int, int, Window]]:
    """
    Calculates which tiles intersect a given chip's pixel bounds and the
    window within each tile corresponding to the chip area.

    Args:
        chip_pixel_col_start: Starting column index of the chip in the full image grid.
        chip_pixel_row_start: Starting row index of the chip in the full image grid.
        chip_pixel_col_end: Ending column index (exclusive) of the chip.
        chip_pixel_row_end: Ending row index (exclusive) of the chip.
        tile_width: Width of the source COG tiles.
        tile_height: Height of the source COG tiles.

    Returns:
        List of tuples: (tile_row, tile_col, window_in_tile).
        'window_in_tile' specifies the region (col_off, row_off, width, height)
        within the source tile (tile_row, tile_col) that corresponds to the
        intersection with the chip.
    """
    intersecting_info = []

    # Determine the range of tiles intersected by the chip
    start_tile_col = chip_pixel_col_start // tile_width
    end_tile_col = (chip_pixel_col_end - 1) // tile_width
    start_tile_row = chip_pixel_row_start // tile_height
    end_tile_row = (chip_pixel_row_end - 1) // tile_height

    for r in range(start_tile_row, end_tile_row + 1):
        for c in range(start_tile_col, end_tile_col + 1):
            # Calculate the pixel bounds of this specific tile
            tile_pixel_col_start = c * tile_width
            tile_pixel_row_start = r * tile_height
            tile_pixel_col_end = tile_pixel_col_start + tile_width
            tile_pixel_row_end = tile_pixel_row_start + tile_height

            # Find the intersection window in absolute pixel coordinates
            intersect_col_start = max(chip_pixel_col_start, tile_pixel_col_start)
            intersect_row_start = max(chip_pixel_row_start, tile_pixel_row_start)
            intersect_col_end = min(chip_pixel_col_end, tile_pixel_col_end)
            intersect_row_end = min(chip_pixel_row_end, tile_pixel_row_end)

            # Calculate the window parameters *relative to the top-left of the tile*
            window_col_offset = intersect_col_start - tile_pixel_col_start
            window_row_offset = intersect_row_start - tile_pixel_row_start
            window_width = intersect_col_end - intersect_col_start
            window_height = intersect_row_end - intersect_row_start

            # Ensure width/height are positive (can be 0 if only touching edge)
            if window_width > 0 and window_height > 0:
                window = Window(
                    col_off=window_col_offset,
                    row_off=window_row_offset,
                    width=window_width,
                    height=window_height
                )
                intersecting_info.append((r, c, window))

    return intersecting_info

def calculate_chip_bounds_affine(
    chip_cx: int,
    chip_cy: int,
    chip_pixel_width: int,
    chip_pixel_height: int,
    chip_size: int,
    grid_definition: Dict[str, Any]
) -> List[float]:
    """
    Calculates the geographic bounds [minx, miny, maxx, maxy] for a specific chip
    using the Affine library convention.

    Args:
        chip_cx: Chip column index
        chip_cy: Chip row index
        chip_pixel_width: Actual width of this chip (can be < chip_size at edges)
        chip_pixel_height: Actual height of this chip
        chip_size: The nominal target chip size used for indexing
        grid_definition: Dictionary with grid parameters including transform

    Returns:
        Bounds as [minx, miny, maxx, maxy]
    """
    transform_tuple = grid_definition.get('transform')
    if not transform_tuple or len(transform_tuple) != 6:
        logger.warning(f"Invalid or missing transform in grid_definition: {transform_tuple}. Cannot calculate bounds.")
        return [-1.0, -1.0, -1.0, -1.0]  # Indicate error

    # Create Affine object (a, b, c, d, e, f)
    affine_transform = Affine(*transform_tuple)

    # Calculate pixel coordinates of the chip's top-left corner in the full grid
    start_pixel_col = chip_cx * chip_size
    start_pixel_row = chip_cy * chip_size

    # Calculate geographic coordinates of the four corners
    # top-left
    ul_x, ul_y = affine_transform * (start_pixel_col, start_pixel_row)
    # top-right
    ur_x, ur_y = affine_transform * (start_pixel_col + chip_pixel_width, start_pixel_row)
    # bottom-left
    ll_x, ll_y = affine_transform * (start_pixel_col, start_pixel_row + chip_pixel_height)
    # bottom-right
    lr_x, lr_y = affine_transform * (start_pixel_col + chip_pixel_width, start_pixel_row + chip_pixel_height)

    # Determine min/max coordinates
    minx = min(ul_x, ur_x, ll_x, lr_x)
    miny = min(ul_y, ur_y, ll_y, lr_y)
    maxx = max(ul_x, ur_x, ll_x, lr_x)
    maxy = max(ul_y, ur_y, ll_y, lr_y)

    return [minx, miny, maxx, maxy]