# band_utils.py
from typing import Tuple, Dict, Set, List

# Mapping between standard band names and Sentinel-2 asset keys
S2_BAND_MAP = {
    # Asset keys to standard names
    'B01': 'coastal', 'B02': 'blue', 'B03': 'green', 'B04': 'red',
    'B05': 'rededge1', 'B06': 'rededge2', 'B07': 'rededge3', 
    'B08': 'nir', 'B8A': 'nir08', 'B09': 'nir09', 'B11': 'swir16', 'B12': 'swir22',
    
    # Standard names to asset keys
    'coastal': 'B01', 'blue': 'B02', 'green': 'B03', 'red': 'B04',
    'rededge1': 'B05', 'rededge2': 'B06', 'rededge3': 'B07', 
    'nir': 'B08', 'nir08': 'B8A', 'nir09': 'B09', 'swir16': 'B11', 'swir22': 'B12'
}

def prepare_input_datasets_map(input_metadata_map: Dict[str, Dict], calculation: str) -> Dict[str, Dict]:
    """
    Map between band names and actual asset keys for a given calculation.
    Ensures that the correct assets are selected for the calculation.
    
    Args:
        input_metadata_map: Map of band keys to dataset metadata
        calculation: Calculation type (e.g., 'ndvi')
        
    Returns:
        Dictionary mapping standard band names to their corresponding metadata
    """
    # Determine required bands for the calculation
    required_bands = set()
    if calculation.lower() == 'ndvi':
        required_bands = {'red', 'nir'}
    elif calculation.lower() == 'ndwi':
        required_bands = {'green', 'nir'}
    # Add more calculations as needed
    
    # Create a mapping from standard names to actual asset keys
    standardized_map = {}
    
    # First, try direct matches for standard names
    for band_key, metadata in input_metadata_map.items():
        std_name, asset_key = normalize_band_name(band_key)
        if std_name in required_bands:
            standardized_map[std_name] = metadata
    
    # Fill in any missing required bands by checking all assets
    for std_band in required_bands:
        if std_band not in standardized_map:
            # Try to find the corresponding asset
            s2_asset_key = S2_BAND_MAP.get(std_band)
            if s2_asset_key:
                # Check if this asset exists in any of the metadata
                for band_key, metadata in input_metadata_map.items():
                    if s2_asset_key in metadata.get('raw_asset_urls', {}):
                        # Found the asset, map the standard name to it
                        standardized_map[std_band] = metadata.copy()
                        # Update any band-specific fields
                        if 'asset_grid_ids' in standardized_map[std_band]:
                            standardized_map[std_band]['_original_asset_key'] = s2_asset_key
    
    return standardized_map

def normalize_band_name(band_name: str) -> Tuple[str, str]:
    """
    Normalizes band names and returns both the standardized name and asset key.
    
    Args:
        band_name: Input band name or asset key
        
    Returns:
        Tuple of (standard_name, asset_key)
    """
    if band_name in S2_BAND_MAP:
        mapped_value = S2_BAND_MAP[band_name]
        
        # Check if mapped value is in the band map and points back to our input
        if mapped_value in S2_BAND_MAP and S2_BAND_MAP[mapped_value] == band_name:
            # Input was an asset key (e.g., 'B04'), mapped_value is standard name (e.g., 'red')
            return (mapped_value, band_name)
        else:
            # Input was a standard name (e.g., 'red'), mapped_value is asset key (e.g., 'B04')
            return (band_name, mapped_value)
    
    # No mapping found, return the same for both
    return (band_name, band_name)

def get_required_bands_for_index(index_name: str) -> Set[str]:
    """
    Returns the set of band names required for a given spectral index.
    
    Args:
        index_name: Name of spectral index
        
    Returns:
        Set of required standard band names
    """
    index_name = index_name.lower()
    
    INDEX_BANDS = {
        'ndvi': {'red', 'nir'},
        'ndwi': {'green', 'nir'},
        'ndbi': {'nir', 'swir1'},
        'ndmi': {'nir', 'swir1'},
        'mndwi': {'green', 'swir1'},
        'bai': {'red', 'nir'},
        'nbr': {'nir', 'swir2'},
        'evi': {'red', 'nir', 'blue'},
        'savi': {'red', 'nir'},
        'msavi': {'red', 'nir'},
        'ndre': {'rededge3', 'nir'},
        'cire': {'rededge3', 'nir'},
    }
    
    return INDEX_BANDS.get(index_name, set())