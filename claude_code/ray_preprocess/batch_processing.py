# batch_processing.py
import asyncio
import ray
import pyarrow as pa
import numpy as np
import httpx
import time
import uuid
import logging
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timezone

from terrafloww.libs.geoarrow_raster.schema import RASTER_CHUNK_SCHEMA
from terrafloww.compute.ray_jobs.preprocess.indices import INDEX_REGISTRY
from terrafloww.compute.ray_jobs.preprocess.tile_processor import (
    identify_unique_tiles, fetch_unique_tiles, assemble_chip_bands
)
from terrafloww.compute.ray_jobs.preprocess.geometry import calculate_chip_bounds_affine
from terrafloww.compute.ray_jobs.preprocess.band_utils import (
    normalize_band_name, get_required_bands_for_index
)

logger = logging.getLogger(__name__)

async def process_chip_batch(batch: pa.Table) -> pa.Table:
    """
    Process a batch of chips to generate spectral indices or other derived products.
    
    Args:
        batch: PyArrow Table with chip definition data
        
    Returns:
        PyArrow Table with processed chips
    """
    task_id = ray.get_runtime_context().get_task_id()[-6:] if ray.is_initialized() else "local"
    start_time = time.time()

    # Convert batch to Python dictionary
    batch_dict = batch.to_pydict()
    num_chips = len(batch_dict.get('chip_cx', []))
    logger.info(f"Task {task_id}: Received batch for {num_chips} chips.")
    
    # Check if batch is empty
    if num_chips == 0:
        return pa.Table.from_pylist([], schema=RASTER_CHUNK_SCHEMA)
    
    # Get grid definition from first chip (all chips should have same grid)
    grid_def = None
    if num_chips > 0 and 'grid_definition' in batch_dict:
        grid_def = batch_dict['grid_definition'][0]
    if grid_def is None:
        logger.error(f"Task {task_id}: Could not find grid_definition in batch data. Aborting batch.")
        return pa.Table.from_pylist([], schema=RASTER_CHUNK_SCHEMA)
    
    # Process the batch
    processed_chips = []
    
    async with httpx.AsyncClient(timeout=60.0, follow_redirects=True, http2=True) as http_client:
        # Step 1: Identify all unique tiles needed for this batch
        unique_tile_fetches = await identify_unique_tiles(batch_dict)
        
        # Step 2: Fetch all unique tiles concurrently
        if unique_tile_fetches:
            logger.info(f"Actor fetching {len(unique_tile_fetches)} unique tiles for batch...")
            decoded_tiles = await fetch_unique_tiles(http_client, unique_tile_fetches)
        else:
            logger.warning("No unique tiles to fetch")
            decoded_tiles = {}
        
        # Step 3: Process each chip in the batch
        for i in range(num_chips):
            try:
                # Extract chip definition
                chip_item = {key: batch_dict[key][i] for key in batch_dict if i < len(batch_dict[key])}
                
                # For logging clarity
                chip_id = f"({chip_item.get('chip_cy', '?')}, {chip_item.get('chip_cx', '?')})"
                
                # Set defaults if missing
                chip_item.setdefault('output_dtype', 'float32')
                chip_item.setdefault('calculation', 'chip')
                
                # Skip chips without required tiles
                if not chip_item.get('required_input_tiles'):
                    logger.warning(f"Chip {chip_id}: No required input tiles specified")
                    continue
                
                # Assemble all available bands from the decoded tiles
                assembled_band_chips = await assemble_chip_bands(
                    chip_item, decoded_tiles, dtype='float32'
                )
                
                # Skip if no bands were assembled
                if not assembled_band_chips:
                    logger.warning(f"Chip {chip_id}: No bands were successfully assembled")
                    continue
                
                # Normalize band names to ensure consistent mapping
                normalized_band_chips = {}
                for band, data in assembled_band_chips.items():
                    std_name, _ = normalize_band_name(band)
                    normalized_band_chips[std_name] = data
                
                # Perform calculation (spectral index or simple chipping)
                calculation_type = chip_item.get('calculation', 'chip')
                
                # Prepare calculation parameters
                calc_params = {
                    'output_dtype': chip_item.get('output_dtype', 'float32')
                }
                
                # Additional parameters from the chip item
                if 'calculation_params' in chip_item:
                    calc_params.update(chip_item['calculation_params'])
                
                # Look up the calculation function
                calc_func = INDEX_REGISTRY.get(calculation_type.lower())
                if calc_func is None:
                    logger.warning(f"Chip {chip_id}: Unknown calculation type '{calculation_type}', "
                                  f"using passthrough instead")
                    calc_func = INDEX_REGISTRY['passthrough']
                
                # Execute the calculation function
                try:
                    output_chip_np, output_bands = calc_func(normalized_band_chips, calc_params)
                except Exception as calc_error:
                    logger.error(f"Chip {chip_id}: Error during calculation: {calc_error}")
                    continue
                
                # Check if calculation returned valid data
                if output_chip_np is None or np.size(output_chip_np) == 0:
                    logger.warning(f"Chip {chip_id}: Calculation '{calculation_type}' returned empty data")
                    continue
                
                # Calculate quality metrics
                valid_pixels = 0
                total_pixels = np.prod(output_chip_np.shape[1:])  # H*W
                
                if np.issubdtype(output_chip_np.dtype, np.floating):
                    valid_pixels = np.sum(~np.isnan(output_chip_np))
                else:
                    # For integer types, assume min value is NoData
                    if np.issubdtype(output_chip_np.dtype, np.signedinteger):
                        valid_pixels = np.sum(output_chip_np != np.iinfo(output_chip_np.dtype).min)
                    else:
                        valid_pixels = np.sum(output_chip_np != 0)
                
                # Calculate valid data percentage
                valid_percent = (valid_pixels / total_pixels) * 100.0 if total_pixels > 0 else 0.0
                
                # If not enough valid data, skip
                if valid_percent < 1.0:  # Less than 1% valid data
                    logger.warning(f"Chip {chip_id}: Insufficient valid data ({valid_percent:.1f}%)")
                    continue
                
                # Calculate geographic bounds
                actual_chip_height, actual_chip_width = output_chip_np.shape[1:]
                chip_bounds = calculate_chip_bounds_affine(
                    chip_item['chip_cx'], 
                    chip_item['chip_cy'],
                    actual_chip_width, 
                    actual_chip_height,
                    chip_item['chip_size'],
                    grid_def
                )
                
                # Get timestamp from chip item or use current time
                datetime_obj = None
                datetime_str = chip_item.get('datetime')
                
                if isinstance(datetime_str, str):
                    try:
                        # Handle potential 'Z' timezone indicator
                        if datetime_str.endswith('Z'):
                            datetime_str = datetime_str[:-1] + '+00:00'
                        datetime_obj = datetime.fromisoformat(datetime_str)
                        # Ensure timezone is UTC if conversion didn't make it aware
                        if datetime_obj.tzinfo is None:
                            datetime_obj = datetime_obj.replace(tzinfo=timezone.utc)
                    except ValueError:
                        logger.warning(f"Chip {chip_id}: Could not parse datetime: {datetime_str}")
                        datetime_obj = datetime.now(timezone.utc)
                elif isinstance(datetime_str, datetime):
                    datetime_obj = datetime_str
                else:
                    datetime_obj = datetime.now(timezone.utc)
                
                # Generate unique chip ID
                asset_key = chip_item.get('asset_key', 'unknown')
                unique_chip_id = f"chip_{asset_key}_{chip_item['chip_cy']}_{chip_item['chip_cx']}_{uuid.uuid4().hex[:6]}"
                
                # Create chip record for output
                chip_record = {
                    "chip_id": unique_chip_id,
                    "raster_data": output_chip_np.flatten().tolist(),
                    "shape": list(output_chip_np.shape),  # [C, H, W]
                    "bounds": chip_bounds,
                    "crs": grid_def['crs'],
                    "datetime": datetime_obj,
                    "bands": output_bands,
                    "label": None,  # Optional label could be added here
                    # Additional metadata if needed
                    "quality": {
                        "valid_data_percent": float(valid_percent)
                    }
                }
                
                processed_chips.append(chip_record)
                logger.debug(f"Chip {chip_id}: Successfully processed with {len(output_bands)} output bands")
                
            except Exception as e:
                logger.exception(f"Error processing chip index {i}: {e}")
                # Continue with next chip
    
    # Create output table
    output_schema = RASTER_CHUNK_SCHEMA
    
    if not processed_chips:
        logger.warning(f"Task {task_id}: No chips were successfully processed")
        result_table = pa.Table.from_pylist([], schema=output_schema)
    else:
        try:
            result_table = pa.Table.from_pylist(processed_chips, schema=output_schema)
        except Exception as schema_e:
            logger.exception(f"Task {task_id}: Schema error creating batch result table: {schema_e}")
            result_table = pa.Table.from_pylist([], schema=output_schema)
    
    end_time = time.time()
    logger.info(f"AsyncHelper {task_id}: Batch processing took {end_time - start_time:.2f}s, output {len(result_table)} chips.")
    
    return result_table

# Synchronous Ray Task wrapper
def process_chip_batch_task_wrapper(batch: pa.Table) -> pa.Table:
    """
    Synchronous Ray Task wrapper that executes the async batch processing logic.
    """
    task_id = ray.get_runtime_context().get_task_id()[-6:] if ray.is_initialized() else "local"
    logger.info(f"Wrapper Task {task_id}: Running async helper...")
    
    try:
        # Ensure a new event loop is created for each task
        result_table = asyncio.run(process_chip_batch(batch))
        logger.info(f"Wrapper Task {task_id}: Async helper completed.")
        return result_table
    except Exception as e:
        logger.exception(f"Wrapper Task {task_id}: Exception during asyncio.run: {e}")
        # Return empty table on error with appropriate schema
        return pa.Table.from_pylist([], schema=RASTER_CHUNK_SCHEMA)