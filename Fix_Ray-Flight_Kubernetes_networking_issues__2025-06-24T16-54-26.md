[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:Create lightweight user-facing SDK structure DESCRIPTION:Create new sdk-public directory with clean user-facing API that only contains gRPC client code and basic data structures, no heavy dependencies
-[x] NAME:Extract user-facing API code DESCRIPTION:Copy and clean up collections.py, loaders.py and other user-facing code, removing internal dependencies and imports
-[x] NAME:Create lightweight pyproject.toml DESCRIPTION:Create new pyproject.toml with minimal dependencies (grpcio, pyarrow, geopandas) and no internal libraries
-[x] NAME:Update gRPC client code DESCRIPTION:Ensure gRPC client code works with hosted Processing Engine without requiring internal libraries
-[x] NAME:Test lightweight SDK DESCRIPTION:Test that new SDK can be installed cleanly and works with the existing Processing Engine
-[x] NAME:Update documentation DESCRIPTION:Update README and documentation to show clear separation between user SDK and internal platform development