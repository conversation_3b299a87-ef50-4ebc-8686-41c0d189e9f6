# SPDX-FileCopyrightText: Terrafloww Labs, 2025

apiVersion: apps/v1
kind: Deployment
metadata:
  name: terrafloww-processing-engine
  namespace: terrafloww-platform
  labels:
    app: terrafloww
    component: processing-engine
spec:
  replicas: 1
  selector:
    matchLabels:
      app: terrafloww
      component: processing-engine
  template:
    metadata:
      labels:
        app: terrafloww
        component: processing-engine
    spec:
      # Schedule on data pool nodes
      nodeSelector:
        doks.digitalocean.com/node-pool: data-pool-e0y1e6npd
      tolerations:
      - key: workload
        operator: Equal
        value: data
        effect: NoSchedule
      imagePullSecrets:
      - name: registry-terrafloww-dev
      containers:
      - name: processing-engine
        image: registry.digitalocean.com/terrafloww-dev/processing-engine:24175c2-20250626-131518
        imagePullPolicy: Always
        ports:
        - containerPort: 50051
          name: grpc
          protocol: TCP
        - containerPort: 50052
          name: flight
          protocol: TCP
        env:
        - name: GRPC_PORT
          value: "50051"
        - name: FLIGHT_PORT
          value: "50052"
        - name: FLIGHT_SERVER_HOST
          value: "0.0.0.0"
        # RAY_ADDRESS for Ray Client mode - using head service DNS name with client port
        - name: RAY_ADDRESS
          value: "ray://terrafloww-ray-cluster-head-svc:10001"
        # RAY_ENABLE_AUTO_CONNECT - stop auto-init from firing globally
        - name: RAY_ENABLE_AUTO_CONNECT
          value: "0"
        # --- ADD THIS ENVIRONMENT VARIABLE ---
        - name: FLIGHT_INTERNAL_SVC_ADDRESS
          # Use the Kubernetes internal DNS name for your service.
          # Format: <service-name>.<namespace>.svc.cluster.local:<port>
          value: "terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052"
        - name: FLIGHT_CACHE_TTL_SECONDS
          value: "3600"
        - name: TFW_OUTPUT_STORAGE_PATH
          value: "/tmp/terrafloww/results"
        - name: TFW_USE_HTTP_POOL
          value: "false"  # HTTP optimization feature flag (safe default)
        - name: PYTHONPATH
          value: "/app"
        # S3/Cloud catalog configuration
        - name: STAC_CATALOG_S3_BUCKET
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: bucket
        - name: STAC_CATALOG_S3_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: endpoint
        - name: STAC_CATALOG_S3_REGION
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: region
        - name: STAC_CATALOG_S3_PATH_PREFIX
          value: "catalog"
        # DigitalOcean Spaces credentials
        - name: DO_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: access_key_id
        - name: DO_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: secret_access_key
        - name: DO_REGION
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: region
        - name: DO_SPACE_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: terrafloww-catalog-secrets
              key: endpoint
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
          requests:
            cpu: "500m"
            memory: "1Gi"
        livenessProbe:
          exec:
            command:
            - python
            - -c
            - "import grpc; import sys; sys.exit(0)"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - python
            - -c
            - "import grpc; import sys; sys.exit(0)"
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: tmp-storage
          mountPath: /tmp/terrafloww
      volumes:
      - name: tmp-storage
        emptyDir: {}
      restartPolicy: Always
