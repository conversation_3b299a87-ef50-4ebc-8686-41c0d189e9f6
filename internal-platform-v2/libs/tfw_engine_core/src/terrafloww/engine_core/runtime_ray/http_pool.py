# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/http_pool.py

"""
HTTP Connection Pool for Ray Actors

Provides persistent HTTP connection pooling using aiohttp for optimal performance
in satellite image processing workloads. Replaces per-task HTTP client creation
with persistent connection reuse across Ray tasks.

Performance Target: 20x improvement (123s → 6s) for NDVI time series processing
"""

import asyncio
import logging
import time
from typing import List, Dict, Tuple, Optional, Any
from collections import defaultdict
from urllib.parse import urlparse

import aiohttp
import ray

from .common_types import WindowSpec

logger = logging.getLogger(__name__)


@ray.remote
class HTTPConnectionPool:
    """
    Ray actor providing persistent HTTP connection pooling with aiohttp.
    
    Maintains persistent aiohttp.ClientSession for connection reuse across
    multiple Ray tasks, eliminating TCP handshake overhead and enabling
    HTTP/2 multiplexing for optimal COG data fetching performance.
    """
    
    def __init__(self, pool_size: int = 4, max_connections: int = 100):
        """
        Initialize HTTP connection pool actor.
        
        Args:
            pool_size: Number of HTTP client sessions to maintain
            max_connections: Maximum total connections across all sessions
        """
        self.pool_size = pool_size
        self.max_connections = max_connections
        self.sessions: List[aiohttp.ClientSession] = []
        self.round_robin_index = 0
        
        # Performance tracking
        self.stats = {
            "requests_processed": 0,
            "bytes_fetched": 0,
            "connection_reuses": 0,
            "errors": 0,
            "last_reset": time.time()
        }
        
        # Connection health monitoring
        self.health_check_interval = 60  # seconds
        self.max_error_rate = 0.1  # 10% error rate triggers reset
        self._health_monitor_task = None
        
    async def initialize(self):
        """
        Initialize persistent aiohttp ClientSessions with optimal configuration.
        
        Creates multiple sessions for load distribution and configures
        TCPConnector for maximum connection reuse and HTTP/2 support.
        """
        try:
            # Calculate connections per session
            connections_per_session = max(1, self.max_connections // self.pool_size)
            
            for i in range(self.pool_size):
                # Configure TCP connector for optimal performance
                connector = aiohttp.TCPConnector(
                    limit=connections_per_session,
                    limit_per_host=min(50, connections_per_session),
                    keepalive_timeout=300,  # 5 minutes
                    enable_cleanup_closed=True,
                    use_dns_cache=True,
                    ttl_dns_cache=300,
                    family=0,  # Allow both IPv4 and IPv6
                )
                
                # Configure timeouts
                timeout = aiohttp.ClientTimeout(
                    total=30.0,
                    connect=10.0,
                    sock_read=30.0
                )
                
                # Create session with optimal headers
                session = aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout,
                    headers={
                        "User-Agent": "TerraflowPlatform/1.0",
                        "Accept-Encoding": "gzip, deflate",
                        "Connection": "keep-alive"
                    },
                    trust_env=True
                )
                
                self.sessions.append(session)
                logger.info(f"Created HTTP session {i} with {connections_per_session} max connections")
            
            # Start health monitoring
            self._health_monitor_task = asyncio.create_task(self._monitor_health())
            
            logger.info(f"HTTP connection pool initialized with {self.pool_size} sessions, "
                       f"{self.max_connections} total connections")
            
        except Exception as e:
            logger.error(f"Failed to initialize HTTP connection pool: {e}")
            raise
    
    async def fetch_batch(self, window_specs_batch: List[WindowSpec]) -> Dict[str, bytes]:
        """
        Fetch COG data for a batch of window specifications using persistent connections.
        
        Groups requests by URL for optimal connection reuse and implements
        range merging to minimize total HTTP requests.
        
        Args:
            window_specs_batch: List of WindowSpec objects to fetch
            
        Returns:
            Dictionary mapping request keys to fetched byte data
        """
        if not window_specs_batch:
            return {}
        
        try:
            # Group requests by URL for connection reuse
            url_groups = self._group_by_url(window_specs_batch)
            
            results = {}
            fetch_tasks = []
            
            # Process each URL group with optimal session selection
            for url, specs in url_groups.items():
                session = self._get_next_session()
                task = self._fetch_url_group(session, url, specs)
                fetch_tasks.append(task)
            
            # Execute all URL groups concurrently
            group_results = await asyncio.gather(*fetch_tasks, return_exceptions=True)
            
            # Combine results from all groups
            for group_result in group_results:
                if isinstance(group_result, Exception):
                    logger.error(f"URL group fetch failed: {group_result}")
                    self.stats["errors"] += 1
                elif isinstance(group_result, dict):
                    results.update(group_result)
            
            # Update statistics
            self.stats["requests_processed"] += len(window_specs_batch)
            total_bytes = sum(len(data) for data in results.values() if data)
            self.stats["bytes_fetched"] += total_bytes
            
            logger.debug(f"Fetched {len(results)} ranges, {total_bytes} bytes total")
            return results
            
        except Exception as e:
            logger.error(f"Batch fetch failed: {e}")
            self.stats["errors"] += 1
            return {}
    
    async def _fetch_url_group(self, session: aiohttp.ClientSession, url: str, 
                              specs: List[WindowSpec]) -> Dict[str, bytes]:
        """
        Fetch all ranges for a single URL using the same session.
        
        Implements range merging to combine nearby byte ranges into
        fewer HTTP requests for optimal performance.
        """
        try:
            # Merge nearby byte ranges to minimize requests
            merged_ranges = self._merge_byte_ranges(specs)
            
            results = {}
            range_tasks = []
            
            # Fetch all merged ranges concurrently
            for start, end in merged_ranges:
                task = self._fetch_range(session, url, start, end)
                range_tasks.append((task, start, end))
            
            # Wait for all range fetches
            for task, start, end in range_tasks:
                try:
                    data = await task
                    if data:
                        # Extract individual spec data from merged range
                        spec_data = self._extract_spec_data(specs, data, start, end)
                        results.update(spec_data)
                        self.stats["connection_reuses"] += 1
                except Exception as e:
                    logger.error(f"Range fetch failed for {url} {start}-{end}: {e}")
                    self.stats["errors"] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"URL group fetch failed for {url}: {e}")
            return {}
    
    async def _fetch_range(self, session: aiohttp.ClientSession, url: str, 
                          start: int, end: int) -> Optional[bytes]:
        """
        Fetch a single byte range using persistent session.
        """
        headers = {"Range": f"bytes={start}-{end}"}
        
        try:
            async with session.get(url, headers=headers) as response:
                if response.status in (200, 206):
                    return await response.read()
                else:
                    logger.warning(f"HTTP {response.status} for {url} range {start}-{end}")
                    return None
                    
        except Exception as e:
            logger.error(f"Range fetch exception for {url} {start}-{end}: {e}")
            return None
    
    def _group_by_url(self, window_specs: List[WindowSpec]) -> Dict[str, List[WindowSpec]]:
        """Group window specifications by URL for connection reuse."""
        url_groups = defaultdict(list)
        for spec in window_specs:
            url_groups[spec.cog_href].append(spec)
        return dict(url_groups)
    
    def _merge_byte_ranges(self, specs: List[WindowSpec], gap_threshold: int = 8192) -> List[Tuple[int, int]]:
        """
        Merge nearby byte ranges to minimize HTTP requests.
        
        Args:
            specs: List of WindowSpec objects
            gap_threshold: Maximum gap size to merge (8KB default)
            
        Returns:
            List of (start, end) tuples for merged ranges
        """
        if not specs:
            return []
        
        # Sort by byte offset
        sorted_specs = sorted(specs, key=lambda s: s.byte_offset)
        ranges = [(s.byte_offset, s.byte_offset + s.byte_size - 1) for s in sorted_specs]
        
        merged = [ranges[0]]
        for start, end in ranges[1:]:
            last_start, last_end = merged[-1]
            
            # Merge if gap is smaller than threshold
            if start <= last_end + gap_threshold:
                merged[-1] = (last_start, max(last_end, end))
            else:
                merged.append((start, end))
        
        return merged
    
    def _extract_spec_data(self, specs: List[WindowSpec], merged_data: bytes, 
                          range_start: int, range_end: int) -> Dict[str, bytes]:
        """
        Extract individual spec data from merged range response.
        """
        results = {}
        
        for spec in specs:
            spec_start = spec.byte_offset
            spec_end = spec.byte_offset + spec.byte_size - 1
            
            # Check if this spec is within the merged range
            if spec_start >= range_start and spec_end <= range_end:
                # Calculate offset within merged data
                offset_in_merged = spec_start - range_start
                spec_data = merged_data[offset_in_merged:offset_in_merged + spec.byte_size]
                
                # Create unique key for this spec
                key = f"{spec.cog_href}:{spec.byte_offset}-{spec.byte_offset + spec.byte_size - 1}"
                results[key] = spec_data
        
        return results
    
    def _get_next_session(self) -> aiohttp.ClientSession:
        """Get next session using round-robin distribution."""
        session = self.sessions[self.round_robin_index]
        self.round_robin_index = (self.round_robin_index + 1) % len(self.sessions)
        return session
    
    async def _monitor_health(self):
        """Background task to monitor connection health and reset if needed."""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                # Check error rate
                total_requests = max(self.stats["requests_processed"], 1)
                error_rate = self.stats["errors"] / total_requests
                
                if error_rate > self.max_error_rate:
                    logger.warning(f"High error rate detected: {error_rate:.2%}, resetting sessions")
                    await self._reset_sessions()
                    
            except Exception as e:
                logger.error(f"Health monitor error: {e}")
    
    async def _reset_sessions(self):
        """Reset all HTTP sessions due to health issues."""
        try:
            # Close existing sessions
            for session in self.sessions:
                await session.close()
            
            self.sessions.clear()
            
            # Reinitialize sessions
            await self.initialize()
            
            # Reset stats
            self.stats = {
                "requests_processed": 0,
                "bytes_fetched": 0,
                "connection_reuses": 0,
                "errors": 0,
                "last_reset": time.time()
            }
            
            logger.info("HTTP sessions reset successfully")
            
        except Exception as e:
            logger.error(f"Session reset failed: {e}")
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get connection pool performance statistics."""
        return {
            **self.stats,
            "pool_size": self.pool_size,
            "max_connections": self.max_connections,
            "active_sessions": len(self.sessions)
        }
    
    async def cleanup(self):
        """Clean shutdown of all HTTP sessions."""
        try:
            if self._health_monitor_task:
                self._health_monitor_task.cancel()
                
            for session in self.sessions:
                await session.close()
                
            self.sessions.clear()
            logger.info("HTTP connection pool cleaned up")
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")
