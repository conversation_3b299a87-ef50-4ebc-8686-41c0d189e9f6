# Ray Performance Optimization Tasks Summary
**Target**: 6-second NDVI processing with 4-CPU Ray workers  
**Current**: 123 seconds (20x slower than pure Python)  
**Root Cause**: HTTP connection inefficiencies in Ray tasks

## High-Impact Tasks (10x+ Performance Boost)

### 1. Migrate Ray Tasks to Ray Actors with Persistent HTTP Clients
**Impact**: 10-15x improvement  
**Effort**: Medium (2-3 days)  
**Priority**: CRITICAL

```python
# BEFORE: Ray Task (creates new client each time)
@ray.remote
def process_batch_on_worker(window_specs_batch):
    async with httpx.AsyncClient() as client:  # ❌ New client per task
        # Process and discard

# AFTER: Ray Actor (persistent client)
@ray.remote
class HTTPWorkerActor:
    def __init__(self):
        self.client = None
    
    async def initialize(self):
        self.client = httpx.AsyncClient(
            limits=httpx.Limits(max_keepalive_connections=50),
            http2=True
        )  # ✅ Persistent across requests
    
    async def process_batch(self, window_specs_batch):
        # Reuse self.client for all requests
```

**Why This Works:**
- Eliminates TCP handshake overhead (100-200ms per connection)
- Enables HTTP/2 connection multiplexing
- Maintains connection pools across Ray task invocations
- Reduces effective I/O time from 6.77s to ~0.5s per batch

### 2. Implement HTTP Range Request Merging
**Impact**: 3-5x improvement  
**Effort**: Low (1 day)  
**Priority**: HIGH

```python
def merge_byte_ranges(requests: List[WindowSpec], gap_threshold: int = 8192):
    """Merge nearby byte ranges to minimize HTTP requests"""
    ranges = [(r.byte_offset, r.byte_offset + r.byte_size) for r in requests]
    ranges.sort()
    merged = [ranges[0]]
    
    for start, end in ranges[1:]:
        last_start, last_end = merged[-1]
        if start <= last_end + gap_threshold:
            merged[-1] = (last_start, max(last_end, end))
        else:
            merged.append((start, end))
    
    return merged
```

**Why This Works:**
- Reduces number of HTTP requests by 50-80%
- Leverages COG spatial locality (nearby tiles often accessed together)
- Minimizes request overhead while maintaining data efficiency

### 3. Group Requests by URL for Connection Reuse
**Impact**: 2-3x improvement  
**Effort**: Low (1 day)  
**Priority**: HIGH

```python
async def process_batch_optimized(self, window_specs_batch):
    # Group by URL for connection reuse
    url_groups = defaultdict(list)
    for spec in window_specs_batch:
        url_groups[spec.cog_href].append(spec)
    
    results = []
    for url, specs in url_groups.items():
        # Process all requests for same URL together
        batch_results = await self._process_url_batch(url, specs)
        results.extend(batch_results)
    
    return results
```

**Why This Works:**
- Maximizes HTTP/2 connection multiplexing
- Reduces connection establishment overhead
- Enables better request pipelining

## Medium-Impact Tasks (2-5x Performance Boost)

### 4. Optimize Ray Cluster Configuration
**Impact**: 2x improvement  
**Effort**: Low (configuration change)  
**Priority**: MEDIUM

```yaml
# Current (suboptimal)
rayStartParams:
  num-cpus: '2'
resources:
  limits:
    cpu: "2"
    memory: "4Gi"

# Optimized for HTTP workloads
rayStartParams:
  num-cpus: '4'  # Match target 4-CPU performance
  object-store-memory: '2147483648'  # 2GB for COG caching
resources:
  limits:
    cpu: "4"
    memory: "8Gi"  # More memory for connection pools
```

### 5. Implement Actor Pool Management
**Impact**: 2-3x improvement  
**Effort**: Medium (1-2 days)  
**Priority**: MEDIUM

```python
class RayDriver:
    def __init__(self):
        self.http_actors = []
    
    async def initialize_actors(self, num_actors: int = 4):
        # Create persistent actor pool
        self.http_actors = [HTTPWorkerActor.remote(i) for i in range(num_actors)]
        await asyncio.gather(*[actor.initialize.remote() for actor in self.http_actors])
    
    async def execute_workflow(self, execution_id: str, plan):
        # Distribute work across actor pool
        tasks = []
        for i, batch in enumerate(window_batches):
            actor = self.http_actors[i % len(self.http_actors)]
            tasks.append(actor.process_batch.remote(batch))
        
        return await asyncio.gather(*tasks)
```

### 6. Evaluate aiohttp vs httpx Performance
**Impact**: 1.5-2x improvement  
**Effort**: Medium (2-3 days for benchmarking)  
**Priority**: MEDIUM

```python
# Benchmark both libraries
async def benchmark_http_clients():
    # Test httpx
    async with httpx.AsyncClient(limits=httpx.Limits(max_keepalive_connections=50)) as client:
        start = time.time()
        await process_test_batch(client)
        httpx_time = time.time() - start
    
    # Test aiohttp  
    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(limit=50)) as session:
        start = time.time()
        await process_test_batch(session)
        aiohttp_time = time.time() - start
    
    return {"httpx": httpx_time, "aiohttp": aiohttp_time}
```

## Low-Impact Tasks (Infrastructure)

### 7. Implement Bounded Streams for Backpressure
**Impact**: 1.2-1.5x improvement  
**Effort**: High (3-4 days)  
**Priority**: LOW

```python
# anyio-style bounded streams in Ray actors
@ray.remote
class StreamingHTTPProcessor:
    def __init__(self, max_queue_size: int = 1000):
        self.input_queue = asyncio.Queue(maxsize=max_queue_size)
        self.output_queue = asyncio.Queue(maxsize=max_queue_size)
```

### 8. Add Connection Health Monitoring
**Impact**: 1.1-1.3x improvement  
**Effort**: Medium (2 days)  
**Priority**: LOW

```python
async def monitor_connection_health(self):
    """Monitor and recreate unhealthy connections"""
    if self.client and self.client.is_closed:
        await self.client.aclose()
        self.client = await self._create_new_client()
```

## Implementation Timeline

### Week 1: Core Actor Migration (Target: 50% improvement)
- [ ] Task 1: Migrate Ray tasks to Ray actors with persistent HTTP clients
- [ ] Task 4: Optimize Ray cluster configuration (4 CPUs, 8GB memory)
- [ ] Task 5: Implement basic actor pool management

**Expected Result**: 123s → 60s (2x improvement)

### Week 2: HTTP Optimization (Target: 80% improvement)  
- [ ] Task 2: Implement HTTP range request merging
- [ ] Task 3: Group requests by URL for connection reuse
- [ ] Task 6: Benchmark aiohttp vs httpx performance

**Expected Result**: 60s → 25s (5x total improvement)

### Week 3: Advanced Patterns (Target: 95% improvement)
- [ ] Task 7: Implement bounded streams for backpressure
- [ ] Task 8: Add connection health monitoring
- [ ] Performance tuning and optimization

**Expected Result**: 25s → 6s (20x total improvement)

## Success Metrics

### Performance Targets
- **Week 1**: 60 seconds (2x improvement)
- **Week 2**: 25 seconds (5x improvement)  
- **Week 3**: 6 seconds (20x improvement, matching pure Python)

### Technical Metrics
- **HTTP connection reuse**: >90% of requests use existing connections
- **Request merging efficiency**: >50% reduction in HTTP request count
- **Actor utilization**: >80% of 4-CPU capacity utilized
- **Memory usage**: <6GB per Ray worker (within 8GB limit)

### Validation Tests
```python
# Performance validation test
async def validate_performance():
    start_time = time.time()
    
    # Process 72 Sentinel-2 scenes (Bangalore AOI, full year 2024)
    result = await process_ndvi_time_series(
        bbox=(77.55, 13.01, 77.58, 13.08),
        date_range=("2024-01-01", "2024-12-31")
    )
    
    total_time = time.time() - start_time
    
    assert total_time < 10.0, f"Performance target missed: {total_time}s > 10s"
    assert len(result) == 72, f"Data completeness check failed: {len(result)} != 72"
    
    print(f"✅ Performance target achieved: {total_time:.2f}s")
```

## Key Insights

1. **Ray parallelism works perfectly** - the issue is HTTP connection efficiency, not Ray scaling
2. **Pure Python patterns are directly applicable** - COGReader patterns can be adapted to Ray actors
3. **Connection pooling is the biggest win** - 10x+ improvement from persistent HTTP clients
4. **Range merging provides significant gains** - 3-5x improvement from request optimization
5. **4-CPU workers are optimal** - matches pure Python performance target with proper HTTP patterns

The path to 6-second performance is clear: migrate to Ray actors with persistent HTTP clients and implement the proven optimization patterns from the pure Python implementation.
