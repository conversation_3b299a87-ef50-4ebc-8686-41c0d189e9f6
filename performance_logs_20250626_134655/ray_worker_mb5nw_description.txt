Name:             terrafloww-ray-cluster-worker-group-worker-mb5nw
Namespace:        terrafloww-platform
Priority:         0
Service Account:  default
Node:             data-pool-e0y1e6npd-l3xcq/10.122.0.18
Start Time:       Thu, 26 Jun 2025 13:41:08 +0530
Labels:           app=terrafloww
                  app.kubernetes.io/created-by=kuberay-operator
                  app.kubernetes.io/name=kuberay
                  component=ray-worker
                  ray.io/cluster=terrafloww-ray-cluster
                  ray.io/group=worker-group
                  ray.io/identifier=terrafloww-ray-cluster-worker
                  ray.io/is-ray-node=yes
                  ray.io/node-type=worker
Annotations:      <none>
Status:           Running
IP:               ************
IPs:
  IP:           ************
Controlled By:  RayCluster/terrafloww-ray-cluster
Init Containers:
  wait-gcs-ready:
    Container ID:  containerd://e7c276db22498358a3cd7ef35d0cedc62831e031142eab72bf6d98fefaa812f1
    Image:         registry.digitalocean.com/terrafloww-dev/ray-custom:**********
    Image ID:      registry.digitalocean.com/terrafloww-dev/ray-custom@sha256:****************************************************************
    Port:          <none>
    Host Port:     <none>
    Command:
      /bin/bash
      -lc
      --
    Args:
      
                            SECONDS=0
                            while true; do
                              if (( SECONDS <= 120 )); then
                                if ray health-check --address terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379 > /dev/null 2>&1; then
                                  echo "GCS is ready."
                                  break
                                fi
                                echo "$SECONDS seconds elapsed: Waiting for GCS to be ready."
                              else
                                if ray health-check --address terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379; then
                                  echo "GCS is ready. Any error messages above can be safely ignored."
                                  break
                                fi
                                echo "$SECONDS seconds elapsed: Still waiting for GCS to be ready. For troubleshooting, refer to the FAQ at https://github.com/ray-project/kuberay/blob/master/docs/guidance/FAQ.md."
                              fi
                              sleep 5
                            done
                          
    State:          Terminated
      Reason:       Completed
      Exit Code:    0
      Started:      Thu, 26 Jun 2025 13:41:10 +0530
      Finished:     Thu, 26 Jun 2025 13:41:26 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     200m
      memory:  256Mi
    Requests:
      cpu:     200m
      memory:  256Mi
    Environment:
      RAY_DISABLE_IMPORT_WARNING:  1
      RAY_DEDUP_LOGS:              0
      RAY_USAGE_STATS_ENABLED:     0
      FLIGHT_HOST:                 terrafloww-processing-engine-svc
      FLIGHT_PORT:                 50052
      FQ_RAY_IP:                   terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local
      RAY_IP:                      terrafloww-ray-cluster-head-svc
    Mounts:
      /dev/shm from shared-mem (rw)
      /tmp/ray from ray-logs (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-6fwt7 (ro)
Containers:
  ray-worker:
    Container ID:  containerd://a194b701c6eab1cb79c320edc3709d79d3444800b4525fc6787839f27b4193ce
    Image:         registry.digitalocean.com/terrafloww-dev/ray-custom:**********
    Image ID:      registry.digitalocean.com/terrafloww-dev/ray-custom@sha256:****************************************************************
    Port:          8080/TCP
    Host Port:     0/TCP
    Command:
      /bin/bash
      -lc
      --
    Args:
      ulimit -n 65536; ray start  --address=terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379  --block  --dashboard-agent-listen-port=52365  --memory=**********  --metrics-export-port=8080  --num-cpus=2 
    State:          Running
      Started:      Thu, 26 Jun 2025 13:41:28 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     2
      memory:  4Gi
    Requests:
      cpu:      1
      memory:   2Gi
    Liveness:   exec [bash -c wget -T 2 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success] delay=30s timeout=2s period=5s #success=1 #failure=120
    Readiness:  exec [bash -c wget -T 2 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success] delay=10s timeout=2s period=5s #success=1 #failure=10
    Environment:
      RAY_DISABLE_IMPORT_WARNING:           1
      RAY_DEDUP_LOGS:                       0
      RAY_USAGE_STATS_ENABLED:              0
      FLIGHT_HOST:                          terrafloww-processing-engine-svc
      FLIGHT_PORT:                          50052
      FQ_RAY_IP:                            terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local
      RAY_IP:                               terrafloww-ray-cluster-head-svc
      RAY_CLUSTER_NAME:                      (v1:metadata.labels['ray.io/cluster'])
      RAY_CLOUD_INSTANCE_ID:                terrafloww-ray-cluster-worker-group-worker-mb5nw (v1:metadata.name)
      RAY_NODE_TYPE_NAME:                    (v1:metadata.labels['ray.io/group'])
      KUBERAY_GEN_RAY_START_CMD:            ray start  --address=terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379  --block  --dashboard-agent-listen-port=52365  --memory=**********  --metrics-export-port=8080  --num-cpus=2 
      RAY_PORT:                             6379
      RAY_ADDRESS:                          terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379
      RAY_USAGE_STATS_KUBERAY_IN_USE:       1
      RAY_DASHBOARD_ENABLE_K8S_DISK_USAGE:  1
    Mounts:
      /dev/shm from shared-mem (rw)
      /tmp/ray from ray-logs (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-6fwt7 (ro)
Conditions:
  Type                        Status
  PodReadyToStartContainers   True 
  Initialized                 True 
  Ready                       True 
  ContainersReady             True 
  PodScheduled                True 
Volumes:
  ray-logs:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     
    SizeLimit:  <unset>
  shared-mem:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     Memory
    SizeLimit:  2Gi
  kube-api-access-6fwt7:
    Type:                    Projected (a volume that contains injected data from multiple sources)
    TokenExpirationSeconds:  3607
    ConfigMapName:           kube-root-ca.crt
    Optional:                false
    DownwardAPI:             true
QoS Class:                   Burstable
Node-Selectors:              doks.digitalocean.com/node-pool=data-pool-e0y1e6npd
Tolerations:                 node.kubernetes.io/not-ready:NoExecute op=Exists for 300s
                             node.kubernetes.io/unreachable:NoExecute op=Exists for 300s
                             workload=data:NoSchedule
Events:
  Type     Reason             Age    From                Message
  ----     ------             ----   ----                -------
  Warning  FailedScheduling   8m20s  default-scheduler   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
  Normal   Scheduled          5m49s  default-scheduler   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mb5nw to data-pool-e0y1e6npd-l3xcq
  Normal   NotTriggerScaleUp  7m52s  cluster-autoscaler  pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
  Normal   Pulling            5m49s  kubelet             Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
  Normal   Pulled             5m48s  kubelet             Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 780ms (780ms including waiting). Image size: 1339265896 bytes.
  Normal   Created            5m48s  kubelet             Created container: wait-gcs-ready
  Normal   Started            5m48s  kubelet             Started container wait-gcs-ready
  Normal   Pulling            5m31s  kubelet             Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
  Normal   Pulled             5m30s  kubelet             Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 744ms (744ms including waiting). Image size: 1339265896 bytes.
  Normal   Created            5m30s  kubelet             Created container: ray-worker
  Normal   Started            5m30s  kubelet             Started container ray-worker
