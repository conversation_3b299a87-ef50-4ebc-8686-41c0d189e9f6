LAST SEEN   TYPE      REASON                    OBJECT                                                 MESSAGE
8m48s       Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-dk2rf to data-pool-e0y1e6npd-l3xcq
26m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Po<PERSON>'s node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
25m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-85rtz to data-pool-e0y1e6npd-l3xcq
25m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-85rtz   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
25m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-mww9q   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
18m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-55f5k   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
26m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-85rtz   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
26m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-85rtz   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cloudprovider.kubernetes.io/uninitialized: true}, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
32m         Normal    Scheduled                 pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Successfully assigned terrafloww-platform/terrafloww-processing-engine-555b7f7c4c-bgvj2 to data-pool-e0y1e6npd-t0iv9
28m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-85rtz   0/13 nodes are available: 1 Insufficient cpu, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/13 nodes are available: 11 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
26m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-mww9q   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cloudprovider.kubernetes.io/uninitialized: true}, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
18m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-c7jl5 to data-pool-e0y1e6npd-t0iv9
10m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
9m56s       Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
28m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-9gzmp to data-pool-e0y1e6npd-t0iv9
28m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   0/13 nodes are available: 1 Insufficient cpu, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/13 nodes are available: 11 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
30m         Normal    Scheduled                 pod/terrafloww-processing-engine-d677dccb-nlkcp        Successfully assigned terrafloww-platform/terrafloww-processing-engine-d677dccb-nlkcp to data-pool-e0y1e6npd-t0iv9
8m22s       Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-5wvkr to data-pool-e0y1e6npd-t0iv9
26m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cloudprovider.kubernetes.io/uninitialized: true}, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
28m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-mww9q   0/13 nodes are available: 1 Insufficient cpu, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/13 nodes are available: 11 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
25m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
25m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
22m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-dqkqp to data-pool-e0y1e6npd-l3xcq
10m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-fnpff to data-pool-e0y1e6npd-t0iv9
8m22s       Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-kpd82 to data-pool-e0y1e6npd-l3xcq
10m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-4nzvf to data-pool-e0y1e6npd-l3xcq
10m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-l7ngv to data-pool-e0y1e6npd-l3xcq
8m22s       Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
5m50s       Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mb5nw to data-pool-e0y1e6npd-l3xcq
18m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-xtnmj to data-pool-e0y1e6npd-t0iv9
18m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-w5hjx to data-pool-e0y1e6npd-l3xcq
25m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mww9q to data-pool-e0y1e6npd-l3xcq
26m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-mww9q   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
43m         Normal    Scheduled                 pod/data-ingestion-stac-20250626-130329-b7rht          Successfully assigned terrafloww-platform/data-ingestion-stac-20250626-130329-b7rht to data-pool-e0y1e6npd-t0iv9
17m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-55f5k to data-pool-e0y1e6npd-t0iv9
43m         Normal    SuccessfulCreate          job/data-ingestion-stac-20250626-130329                Created pod: data-ingestion-stac-20250626-130329-b7rht
43m         Normal    Pulling                   pod/data-ingestion-stac-20250626-130329-b7rht          Pulling image "registry.digitalocean.com/terrafloww-dev/data-ingestion:latest"
43m         Normal    Started                   pod/data-ingestion-stac-20250626-130329-b7rht          Started container ingestion
43m         Normal    Pulled                    pod/data-ingestion-stac-20250626-130329-b7rht          Successfully pulled image "registry.digitalocean.com/terrafloww-dev/data-ingestion:latest" in 15.121s (15.121s including waiting). Image size: 858097857 bytes.
43m         Normal    Created                   pod/data-ingestion-stac-20250626-130329-b7rht          Created container: ingestion
41m         Normal    Completed                 job/data-ingestion-stac-20250626-130329                Job completed
32m         Normal    SuccessfulCreate          replicaset/terrafloww-processing-engine-555b7f7c4c     Created pod: terrafloww-processing-engine-555b7f7c4c-bgvj2
32m         Normal    Killing                   pod/terrafloww-processing-engine-555b7f7c4c-5zd2p      Stopping container processing-engine
32m         Normal    Pulling                   pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Pulling image "registry.digitalocean.com/terrafloww-dev/processing-engine:1750759555"
32m         Normal    Started                   pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Started container processing-engine
32m         Normal    Pulled                    pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Successfully pulled image "registry.digitalocean.com/terrafloww-dev/processing-engine:1750759555" in 1.203s (1.203s including waiting). Image size: 657939324 bytes.
32m         Normal    Created                   pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Created container: processing-engine
30m         Normal    SuccessfulCreate          replicaset/terrafloww-processing-engine-d677dccb       Created pod: terrafloww-processing-engine-d677dccb-nlkcp
30m         Normal    ScalingReplicaSet         deployment/terrafloww-processing-engine                Scaled up replica set terrafloww-processing-engine-d677dccb from 0 to 1
30m         Normal    Pulling                   pod/terrafloww-processing-engine-d677dccb-nlkcp        Pulling image "registry.digitalocean.com/terrafloww-dev/processing-engine:24175c2-20250626-131518"
30m         Normal    Started                   pod/terrafloww-processing-engine-d677dccb-nlkcp        Started container processing-engine
30m         Normal    Created                   pod/terrafloww-processing-engine-d677dccb-nlkcp        Created container: processing-engine
30m         Normal    Pulled                    pod/terrafloww-processing-engine-d677dccb-nlkcp        Successfully pulled image "registry.digitalocean.com/terrafloww-dev/processing-engine:24175c2-20250626-131518" in 7.718s (7.718s including waiting). Image size: 657869265 bytes.
30m         Normal    Killing                   pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Stopping container processing-engine
30m         Normal    SuccessfulDelete          replicaset/terrafloww-processing-engine-555b7f7c4c     Deleted pod: terrafloww-processing-engine-555b7f7c4c-bgvj2
30m         Normal    ScalingReplicaSet         deployment/terrafloww-processing-engine                Scaled down replica set terrafloww-processing-engine-555b7f7c4c from 1 to 0
28m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-dqkqp
28m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-85rtz
28m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mww9q
28m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-9gzmp
28m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Started container wait-gcs-ready
28m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Created container: wait-gcs-ready
28m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 776ms (776ms including waiting). Image size: ********** bytes.
28m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
28m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
28m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Created container: ray-worker
28m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 724ms (724ms including waiting). Image size: ********** bytes.
28m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Started container ray-worker
27m         Normal    TriggeredScaleUp          pod/terrafloww-ray-cluster-worker-group-worker-mww9q   pod triggered scale-up: [{916f3b54-4b2b-4165-8a5c-96f060b1f76f 2->3 (max: 3)}]
27m         Normal    TriggeredScaleUp          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   pod triggered scale-up: [{916f3b54-4b2b-4165-8a5c-96f060b1f76f 2->3 (max: 3)}]
26m         Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-mww9q   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
26m         Normal    UpdatedLoadBalancer       service/terrafloww-processing-engine-lb                Updated load balancer with new hosts
25m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
25m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
24m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Started container wait-gcs-ready
24m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Created container: wait-gcs-ready
24m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Started container wait-gcs-ready
24m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Created container: wait-gcs-ready
24m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 56.452s (56.452s including waiting). Image size: ********** bytes.
24m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 56.441s (56.441s including waiting). Image size: ********** bytes.
24m         Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
24m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
24m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 688ms (688ms including waiting). Image size: ********** bytes.
24m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
24m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Created container: ray-worker
24m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Created container: ray-worker
24m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Started container ray-worker
24m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Started container ray-worker
24m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 1.439s (1.439s including waiting). Image size: ********** bytes.
22m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Stopping container ray-worker
22m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Stopping container ray-worker
22m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Stopping container ray-worker
22m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-85rtz
22m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mww9q
22m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Readiness probe failed:
22m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Readiness probe failed:
22m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-9gzmp
22m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Readiness probe failed:
22m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-85rtz; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:52:50 +0000 UTC,FinishedAt:2025-06-26 07:54:28 +0000 UTC,ContainerID:containerd://d6924dc6591a0a85f7be89111d9be8beaeaba889e40d05112108d67ef8d0bb07,}
22m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mww9q; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:52:50 +0000 UTC,FinishedAt:2025-06-26 07:54:28 +0000 UTC,ContainerID:containerd://9bad90b4d5527b4b5723737d9762a428bfe4dcf83e8767d2cab258f4824fa4e2,}
22m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-9gzmp; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:48:50 +0000 UTC,FinishedAt:2025-06-26 07:54:28 +0000 UTC,ContainerID:containerd://db5a0c2651dce42239a18549d34c214ccca285b012cb1be62f1aac66e43c8965,}
22m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
22m         Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mww9q; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:52:50 +0000 UTC,FinishedAt:2025-06-26 07:54:28 +0000 UTC,ContainerID:containerd://9bad90b4d5527b4b5723737d9762a428bfe4dcf83e8767d2cab258f4824fa4e2,}, pods "terrafloww-ray-cluster-worker-group-worker-mww9q" not found
22m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 930ms (930ms including waiting). Image size: ********** bytes.
22m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Created container: wait-gcs-ready
22m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Started container wait-gcs-ready
22m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
22m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 1.356s (1.356s including waiting). Image size: ********** bytes.
22m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Started container ray-worker
22m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Created container: ray-worker
22m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-lqptq
22m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-lqptq   Stopping container ray-worker
22m         Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-lqptq; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-24 10:02:32 +0000 UTC,FinishedAt:2025-06-26 07:54:59 +0000 UTC,ContainerID:containerd://6b6db90588b793e22a22371f497da8ea0860165f8dce93ad5006bde5bd28f0a4,}, pods "terrafloww-ray-cluster-worker-group-worker-lqptq" not found
22m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-lqptq   Readiness probe errored: rpc error: code = Unknown desc = failed to exec in container: container is in CONTAINER_EXITED state
18m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
18m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 781ms (781ms including waiting). Image size: ********** bytes.
18m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Created container: wait-gcs-ready
18m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Started container wait-gcs-ready
18m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
18m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
18m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Created container: wait-gcs-ready
18m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 731ms (731ms including waiting). Image size: ********** bytes.
18m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Started container wait-gcs-ready
18m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Started container wait-gcs-ready
18m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 697ms (697ms including waiting). Image size: ********** bytes.
18m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Created container: wait-gcs-ready
18m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
18m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Created container: ray-worker
18m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Started container ray-worker
18m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 918ms (918ms including waiting). Image size: ********** bytes.
18m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Started container ray-worker
18m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Created container: ray-worker
18m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 680ms (680ms including waiting). Image size: ********** bytes.
18m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
18m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
18m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 753ms (753ms including waiting). Image size: ********** bytes.
18m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Created container: ray-worker
18m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Started container ray-worker
18m         Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-55f5k   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
17m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Stopping container ray-worker
17m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-c7jl5
17m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Readiness probe failed:
17m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
17m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Stopping container ray-worker
17m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Stopping container ray-worker
17m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Created container: wait-gcs-ready
17m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Started container wait-gcs-ready
17m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 715ms (715ms including waiting). Image size: ********** bytes.
17m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Readiness probe failed:
17m         Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-xtnmj; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:58:33 +0000 UTC,FinishedAt:2025-06-26 07:59:58 +0000 UTC,ContainerID:containerd://0bd6922f5ff93c092a938745e557e2acf78d7f34ba81e7244e60a3f0754a4fe1,}, pods "terrafloww-ray-cluster-worker-group-worker-xtnmj" not found
16m         Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-w5hjx; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:58:31 +0000 UTC,FinishedAt:2025-06-26 08:00:02 +0000 UTC,ContainerID:containerd://d73d00a0495e2840ccbbd5d7bc2979c2c21aadf63cf293c6cf259e9ebb665fb2,}, pods "terrafloww-ray-cluster-worker-group-worker-w5hjx" not found
16m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
16m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Started container ray-worker
16m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Created container: ray-worker
16m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 729ms (729ms including waiting). Image size: ********** bytes.
16m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Stopping container ray-worker
16m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Readiness probe failed:
10m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
10m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
10m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
10m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-fnpff
10m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Created container: wait-gcs-ready
10m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 734ms (734ms including waiting). Image size: ********** bytes.
10m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Started container wait-gcs-ready
10m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 748ms (748ms including waiting). Image size: ********** bytes.
10m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Created container: wait-gcs-ready
10m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 715ms (715ms including waiting). Image size: ********** bytes.
10m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Started container wait-gcs-ready
10m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Started container wait-gcs-ready
10m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Created container: wait-gcs-ready
10m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
10m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 781ms (781ms including waiting). Image size: ********** bytes.
10m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Started container ray-worker
10m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
10m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Created container: ray-worker
10m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Created container: ray-worker
10m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 740ms (740ms including waiting). Image size: ********** bytes.
10m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
10m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Started container ray-worker
10m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 768ms (769ms including waiting). Image size: ********** bytes.
10m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Created container: ray-worker
10m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Started container ray-worker
9m56s       Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
8m53s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Stopping container ray-worker
8m53s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Stopping container ray-worker
8m53s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Stopping container ray-worker
8m52s       Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Readiness probe failed:
8m49s       Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Readiness probe errored: rpc error: code = Unknown desc = failed to exec in container: container is in CONTAINER_EXITED state
8m49s       Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-l7ngv; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 08:06:44 +0000 UTC,FinishedAt:2025-06-26 08:08:10 +0000 UTC,ContainerID:containerd://122c0113da19d99f84eb92f512aa0b1ccfa36e6fd42715221bf0b85db3548fc1,}, pods "terrafloww-ray-cluster-worker-group-worker-l7ngv" not found
8m48s       Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
8m47s       Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 833ms (833ms including waiting). Image size: ********** bytes.
8m47s       Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Created container: wait-gcs-ready
8m47s       Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Started container wait-gcs-ready
8m31s       Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
8m30s       Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 768ms (768ms including waiting). Image size: ********** bytes.
8m30s       Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Created container: ray-worker
8m30s       Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Started container ray-worker
8m22s       Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
8m22s       Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      (combined from similar events): Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-kpd82
8m22s       Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
8m21s       Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Started container wait-gcs-ready
8m21s       Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Started container wait-gcs-ready
8m21s       Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Created container: wait-gcs-ready
8m21s       Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Created container: wait-gcs-ready
8m21s       Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 815ms (815ms including waiting). Image size: ********** bytes.
8m21s       Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 755ms (755ms including waiting). Image size: ********** bytes.
8m3s        Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Started container ray-worker
8m3s        Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
8m3s        Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 703ms (703ms including waiting). Image size: ********** bytes.
8m3s        Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Created container: ray-worker
8m1s        Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
8m          Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Started container ray-worker
8m          Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Created container: ray-worker
8m          Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 814ms (814ms including waiting). Image size: ********** bytes.
7m53s       Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
5m55s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Stopping container ray-worker
5m51s       Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Readiness probe failed:
5m50s       Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-kpd82; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 08:08:56 +0000 UTC,FinishedAt:2025-06-26 08:11:08 +0000 UTC,ContainerID:containerd://59a3ac04cc97d7a0ff2598bc1490665b9145a62e51ca59f967be7c939e535ea6,}, pods "terrafloww-ray-cluster-worker-group-worker-kpd82" not found
5m50s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Stopping container ray-worker
5m50s       Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
5m49s       Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Started container wait-gcs-ready
5m49s       Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Created container: wait-gcs-ready
5m49s       Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 780ms (780ms including waiting). Image size: ********** bytes.
5m49s       Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Readiness probe failed:
5m45s       Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-5wvkr; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 08:08:59 +0000 UTC,FinishedAt:2025-06-26 08:11:13 +0000 UTC,ContainerID:containerd://2adb682bcdd74f589d2e91fa70949da081f00527727ecb7da1312b804dc30851,}, pods "terrafloww-ray-cluster-worker-group-worker-5wvkr" not found
5m45s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Stopping container ray-worker
5m43s       Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Readiness probe failed:
5m40s       Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-dk2rf; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 08:08:29 +0000 UTC,FinishedAt:2025-06-26 08:11:18 +0000 UTC,ContainerID:containerd://5e4b5a879c95e84bd2b6bda99e3907d1b98e2fbe13950a8f1c5be9b1dfadde08,}, pods "terrafloww-ray-cluster-worker-group-worker-dk2rf" not found
5m32s       Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
5m31s       Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 744ms (744ms including waiting). Image size: ********** bytes.
5m31s       Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Created container: ray-worker
5m31s       Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Started container ray-worker
5m24s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Stopping container ray-worker
5m22s       Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Readiness probe failed:
