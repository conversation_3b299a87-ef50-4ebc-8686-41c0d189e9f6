[36m(process_batch_on_worker pid=104, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=104, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=104, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=104, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,660 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,660 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,661 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,661 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,662 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=104, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=104, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=104, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=104, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=104, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=104, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=104, ip=************)[0m label: string
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=104, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=104, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=104, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=104, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,662 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,687 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
INFO:app.flight_server:Finished receiving data for job_13880687081e4fd99f7e5248e3680d67 from one worker.
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:02,755 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:02,720 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f792b5eb0b0>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:02,720 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f792b5d78d0> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:02,793 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f798876c200>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:02,794 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:02,794 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:02,794 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:02,794 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:02,794 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:02,795 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,783 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,801 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,802 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,812 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,813 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,813 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,813 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,814 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,930 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,930 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,962 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241213_0_L2A/B04.tif with 1 requests.
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,962 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241213_0_L2A/B08.tif with 1 requests.
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,962 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching data for 2 URLs concurrently...
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:02,963 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:02,962 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f792b5ea930>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:02,962 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:02,963 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:02,963 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:02,963 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:02,963 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:03,019 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'B0Dq+fGT4ocZqYXzG6RLS0IyqrvyGJUirmo1rk/iERjfJkGVhCbRffPRJv4a+sc9yGeMyY9JxGs='), (b'x-amz-request-id', b'KNC5ZMCZ7CT59KQW'), (b'Date', b'Thu, 26 Jun 2025 08:10:03 GMT'), (b'Last-Modified', b'Mon, 23 Dec 2024 10:14:53 GMT'), (b'ETag', b'"c986c279cea47caa6cf1b7bff8831c24-29"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 156200179-157805747/242784066'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1605569'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:03,019 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241223_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:03,020 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:03,067 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:03,068 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:03,068 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:03,068 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:03,068 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:03,068 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,040 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f798876d7f0>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,040 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f798289d7d0> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:03,180 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,195 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f18db9b7b90>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,195 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f18dba4e4d0> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,279 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f798876d9a0>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,279 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,280 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,280 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,280 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,280 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,284 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'D2F6+zGPhSfJE/j4uygdPwV4vB5gllxNKIDZEJvYwMBW9eWnyl8iLKQIADRUS3neOtMcijB44Bc='), (b'x-amz-request-id', b'KNC5R22ZWNB4GJKR'), (b'Date', b'Thu, 26 Jun 2025 08:10:03 GMT'), (b'Last-Modified', b'Wed, 18 Dec 2024 11:03:39 GMT'), (b'ETag', b'"01c7acfa974adae0ff3169bc3cacacc9-27"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 143030413-144532953/224488304'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1502541'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,285 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2B_43PGQ_20241218_1_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,285 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:03,424 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'bYA2bS3O4raTDlrWrgP8V1ViB2dFjKbW+le4Y9YRoL8jsNmSvJ+BHKbXMPi3KYXItBBBkdeMpeo='), (b'x-amz-request-id', b'Y2EAHSV68GYY3SZ3'), (b'Date', b'Thu, 26 Jun 2025 08:10:04 GMT'), (b'Last-Modified', b'Mon, 23 Dec 2024 10:12:56 GMT'), (b'ETag', b'"8243ac57280621f89b001762d5faa4c1-28"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 153214381-154705854/234433263'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1491474'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:03,425 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241223_0_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:03,425 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,424 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f18db9e16a0>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,425 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,426 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,426 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,426 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,427 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,427 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,703 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f18f5e52210>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,703 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f18dba4e4d0> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,751 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'7IcbjnP6rhMw8xCoBE4V6HDlmhsR99Q2bVZOLwzBMyfPXTB9G7cqtFQuZHqPJyomt8GUQl/khK4='), (b'x-amz-request-id', b'Y2E69TVWKWTZ7WB4'), (b'Date', b'Thu, 26 Jun 2025 08:10:04 GMT'), (b'Last-Modified', b'Wed, 18 Dec 2024 11:05:39 GMT'), (b'ETag', b'"700d4a5ad4d41d21341f3a83c048c708-27"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 139452711-140867305/219809175'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1414595'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,751 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2B_43PGQ_20241218_1_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:03,751 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,846 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=83, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=83, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=83, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=83, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=83, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=83, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=83, ip=************)[0m label: string
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=83, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=83, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: float
INFO:app.flight_server:Received do_put data for execution: job_13880687081e4fd99f7e5248e3680d67
INFO:app.flight_server:Finished receiving data for job_13880687081e4fd99f7e5248e3680d67 from one worker.
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,852 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'HiZKU8jHJTXfdzzGzHY3+YnJnVU9SMUQNs1hsPhPjTBNGwoIDrNwXunrtmHUYe7jPUvDBJcBHJc='), (b'x-amz-request-id', b'Y2EF122GV4SHPY9Q'), (b'Date', b'Thu, 26 Jun 2025 08:10:04 GMT'), (b'Last-Modified', b'Fri, 13 Dec 2024 10:28:39 GMT'), (b'ETag', b'"1397455dda5b574077a475c825441b73-20"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 110501330-111421043/161177233'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'919714'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,852 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241213_0_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,852 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,897 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,897 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,911 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,912 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,912 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=83, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=83, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=83, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=83, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=83, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=83, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=83, ip=************)[0m label: string
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=83, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=83, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,912 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,925 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,930 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,938 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,938 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,943 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,944 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,945 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,945 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:10:03,945 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,946 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f18dbb40f50>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,946 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,947 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,947 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,947 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:03,947 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
INFO:app.flight_server:Received do_put data for execution: job_13880687081e4fd99f7e5248e3680d67
INFO:app.flight_server:Finished receiving data for job_13880687081e4fd99f7e5248e3680d67 from one worker.
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,090 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=84, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=84, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=84, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=84, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=84, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=84, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=84, ip=************)[0m label: string
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=84, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=84, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,128 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,128 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,130 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,130 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,130 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=84, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=84, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=84, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=84, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=84, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=84, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=84, ip=************)[0m label: string
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=84, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=84, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,130 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,140 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,145 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,153 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,153 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,155 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,155 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,155 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,155 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:10:04,156 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:04,396 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'T2NOcTnse+HkNJ1vHOqqhWYuBDU/TZSTB31WwCh77Kus9eh5q2CJB+4Kxsub9rpzvMhDlibY9Lg='), (b'x-amz-request-id', b'9P5EFDKPNMKE9C36'), (b'Date', b'Thu, 26 Jun 2025 08:10:05 GMT'), (b'Last-Modified', b'Fri, 13 Dec 2024 10:31:20 GMT'), (b'ETag', b'"eaf9e07049f4640a8d9d92b19bbbdf95-20"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 111367062-112313146/163079548'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'946085'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:04,396 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241213_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:04,397 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:05,494 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:05,494 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:05,495 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:05,833 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:05,834 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:05,834 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,063 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,063 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,064 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,065 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,086 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,106 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,110 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,110 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,111 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,111 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,111 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,113 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,113 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,436 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,437 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,437 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,593 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,613 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2B_43PGQ_20241208_0_L2A_T0507_a8c5
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,613 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,613 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:06,613 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f490bb0f4c0>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,699 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,699 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,699 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,700 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,718 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,739 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,742 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,742 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,743 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,743 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,743 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,744 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:06,745 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
INFO:app.flight_server:Received do_put data for execution: job_13880687081e4fd99f7e5248e3680d67
INFO:app.flight_server:Finished receiving data for job_13880687081e4fd99f7e5248e3680d67 from one worker.
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,018 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,018 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,018 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,018 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,018 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,018 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,079 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,304 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,334 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2A_43PGQ_20241203_0_L2A_T0507_5fa0
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,334 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,334 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,334 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f20b5d3c540>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,663 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=84, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=84, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=84, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=84, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=84, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=84, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=84, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=84, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=84, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,694 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,694 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,694 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,695 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,695 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=84, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=84, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=84, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=84, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=84, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=84, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=84, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=84, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=84, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,695 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,701 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,704 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,707 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,707 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,708 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,709 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,709 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,709 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:10:07,709 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,806 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,807 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,807 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,807 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,807 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,807 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:07,878 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,506 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=83, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=83, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=83, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=83, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=83, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=83, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,542 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,542 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,543 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,543 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,543 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=83, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=83, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=83, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=83, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=83, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=83, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,543 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
INFO:app.flight_server:Received do_put data for execution: job_13880687081e4fd99f7e5248e3680d67
INFO:app.flight_server:Finished receiving data for job_13880687081e4fd99f7e5248e3680d67 from one worker.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,552 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,555 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,562 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,562 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,563 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,564 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,564 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,564 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:10:08,564 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:08,944 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:08,944 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:08,945 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,225 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,225 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,226 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,228 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,248 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,271 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,274 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,274 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,275 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,275 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,276 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,277 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,277 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,592 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,592 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,592 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,593 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,629 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,656 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2B_43PGQ_20241228_0_L2A_T0507_b46a
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,656 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,656 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:09,656 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f4dc07e4220>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,606 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,625 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,629 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,629 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,630 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,630 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,630 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,632 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,632 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:09,702 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:09,702 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:09,702 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:09,861 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:09,861 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:09,861 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,955 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,972 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2A_43PGQ_20241213_0_L2A_T0507_b815
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,972 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,973 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:09,973 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f18f5e78540>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,150 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,150 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,150 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,151 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,167 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,188 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,191 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,191 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,192 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,192 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,192 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,193 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,193 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,161 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,161 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,161 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,161 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,161 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,161 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,211 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,312 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,312 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,312 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,313 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,350 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,374 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,377 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,377 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,378 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,378 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,378 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,379 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,379 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,329 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,329 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,329 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,329 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,329 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,329 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,380 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
INFO:app.flight_server:Received do_put data for execution: job_13880687081e4fd99f7e5248e3680d67
INFO:app.flight_server:Finished receiving data for job_13880687081e4fd99f7e5248e3680d67 from one worker.
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,514 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,530 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2A_43PGQ_20241223_0_L2A_T0507_28e2
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,530 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,530 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,530 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f7946070680>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,709 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,720 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=103, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=103, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=103, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=103, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=103, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=103, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=103, ip=************)[0m label: string
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=103, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=103, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=103, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=103, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,729 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2B_43PGQ_20241218_1_L2A_T0507_9250
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,729 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,729 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:10,729 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f798e2eaca0>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,749 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,749 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,749 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,750 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,750 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=103, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=103, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=103, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=103, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=103, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=103, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=103, ip=************)[0m label: string
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=103, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=103, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=103, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=103, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=103, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=103, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=103, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,750 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,758 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,763 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,769 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,769 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,770 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,770 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,771 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,771 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=103, ip=************)[0m 2025-06-26 01:10:10,771 - httpcore.connection - DEBUG - close.complete
INFO:app.flight_server:Received do_put data for execution: job_13880687081e4fd99f7e5248e3680d67
INFO:app.flight_server:Finished receiving data for job_13880687081e4fd99f7e5248e3680d67 from one worker.
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,864 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,864 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,864 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,864 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,864 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,864 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,852 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=104, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=104, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=104, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=104, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=104, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=104, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=104, ip=************)[0m label: string
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=104, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=104, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=104, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=104, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,880 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,881 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,881 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,881 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,881 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=104, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=104, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=104, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=104, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=104, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=104, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=104, ip=************)[0m label: string
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=104, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=104, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=104, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=104, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=104, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=104, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=104, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,881 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,889 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,897 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,904 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,904 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,906 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,907 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,907 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,907 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=104, ip=************)[0m 2025-06-26 01:10:10,908 - httpcore.connection - DEBUG - close.complete
INFO:app.flight_server:Received do_put data for execution: job_13880687081e4fd99f7e5248e3680d67
INFO:app.flight_server:Finished receiving data for job_13880687081e4fd99f7e5248e3680d67 from one worker.
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:10,944 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,115 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,115 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,115 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,115 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,115 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,115 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,170 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,422 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,451 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,452 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,453 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,453 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,453 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=1026, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=1026, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=1026, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,453 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,475 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,479 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,484 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,484 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,485 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,485 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,486 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,486 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=1026, ip=***********)[0m 2025-06-26 01:10:11,486 - httpcore.connection - DEBUG - close.complete
INFO:app.flight_server:Received do_put data for execution: job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,617 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,645 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,645 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,645 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,646 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_13880687081e4fd99f7e5248e3680d67
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,646 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=1025, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=1025, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=1025, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,646 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=1025, ip=***********)[0m 2025-06-26 01:10:11,651 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
INFO:app.flight_server:Finished receiving data for job_13880687081e4fd99f7e5248e3680d67 from one worker.
INFO:terrafloww.engine_core.runtime_ray.driver:All 72 workers for job_13880687081e4fd99f7e5248e3680d67 completed successfully.
INFO:app.flight_server:Received COMPLETION signal for execution: job_13880687081e4fd99f7e5248e3680d67
INFO:terrafloww.engine_core.runtime_ray.driver:Signaled completion to Flight server for execution job_13880687081e4fd99f7e5248e3680d67
INFO:app.flight_server:Execution job_13880687081e4fd99f7e5248e3680d67 completed. Streaming results.
INFO:app.flight_server:Streaming table with 72 rows.
