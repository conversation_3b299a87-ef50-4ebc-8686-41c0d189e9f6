# Ray Actor Integration Analysis
**Date**: 2025-06-26  
**Focus**: Practical integration of Ray actor optimizations with current codebase

## Current Architecture Reality Check

### 🏗️ **Service Lifecycle & Integration Points**

#### Current Flow
```
gRPC Request → RayDriver (singleton) → Ray Tasks → Flight Server
     ↓              ↓                    ↓            ↓
  main.py    get_driver_instance()  process_batch_on_worker  Results Cache
```

#### Current Ray Configuration
```yaml
# ray-cluster.yaml
rayStartParams:
  num-cpus: '2'        # ❌ Should be 4 for target performance
resources:
  limits:
    cpu: "2"           # ❌ Should be 4
    memory: "4Gi"      # ❌ Should be 8Gi for connection pools
```

#### Current Worker Pattern
```python
# worker.py - Line 47
DEFAULT_WORKER_RESOURCES = {"num_cpus": 1}  # ❌ Underutilized

@ray.remote(**DEFAULT_WORKER_RESOURCES)
def process_batch_on_worker(...):
    # ❌ Creates new httpx.AsyncClient per task
    async with httpx.AsyncClient(http2=True, timeout=60.0) as client:
        # Process and discard client
```

## 🚨 **Integration Challenges Identified**

### 1. **Singleton Driver vs Actor Pool Management**
**Problem**: Current singleton driver doesn't manage persistent actors
```python
# Current: driver.py lines 278-290
_driver_instance = None  # Singleton pattern

async def get_driver_instance():
    if _driver_instance is None:
        _driver_instance = RayDriver()
        await _driver_instance._ensure_ray_initialized(ray_address)
```

**Solution**: Driver needs to manage actor pool lifecycle
```python
class RayDriver:
    def __init__(self):
        self.http_actor_pool = None  # NEW: Actor pool management
    
    async def _ensure_actor_pool_initialized(self):
        """Initialize persistent HTTP actor pool"""
        if self.http_actor_pool is None:
            self.http_actor_pool = HTTPActorPool.remote(pool_size=4)
            await self.http_actor_pool.initialize_pool.remote()
```

### 2. **Task-Based vs Actor-Based Execution**
**Problem**: Current execution submits individual Ray tasks
```python
# Current: driver.py lines 174-186
for spatial_key, window_data in windows_to_process:
    task_futures.append(
        process_batch_on_worker.remote(  # ❌ Ray task, not actor
            specs_for_chunk,
            plan_apply_steps_serializable,
            execution_id,
            flight_address
        )
    )
```

**Solution**: Submit work to persistent actor pool
```python
# NEW: Actor-based execution
for spatial_key, window_data in windows_to_process:
    actor = await self.http_actor_pool.get_next_actor.remote()
    task_futures.append(
        actor.process_window_specs.remote(  # ✅ Actor method
            specs_for_chunk,
            plan_apply_steps_serializable,
            execution_id,
            flight_address
        )
    )
```

### 3. **Service Startup vs Actor Initialization**
**Problem**: When do actors get created and initialized?

**Current Service Startup**:
```python
# main.py - Services start concurrently
async def serve():
    grpc_server = grpc.aio.server()
    flight_server_instance = FlightServer(location=flight_location)
    # Ray initialization happens on first request
```

**Solution**: Actor initialization during service startup
```python
async def serve():
    # Initialize services
    grpc_server = grpc.aio.server()
    flight_server_instance = FlightServer(location=flight_location)
    
    # NEW: Pre-initialize Ray actor pool
    driver = await get_driver_instance()
    await driver._ensure_actor_pool_initialized()
    
    logger.info("Ray actor pool pre-initialized")
```

## ✅ **Practical Integration Strategy**

### Phase 1: Minimal Disruption Actor Migration

#### 1. **Extend Current Driver (No Breaking Changes)**
```python
# driver.py - Add actor pool management to existing RayDriver
class RayDriver:
    def __init__(self):
        self.output_storage_path = os.environ.get("TFW_OUTPUT_STORAGE_PATH", "/tmp/terrafloww/results")
        self.http_actor_pool = None  # NEW: Add actor pool
        self.use_actors = os.environ.get("TFW_USE_RAY_ACTORS", "false").lower() == "true"  # Feature flag
    
    async def _ensure_actor_pool_initialized(self):
        """Initialize actor pool if using actors"""
        if self.use_actors and self.http_actor_pool is None:
            self.http_actor_pool = HTTPActorPool.remote(pool_size=4)
            await self.http_actor_pool.initialize_pool.remote()
            logger.info("Ray HTTP actor pool initialized")
    
    async def execute_workflow(self, execution_id: str, plan):
        # Existing logic...
        
        if self.use_actors:
            await self._ensure_actor_pool_initialized()
            # Use actor-based execution
            task_futures = await self._execute_with_actors(windows_to_process, ...)
        else:
            # Use existing task-based execution
            task_futures = self._execute_with_tasks(windows_to_process, ...)
```

#### 2. **Create Actor Pool Implementation**
```python
# NEW: runtime_ray/actor_pool.py
@ray.remote
class HTTPActorPool:
    def __init__(self, pool_size: int = 4):
        self.pool_size = pool_size
        self.actors = []
        self.round_robin_index = 0
    
    async def initialize_pool(self):
        """Create persistent HTTP actors"""
        for i in range(self.pool_size):
            actor_name = f"http_worker_{i}"
            try:
                # Try to get existing persistent actor
                actor = ray.get_actor(actor_name)
                logger.info(f"Retrieved existing actor {actor_name}")
            except ValueError:
                # Create new persistent actor
                actor = HTTPWorkerActor.options(
                    name=actor_name,
                    lifetime="detached"
                ).remote(i)
                await actor.initialize.remote()
                logger.info(f"Created new actor {actor_name}")
            
            self.actors.append(actor)
    
    async def get_next_actor(self):
        """Round-robin actor selection"""
        actor = self.actors[self.round_robin_index]
        self.round_robin_index = (self.round_robin_index + 1) % len(self.actors)
        return actor
```

#### 3. **Create HTTP Worker Actor**
```python
# NEW: runtime_ray/http_actor.py
@ray.remote(max_restarts=2)
class HTTPWorkerActor:
    def __init__(self, actor_id: int):
        self.actor_id = actor_id
        self.client = None
        self.stats = {"requests": 0, "bytes_fetched": 0}
    
    async def initialize(self):
        """CRITICAL: Async HTTP client initialization"""
        self.client = httpx.AsyncClient(
            limits=httpx.Limits(
                max_keepalive_connections=50,
                max_connections=100,
                keepalive_expiry=300.0
            ),
            http2=True,
            timeout=httpx.Timeout(30.0, connect=10.0)
        )
        logger.info(f"Actor {self.actor_id} HTTP client initialized")
    
    async def process_window_specs(self, window_specs_batch, plan_apply_steps_serializable, execution_id, flight_address):
        """Process window specs with persistent HTTP client"""
        # Reuse existing worker logic but with persistent self.client
        # Group by URL for connection reuse
        url_groups = self._group_by_url(window_specs_batch)
        
        # Process each URL group with persistent connection
        for url, specs in url_groups.items():
            # Merge byte ranges for efficiency
            merged_ranges = self._merge_byte_ranges(specs)
            # Fetch with persistent client
            await self._fetch_and_process_url_group(url, specs, merged_ranges)
        
        # Upload to Flight server
        return await self._upload_results(execution_id, flight_address)
```

### Phase 2: Configuration & Deployment

#### 4. **Update Ray Cluster Configuration**
```yaml
# ray-cluster.yaml - Optimize for HTTP workloads
workerGroupSpecs:
- replicas: 1
  minReplicas: 1
  maxReplicas: 3  # Fewer, more powerful workers
  rayStartParams:
    num-cpus: '4'  # ✅ Match target 4-CPU performance
    object-store-memory: '2147483648'  # 2GB for COG caching
  template:
    spec:
      containers:
      - name: ray-worker
        resources:
          limits:
            cpu: "4"       # ✅ 4 CPUs per worker
            memory: "8Gi"  # ✅ More memory for connection pools
          requests:
            cpu: "3"
            memory: "6Gi"
        env:
        - name: TFW_USE_RAY_ACTORS  # ✅ Feature flag
          value: "true"
```

#### 5. **Environment Variable Configuration**
```bash
# Enable actor-based execution
TFW_USE_RAY_ACTORS=true

# Ray cluster configuration
RAY_ADDRESS=auto

# HTTP optimization settings
TFW_HTTP_POOL_SIZE=4
TFW_HTTP_MAX_CONNECTIONS=50
TFW_HTTP_KEEPALIVE_EXPIRY=300
```

## 🎯 **Rollout Strategy**

### Week 1: Foundation (No Breaking Changes)
1. **Add actor pool classes** (new files, no existing code changes)
2. **Add feature flag to driver** (backward compatible)
3. **Update Ray cluster config** (4 CPUs, 8GB memory)
4. **Deploy with TFW_USE_RAY_ACTORS=false** (existing behavior)

### Week 2: Gradual Migration
1. **Enable actors in staging** (TFW_USE_RAY_ACTORS=true)
2. **Performance testing** (compare task vs actor performance)
3. **Monitor connection pooling** (verify persistent HTTP clients)
4. **Tune actor pool size** (optimize for workload)

### Week 3: Production Rollout
1. **Enable actors in production** (TFW_USE_RAY_ACTORS=true)
2. **Monitor performance metrics** (target 6-second NDVI processing)
3. **Remove task-based fallback** (clean up old code)
4. **Add advanced optimizations** (concurrency groups, health monitoring)

## 🔧 **Modularity Assessment**

### ✅ **Good Modularity**
- **Driver singleton pattern** - Easy to extend with actor pool
- **Worker function isolation** - Can be converted to actor methods
- **Environment-based configuration** - Feature flags work well
- **Flight server integration** - No changes needed

### ⚠️ **Modularity Gaps**
- **HTTP client lifecycle** - Currently created/destroyed per task
- **Resource configuration** - Hardcoded in multiple places
- **Error handling** - No persistent connection health monitoring
- **Metrics collection** - No actor-level performance tracking

## 📊 **Expected Integration Impact**

### Minimal Risk Changes
- **Add new actor classes** (no existing code modification)
- **Feature flag in driver** (backward compatible)
- **Environment variable configuration** (no code changes)

### Medium Risk Changes  
- **Ray cluster resource allocation** (requires pod restart)
- **Actor pool initialization** (new startup dependency)
- **HTTP client lifecycle** (different error patterns)

### High Impact Benefits
- **10x+ performance improvement** (persistent HTTP connections)
- **Better resource utilization** (4-CPU workers, connection pooling)
- **Fault tolerance** (actor restart, connection health monitoring)
- **Scalability foundation** (ready for tabular data features)

The integration is **highly feasible** with your current architecture. The singleton driver pattern and environment-based configuration provide excellent hooks for actor pool management without breaking existing functionality.
