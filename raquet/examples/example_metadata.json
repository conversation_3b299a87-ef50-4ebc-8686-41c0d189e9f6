{"compression": "gzip", "block_resolution": 13, "minresolution": 5, "maxresolution": 13, "nodata": 255, "bounds": [5.537109375000016, 47.12995075666304, 15.600585937500009, 55.07836723201513], "center": [10.568847656250156, 51.10415899434061, 13], "width": 58624, "height": 73984, "block_width": 256, "block_height": 256, "num_blocks": 66181, "num_pixels": 4337238016, "pixel_resolution": 21, "bands": [{"type": "uint8", "name": "band_1", "stats": {"min": 10, "max": 60, "mean": 30.888911620295, "stddev": 21.079634678067, "sum": 30060, "sum_squares": 88816, "count": 1000, "quantiles": {"3": [10, 40], "4": [10, 20, 60], "5": [10, 20, 20, 60], "6": [10, 10, 20, 40, 60], "7": [10, 10, 20, 20, 50, 60], "8": [10, 10, 20, 20, 40, 60, 60], "9": [10, 10, 10, 20, 20, 40, 60, 60], "10": [10, 10, 10, 20, 20, 20, 50, 60, 60], "11": [10, 10, 10, 20, 20, 20, 40, 60, 60, 60], "12": [10, 10, 10, 10, 20, 20, 20, 40, 60, 60, 60], "13": [10, 10, 10, 10, 20, 20, 20, 30, 50, 60, 60, 60], "14": [10, 10, 10, 10, 20, 20, 20, 20, 40, 50, 60, 60, 60], "15": [10, 10, 10, 10, 10, 20, 20, 20, 20, 40, 60, 60, 60, 60], "16": [10, 10, 10, 10, 10, 20, 20, 20, 20, 40, 40, 60, 60, 60, 60], "17": [10, 10, 10, 10, 10, 10, 20, 20, 20, 20, 40, 50, 60, 60, 60, 60], "18": [10, 10, 10, 10, 10, 10, 20, 20, 20, 20, 30, 40, 50, 60, 60, 60, 60], "19": [10, 10, 10, 10, 10, 10, 20, 20, 20, 20, 20, 40, 40, 60, 60, 60, 60, 60]}, "top_values": {"10": 355, "20": 250, "30": 17, "40": 68, "50": 32, "60": 278}, "version": "0.9.1", "approximated_stats": true}, "colorinterp": "palette", "nodata": "255", "colortable": {"0": [0, 0, 0, 255], "1": [0, 0, 0, 255], "2": [0, 0, 0, 255], "3": [0, 0, 0, 255], "4": [0, 0, 0, 255], "5": [0, 0, 0, 255], "6": [0, 0, 0, 255], "7": [0, 0, 0, 255], "8": [0, 0, 0, 255], "9": [0, 0, 0, 255], "10": [4, 135, 29, 255], "11": [17, 143, 39, 255], "12": [30, 152, 50, 255], "13": [43, 161, 61, 255], "14": [57, 169, 72, 255], "15": [70, 178, 83, 255], "16": [83, 187, 93, 255], "17": [97, 195, 104, 255], "18": [110, 204, 115, 255], "19": [123, 213, 126, 255], "20": [137, 222, 137, 255], "21": [125, 201, 144, 255], "22": [113, 180, 152, 255], "23": [101, 159, 160, 255], "24": [88, 138, 167, 255], "25": [76, 116, 175, 255], "26": [64, 95, 183, 255], "27": [51, 74, 190, 255], "28": [39, 53, 198, 255], "29": [27, 32, 206, 255], "30": [14, 10, 214, 255], "31": [35, 19, 204, 255], "32": [57, 29, 193, 255], "33": [78, 39, 183, 255], "34": [100, 49, 172, 255], "35": [121, 59, 162, 255], "36": [143, 69, 151, 255], "37": [164, 79, 141, 255], "38": [186, 89, 130, 255], "39": [207, 99, 120, 255], "40": [229, 109, 109, 255], "41": [225, 116, 106, 255], "42": [220, 123, 103, 255], "43": [215, 130, 100, 255], "44": [210, 137, 97, 255], "45": [205, 144, 93, 255], "46": [200, 151, 90, 255], "47": [195, 158, 87, 255], "48": [190, 165, 84, 255], "49": [185, 172, 81, 255], "50": [180, 180, 77, 255], "51": [185, 185, 72, 255], "52": [190, 190, 67, 255], "53": [195, 195, 62, 255], "54": [200, 200, 57, 255], "55": [205, 205, 51, 255], "56": [210, 210, 46, 255], "57": [215, 215, 41, 255], "58": [220, 220, 36, 255], "59": [225, 225, 31, 255], "60": [231, 231, 25, 255], "61": [0, 0, 0, 255], "62": [0, 0, 0, 255], "63": [0, 0, 0, 255], "64": [0, 0, 0, 255], "65": [0, 0, 0, 255], "66": [0, 0, 0, 255], "67": [0, 0, 0, 255], "68": [0, 0, 0, 255], "69": [0, 0, 0, 255], "70": [0, 0, 0, 255], "71": [0, 0, 0, 255], "72": [0, 0, 0, 255], "73": [0, 0, 0, 255], "74": [0, 0, 0, 255], "75": [0, 0, 0, 255], "76": [0, 0, 0, 255], "77": [0, 0, 0, 255], "78": [0, 0, 0, 255], "79": [0, 0, 0, 255], "80": [0, 0, 0, 255], "81": [0, 0, 0, 255], "82": [0, 0, 0, 255], "83": [0, 0, 0, 255], "84": [0, 0, 0, 255], "85": [0, 0, 0, 255], "86": [0, 0, 0, 255], "87": [0, 0, 0, 255], "88": [0, 0, 0, 255], "89": [0, 0, 0, 255], "90": [0, 0, 0, 255], "91": [0, 0, 0, 255], "92": [0, 0, 0, 255], "93": [0, 0, 0, 255], "94": [0, 0, 0, 255], "95": [0, 0, 0, 255], "96": [0, 0, 0, 255], "97": [0, 0, 0, 255], "98": [0, 0, 0, 255], "99": [0, 0, 0, 255], "100": [0, 0, 0, 255], "101": [0, 0, 0, 255], "102": [0, 0, 0, 255], "103": [0, 0, 0, 255], "104": [0, 0, 0, 255], "105": [0, 0, 0, 255], "106": [0, 0, 0, 255], "107": [0, 0, 0, 255], "108": [0, 0, 0, 255], "109": [0, 0, 0, 255], "110": [0, 0, 0, 255], "111": [0, 0, 0, 255], "112": [0, 0, 0, 255], "113": [0, 0, 0, 255], "114": [0, 0, 0, 255], "115": [0, 0, 0, 255], "116": [0, 0, 0, 255], "117": [0, 0, 0, 255], "118": [0, 0, 0, 255], "119": [0, 0, 0, 255], "120": [0, 0, 0, 255], "121": [0, 0, 0, 255], "122": [0, 0, 0, 255], "123": [0, 0, 0, 255], "124": [0, 0, 0, 255], "125": [0, 0, 0, 255], "126": [0, 0, 0, 255], "127": [0, 0, 0, 255], "128": [0, 0, 0, 255], "129": [0, 0, 0, 255], "130": [0, 0, 0, 255], "131": [0, 0, 0, 255], "132": [0, 0, 0, 255], "133": [0, 0, 0, 255], "134": [0, 0, 0, 255], "135": [0, 0, 0, 255], "136": [0, 0, 0, 255], "137": [0, 0, 0, 255], "138": [0, 0, 0, 255], "139": [0, 0, 0, 255], "140": [0, 0, 0, 255], "141": [0, 0, 0, 255], "142": [0, 0, 0, 255], "143": [0, 0, 0, 255], "144": [0, 0, 0, 255], "145": [0, 0, 0, 255], "146": [0, 0, 0, 255], "147": [0, 0, 0, 255], "148": [0, 0, 0, 255], "149": [0, 0, 0, 255], "150": [0, 0, 0, 255], "151": [0, 0, 0, 255], "152": [0, 0, 0, 255], "153": [0, 0, 0, 255], "154": [0, 0, 0, 255], "155": [0, 0, 0, 255], "156": [0, 0, 0, 255], "157": [0, 0, 0, 255], "158": [0, 0, 0, 255], "159": [0, 0, 0, 255], "160": [0, 0, 0, 255], "161": [0, 0, 0, 255], "162": [0, 0, 0, 255], "163": [0, 0, 0, 255], "164": [0, 0, 0, 255], "165": [0, 0, 0, 255], "166": [0, 0, 0, 255], "167": [0, 0, 0, 255], "168": [0, 0, 0, 255], "169": [0, 0, 0, 255], "170": [0, 0, 0, 255], "171": [0, 0, 0, 255], "172": [0, 0, 0, 255], "173": [0, 0, 0, 255], "174": [0, 0, 0, 255], "175": [0, 0, 0, 255], "176": [0, 0, 0, 255], "177": [0, 0, 0, 255], "178": [0, 0, 0, 255], "179": [0, 0, 0, 255], "180": [0, 0, 0, 255], "181": [0, 0, 0, 255], "182": [0, 0, 0, 255], "183": [0, 0, 0, 255], "184": [0, 0, 0, 255], "185": [0, 0, 0, 255], "186": [0, 0, 0, 255], "187": [0, 0, 0, 255], "188": [0, 0, 0, 255], "189": [0, 0, 0, 255], "190": [0, 0, 0, 255], "191": [0, 0, 0, 255], "192": [0, 0, 0, 255], "193": [0, 0, 0, 255], "194": [0, 0, 0, 255], "195": [0, 0, 0, 255], "196": [0, 0, 0, 255], "197": [0, 0, 0, 255], "198": [0, 0, 0, 255], "199": [0, 0, 0, 255], "200": [0, 0, 0, 255], "201": [0, 0, 0, 255], "202": [0, 0, 0, 255], "203": [0, 0, 0, 255], "204": [0, 0, 0, 255], "205": [0, 0, 0, 255], "206": [0, 0, 0, 255], "207": [0, 0, 0, 255], "208": [0, 0, 0, 255], "209": [0, 0, 0, 255], "210": [0, 0, 0, 255], "211": [0, 0, 0, 255], "212": [0, 0, 0, 255], "213": [0, 0, 0, 255], "214": [0, 0, 0, 255], "215": [0, 0, 0, 255], "216": [0, 0, 0, 255], "217": [0, 0, 0, 255], "218": [0, 0, 0, 255], "219": [0, 0, 0, 255], "220": [0, 0, 0, 255], "221": [0, 0, 0, 255], "222": [0, 0, 0, 255], "223": [0, 0, 0, 255], "224": [0, 0, 0, 255], "225": [0, 0, 0, 255], "226": [0, 0, 0, 255], "227": [0, 0, 0, 255], "228": [0, 0, 0, 255], "229": [0, 0, 0, 255], "230": [0, 0, 0, 255], "231": [0, 0, 0, 255], "232": [0, 0, 0, 255], "233": [0, 0, 0, 255], "234": [0, 0, 0, 255], "235": [0, 0, 0, 255], "236": [0, 0, 0, 255], "237": [0, 0, 0, 255], "238": [0, 0, 0, 255], "239": [0, 0, 0, 255], "240": [0, 0, 0, 255], "241": [0, 0, 0, 255], "242": [0, 0, 0, 255], "243": [0, 0, 0, 255], "244": [0, 0, 0, 255], "245": [0, 0, 0, 255], "246": [0, 0, 0, 255], "247": [0, 0, 0, 255], "248": [0, 0, 0, 255], "249": [0, 0, 0, 255], "250": [0, 0, 0, 255], "251": [0, 0, 0, 255], "252": [0, 0, 0, 255], "253": [0, 0, 0, 255], "254": [0, 0, 0, 255], "255": [0, 0, 0, 0]}}]}