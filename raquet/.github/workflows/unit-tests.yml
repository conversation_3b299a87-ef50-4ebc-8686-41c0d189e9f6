name: Unit Tests

on:
  push:
    branches: [ main ]
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'
        
    - name: Install GDAL dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y gdal-bin libgdal-dev
        
    - name: Install Python packages
      run: |
        python -m pip install --upgrade pip
        python -m pip install GDAL==$(gdal-config --version)
        python -m pip install ruff .
        
    - name: Run unit tests
      run: |
        python -m unittest raquet-tests/test-geotiff2raquet.py
        python -m unittest raquet-tests/test-raquet2geotiff.py

    - name: Check formatting
      run: |
        ruff check raquet
        ruff check raquet-tests
