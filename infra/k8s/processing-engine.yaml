# SPDX-FileCopyrightText: Terrafloww Labs, 2025

apiVersion: apps/v1
kind: Deployment
metadata:
  name: processing-engine
  namespace: terrafloww-platform
  labels:
    app: processing-engine
    component: data-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: processing-engine
  template:
    metadata:
      labels:
        app: processing-engine
        component: data-backend
    spec:
      # Schedule on data pool nodes
      nodeSelector:
        doks.digitalocean.com/node-pool: data-pool-e0y1e6npd
      tolerations:
      - key: workload
        operator: Equal
        value: data
        effect: NoSchedule
      imagePullSecrets:
      - name: registry-terrafloww-dev
      containers:
      - name: processing-engine
        image: registry.digitalocean.com/terrafloww-dev/processing-engine:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 50051
          name: grpc
          protocol: TCP
        - containerPort: 50052
          name: flight
          protocol: TCP
        env:
        - name: GRPC_PORT
          value: "50051"
        - name: FLIGHT_PORT
          value: "50052"
        - name: FLIGHT_SERVER_HOST
          value: "0.0.0.0"
        - name: RAY_ADDRESS
          value: "terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379"
        - name: PYTHONPATH
          value: "/app"
        resources:
          requests:
            cpu: "500m"
            memory: "1Gi"
          limits:
            cpu: "2"
            memory: "4Gi"
        readinessProbe:
          exec:
            command:
            - python
            - -c
            - "import grpc; import sys; sys.exit(0)"
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          exec:
            command:
            - python
            - -c
            - "import grpc; import sys; sys.exit(0)"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: processing-engine-svc
  namespace: terrafloww-platform
  labels:
    app: processing-engine
    component: data-backend
spec:
  selector:
    app: processing-engine
  ports:
  - name: grpc
    port: 50051
    targetPort: 50051
    protocol: TCP
  - name: flight
    port: 50052
    targetPort: 50052
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: processing-engine-external
  namespace: terrafloww-platform
  labels:
    app: processing-engine
    component: data-backend
spec:
  selector:
    app: processing-engine
  ports:
  - name: grpc
    port: 50051
    targetPort: 50051
    protocol: TCP
    nodePort: 30051
  - name: flight
    port: 50052
    targetPort: 50052
    protocol: TCP
    nodePort: 30052
  type: NodePort
