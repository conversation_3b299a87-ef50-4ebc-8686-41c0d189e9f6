Name:             terrafloww-ray-cluster-head-2lndk
Namespace:        terrafloww-platform
Priority:         0
Service Account:  terrafloww-ray-cluster
Node:             data-pool-e0y1e6npd-t0isx/10.122.0.15
Start Time:       Tue, 24 Jun 2025 15:32:05 +0530
Labels:           app=terrafloww
                  app.kubernetes.io/created-by=kuberay-operator
                  app.kubernetes.io/name=kuberay
                  component=ray-head
                  ray.io/cluster=terrafloww-ray-cluster
                  ray.io/group=headgroup
                  ray.io/identifier=terrafloww-ray-cluster-head
                  ray.io/is-ray-node=yes
                  ray.io/node-type=head
Annotations:      ray.io/ft-enabled: false
Status:           Running
IP:               ************
IPs:
  IP:           ************
Controlled By:  RayCluster/terrafloww-ray-cluster
Containers:
  ray-head:
    Container ID:  containerd://51a51458589b69ba66bc5f96951e4c560a359e7d6c414faaab0c155abf42494e
    Image:         registry.digitalocean.com/terrafloww-dev/ray-custom:**********
    Image ID:      registry.digitalocean.com/terrafloww-dev/ray-custom@sha256:****************************************************************
    Ports:         6379/TCP, 8265/TCP, 10001/TCP, 8080/TCP
    Host Ports:    0/TCP, 0/TCP, 0/TCP, 0/TCP
    Command:
      /bin/bash
      -lc
      --
    Args:
      ulimit -n 65536; ray start --head  --block  --dashboard-agent-listen-port=52365  --dashboard-host=0.0.0.0  --dashboard-port=8265  --memory=**********  --metrics-export-port=8080  --no-monitor  --num-cpus=0  --object-store-memory=********* 
    State:          Running
      Started:      Tue, 24 Jun 2025 15:32:17 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     2
      memory:  6Gi
    Requests:
      cpu:      1
      memory:   4Gi
    Liveness:   exec [bash -c wget -T 2 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success && wget -T 10 -q -O- http://localhost:8265/api/gcs_healthz | grep success] delay=30s timeout=5s period=5s #success=1 #failure=120
    Readiness:  exec [bash -c wget -T 4 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success && wget -T 10 -q -O- http://localhost:8265/api/gcs_healthz | grep success] delay=0s timeout=10s period=5s #success=1 #failure=12
    Environment:
      RAY_DISABLE_IMPORT_WARNING:                1
      RAY_DEDUP_LOGS:                            0
      RAY_DASHBOARD_METRICS_COLLECTION_ENABLED:  0
      RAY_USAGE_STATS_ENABLED:                   0
      FLIGHT_HOST:                               ************
      FLIGHT_PORT:                               50052
      RAY_CLUSTER_NAME:                           (v1:metadata.labels['ray.io/cluster'])
      RAY_CLOUD_INSTANCE_ID:                     terrafloww-ray-cluster-head-2lndk (v1:metadata.name)
      RAY_NODE_TYPE_NAME:                         (v1:metadata.labels['ray.io/group'])
      KUBERAY_GEN_RAY_START_CMD:                 ray start --head  --block  --dashboard-agent-listen-port=52365  --dashboard-host=0.0.0.0  --dashboard-port=8265  --memory=**********  --metrics-export-port=8080  --no-monitor  --num-cpus=0  --object-store-memory=********* 
      RAY_PORT:                                  6379
      RAY_ADDRESS:                               127.0.0.1:6379
      RAY_USAGE_STATS_KUBERAY_IN_USE:            1
      RAY_USAGE_STATS_EXTRA_TAGS:                kuberay_version=v1.3.2;kuberay_crd=RayCluster
      RAY_DASHBOARD_ENABLE_K8S_DISK_USAGE:       1
    Mounts:
      /dev/shm from shared-mem (rw)
      /tmp/ray from ray-logs (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-868pn (ro)
  autoscaler:
    Container ID:  containerd://f398e4e80c6fb0db25969c63a7a84b0bedc656b06ebf0b9eaf537404648f898f
    Image:         registry.digitalocean.com/terrafloww-dev/ray-custom:**********
    Image ID:      registry.digitalocean.com/terrafloww-dev/ray-custom@sha256:****************************************************************
    Port:          <none>
    Host Port:     <none>
    Command:
      /bin/bash
      -lc
      --
    Args:
      ray kuberay-autoscaler --cluster-name $(RAY_CLUSTER_NAME) --cluster-namespace $(RAY_CLUSTER_NAMESPACE)
    State:          Running
      Started:      Tue, 24 Jun 2025 15:32:17 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     500m
      memory:  512Mi
    Requests:
      cpu:     500m
      memory:  512Mi
    Environment:
      RAY_CLUSTER_NAME:        (v1:metadata.labels['ray.io/cluster'])
      RAY_CLUSTER_NAMESPACE:  terrafloww-platform (v1:metadata.namespace)
      RAY_HEAD_POD_NAME:      terrafloww-ray-cluster-head-2lndk (v1:metadata.name)
      KUBERAY_CRD_VER:        v1
    Mounts:
      /tmp/ray from ray-logs (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-868pn (ro)
Conditions:
  Type                        Status
  PodReadyToStartContainers   True 
  Initialized                 True 
  Ready                       True 
  ContainersReady             True 
  PodScheduled                True 
Volumes:
  ray-logs:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     
    SizeLimit:  <unset>
  shared-mem:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     Memory
    SizeLimit:  4Gi
  kube-api-access-868pn:
    Type:                    Projected (a volume that contains injected data from multiple sources)
    TokenExpirationSeconds:  3607
    ConfigMapName:           kube-root-ca.crt
    Optional:                false
    DownwardAPI:             true
QoS Class:                   Burstable
Node-Selectors:              doks.digitalocean.com/node-pool=data-pool-e0y1e6npd
Tolerations:                 node.kubernetes.io/not-ready:NoExecute op=Exists for 300s
                             node.kubernetes.io/unreachable:NoExecute op=Exists for 300s
                             workload=data:NoSchedule
Events:                      <none>
