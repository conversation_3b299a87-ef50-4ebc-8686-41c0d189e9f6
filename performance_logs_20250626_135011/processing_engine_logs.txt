[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,492 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,492 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=83, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=83, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=83, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=83, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=83, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=83, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,492 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,497 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,500 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,504 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,504 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,505 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,506 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,506 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,506 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,506 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:32,482 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f63d94dd250>
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:32,482 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f63d43f5350> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:32,483 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f75b91aac30>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:32,483 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f75bd6002d0> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,566 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting async processing for 2 window specs
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,566 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic starting for 2 window specs.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,577 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241213_0_L2A/B04.tif with 1 requests.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,577 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Creating fetch coroutine for https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241213_0_L2A/B08.tif with 1 requests.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,577 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching data for 2 URLs concurrently...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,578 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:32,620 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'fTDiMJl4+BLf9gT5MT5KrhJYXygWfdDHhc2+hAKEdWD8xmp0KNQIeLRXu47msgq+wsSc9djQc2uQVsPDQSR+lQ=='), (b'x-amz-request-id', b'GDFNFPJSEKVJMJVY'), (b'Date', b'Thu, 26 Jun 2025 08:19:33 GMT'), (b'Last-Modified', b'Tue, 03 Dec 2024 09:32:35 GMT'), (b'ETag', b'"3d01405050e70f995a5c9b3d59df3774-24"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 127801761-129113657/194047402'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1311897'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:32,621 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241203_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:32,621 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:32,603 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:32,619 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2B_43PGQ_20241208_0_L2A_T0507_b502
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:32,619 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:32,619 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:32,619 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f222e36c540>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:32,717 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f75b91a9880>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:32,718 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:32,719 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:32,719 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:32,719 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:32,719 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:32,719 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,816 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f0185e9a4b0>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,816 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f0185f03250> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:32,726 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f63d94de990>
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:32,726 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:32,727 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:32,727 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:32,727 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:32,727 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:32,743 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'Wq5wKjjj+++iFJYvAtfnP/iah8c1iQSeTxvKOYmWJxYY5qU4lz+kTK7p34FTpkGQJ+zTLd7VJ9g='), (b'x-amz-request-id', b'GDFYRRGE51BYWRTM'), (b'Date', b'Thu, 26 Jun 2025 08:19:33 GMT'), (b'Last-Modified', b'Mon, 23 Dec 2024 10:14:53 GMT'), (b'ETag', b'"c986c279cea47caa6cf1b7bff8831c24-29"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes *********-*********/*********'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1605569'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:32,744 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241223_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:32,744 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
INFO:app.flight_server:Received do_put data for execution: job_b63a48ee72064fe8ba40c0ab19611555
INFO:app.flight_server:Finished receiving data for job_b63a48ee72064fe8ba40c0ab19611555 from one worker.
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,814 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=1358, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=1358, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=1358, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=1358, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=1358, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=1358, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=1358, ip=************)[0m label: string
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=1358, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=1358, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,842 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,842 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,843 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,843 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,844 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=1358, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=1358, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=1358, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=1358, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=1358, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=1358, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=1358, ip=************)[0m label: string
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=1358, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=1358, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=1358, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1358, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=1358, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=1358, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,844 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,851 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,857 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,863 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,863 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,864 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,865 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,865 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,865 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=1358, ip=************)[0m 2025-06-26 01:19:32,865 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,862 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'Qz+RFaT9vOm0R4DEdVgclnY0Y908fzDc8bb0yLGcmiQ9v9WorZ1x7gTVrhBnukeF7qj9BYr/IlFzuYSw7KOONw=='), (b'x-amz-request-id', b'GDFPYFT6RVRWK5E8'), (b'Date', b'Thu, 26 Jun 2025 08:19:33 GMT'), (b'Last-Modified', b'Sat, 28 Dec 2024 08:26:43 GMT'), (b'ETag', b'"2b3ce1a4e4e7e9da25b143e089ea2d73-29"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 152899458-154512916/239471564'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1613459'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,863 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2B_43PGQ_20241228_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:32,863 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:32,968 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f75b91aa270>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:32,968 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f75bd6002d0> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,016 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,017 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,017 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,017 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,017 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,017 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,088 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,050 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f01866bf0e0>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,051 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,051 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,051 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,051 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,051 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,052 - httpcore.connection - DEBUG - connect_tcp.started host='sentinel-cogs.s3.us-west-2.amazonaws.com' port=443 local_address=None timeout=60.0 socket_options=None
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,137 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'qnN+zmo1LrRYtPiHP0w1868chAQ/90qct+ELsmUZoBCwxv4aiM4+ZoqjogFFtBMqprlRAueM7M93rmefuY4Kbg=='), (b'x-amz-request-id', b'GDFH5HX0P586FPK3'), (b'Date', b'Thu, 26 Jun 2025 08:19:33 GMT'), (b'Last-Modified', b'Wed, 18 Dec 2024 11:03:39 GMT'), (b'ETag', b'"01c7acfa974adae0ff3169bc3cacacc9-27"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 143030413-144532953/224488304'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1502541'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,138 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2B_43PGQ_20241218_1_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,138 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:33,143 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'htcG0AXaY//mj5rxVc8chM5RNyCfgKuWDudy62sY4jRoJz1tnqcdQ7vRTs083neMt1G8WqKL8BQ='), (b'x-amz-request-id', b'GDFRFZXMK5Z52X12'), (b'Date', b'Thu, 26 Jun 2025 08:19:33 GMT'), (b'Last-Modified', b'Mon, 23 Dec 2024 10:12:56 GMT'), (b'ETag', b'"8243ac57280621f89b001762d5faa4c1-28"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 153214381-154705854/234433263'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1491474'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:33,144 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241223_0_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:33,144 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,211 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f75b91aa750>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,211 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,212 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,212 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,212 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,212 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,285 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f0185e9b860>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,285 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f0185f03250> server_hostname='sentinel-cogs.s3.us-west-2.amazonaws.com' timeout=60.0
INFO:app.flight_server:Received do_put data for execution: job_b63a48ee72064fe8ba40c0ab19611555
INFO:app.flight_server:Finished receiving data for job_b63a48ee72064fe8ba40c0ab19611555 from one worker.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,514 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x7f0185e9a480>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,515 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,515 - httpcore.http11 - DEBUG - send_request_headers.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,515 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,516 - httpcore.http11 - DEBUG - send_request_body.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,516 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,549 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'5hehN28+0FttI0wo5Zjd83sUz8iwAoUxY/VwK0PbQn9t5phbp5DJIfYN7cGHlsyaj1F4LBBnlbJaGCBjKsKf+Q=='), (b'x-amz-request-id', b'0PDECN2JYKFTDC2G'), (b'Date', b'Thu, 26 Jun 2025 08:19:34 GMT'), (b'Last-Modified', b'Wed, 18 Dec 2024 11:05:39 GMT'), (b'ETag', b'"700d4a5ad4d41d21341f3a83c048c708-27"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 139452711-140867305/219809175'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'1414595'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,550 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2B_43PGQ_20241218_1_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:33,550 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,605 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=84, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=84, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=84, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=84, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=84, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=84, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=84, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=84, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=84, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,571 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'JPUkM1RuCugv14j3Wk3X7wqw4POfSZ7r5K+oOr5KCNId0iy1L6VYYGHfH9SnqhAWDamCBrI/DZE='), (b'x-amz-request-id', b'0PD30WHTNG4QQ91V'), (b'Date', b'Thu, 26 Jun 2025 08:19:34 GMT'), (b'Last-Modified', b'Fri, 13 Dec 2024 10:28:39 GMT'), (b'ETag', b'"1397455dda5b574077a475c825441b73-20"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 110501330-111421043/161177233'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'919714'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,571 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241213_0_L2A/B04.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,571 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,633 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,634 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,635 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,635 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,635 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=84, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=84, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=84, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=84, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=84, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=84, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=84, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=84, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=84, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=84, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=84, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=84, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,635 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,640 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,642 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,647 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,647 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,648 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,648 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,648 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,648 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=84, ip=***********)[0m 2025-06-26 01:19:33,649 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,667 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,667 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,668 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,669 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,687 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,707 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,710 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,711 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,712 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,712 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,712 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,713 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:33,713 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:33,791 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:33,791 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:33,792 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,917 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 206, b'Partial Content', [(b'x-amz-id-2', b'lHyQ1r8WI4hOa/TGxjqzAj2fzYkxmP9FRERlpN5LXuEgQKDp8xJXToB5ZcHS1dOw2+RC0Qj5CSQ='), (b'x-amz-request-id', b'0PDFD2ZMWK032NKN'), (b'Date', b'Thu, 26 Jun 2025 08:19:34 GMT'), (b'Last-Modified', b'Fri, 13 Dec 2024 10:31:20 GMT'), (b'ETag', b'"eaf9e07049f4640a8d9d92b19bbbdf95-20"'), (b'x-amz-storage-class', b'INTELLIGENT_TIERING'), (b'x-amz-server-side-encryption', b'AES256'), (b'Cache-Control', b'public, max-age=31536000, immutable'), (b'Accept-Ranges', b'bytes'), (b'Content-Range', b'bytes 111367062-112313146/163079548'), (b'Content-Type', b'image/tiff; application=geotiff; profile=cloud-optimized'), (b'Content-Length', b'946085'), (b'Server', b'AmazonS3')])
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,918 - httpx - INFO - HTTP Request: GET https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/P/GQ/2024/12/S2A_43PGQ_20241213_0_L2A/B08.tif "HTTP/1.1 206 Partial Content"
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:33,918 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,013 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,028 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2A_43PGQ_20241123_0_L2A_T0507_57d6
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,028 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,028 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,028 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7fb34068c680>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:34,220 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:34,221 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:34,221 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,332 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,332 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,332 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,332 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,332 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,332 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,298 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,298 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,298 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,384 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,752 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,753 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,753 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,753 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,775 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,837 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=84, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=84, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=84, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=84, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=84, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=84, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=84, ip=************)[0m label: string
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=84, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=84, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: float
INFO:app.flight_server:Received do_put data for execution: job_b63a48ee72064fe8ba40c0ab19611555
INFO:app.flight_server:Finished receiving data for job_b63a48ee72064fe8ba40c0ab19611555 from one worker.
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,800 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,803 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,803 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,804 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,804 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,804 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,805 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:34,805 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,866 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,866 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,867 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,867 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,867 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=84, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=84, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=84, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=84, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=84, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=84, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=84, ip=************)[0m label: string
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=84, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=84, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=84, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=84, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=84, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=84, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=84, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,867 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,873 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,876 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,881 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,881 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,882 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,882 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,883 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,883 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=84, ip=************)[0m 2025-06-26 01:19:34,883 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,106 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,123 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2B_43PGQ_20241218_1_L2A_T0507_5551
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,123 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,123 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,123 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f75beb185e0>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
INFO:app.flight_server:Received do_put data for execution: job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:35,274 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:35,275 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:35,275 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,461 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,462 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,462 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,462 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,462 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,462 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,518 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:35,978 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=1357, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=1357, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=1357, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=1357, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=1357, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=1357, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=1357, ip=************)[0m label: string
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=1357, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=1357, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,007 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,007 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,009 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,009 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,009 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=1357, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=1357, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=1357, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=1357, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=1357, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=1357, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=1357, ip=************)[0m label: string
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=1357, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=1357, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=1357, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=1357, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=1357, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=1357, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,009 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,016 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
INFO:app.flight_server:Finished receiving data for job_b63a48ee72064fe8ba40c0ab19611555 from one worker.
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,021 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,035 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,035 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,037 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,037 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,038 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,038 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=1357, ip=************)[0m 2025-06-26 01:19:36,038 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,620 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,621 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,621 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,621 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,637 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,655 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,658 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,659 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,659 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,659 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,659 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,660 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,660 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,941 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,958 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2A_43PGQ_20241203_0_L2A_T0507_61f2
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,958 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,959 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:38,959 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7fe23a3637e0>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,202 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,202 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,202 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,203 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,220 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,241 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,244 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,244 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,245 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,245 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,245 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,245 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,245 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,252 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,252 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,252 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,252 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,252 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,252 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,305 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
INFO:app.flight_server:Received do_put data for execution: job_b63a48ee72064fe8ba40c0ab19611555
INFO:app.flight_server:Finished receiving data for job_b63a48ee72064fe8ba40c0ab19611555 from one worker.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,341 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,341 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,341 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,343 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,362 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,387 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,394 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,394 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,394 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,394 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,395 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,397 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,397 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:39,587 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:39,587 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:39,588 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,653 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,676 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2B_43PGQ_20241228_0_L2A_T0507_f500
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,676 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,676 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,676 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f94a06834c0>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,757 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,772 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2A_43PGQ_20241213_0_L2A_T0507_512d
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,772 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,772 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:39,772 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f01a0363740>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,729 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=83, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=83, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=83, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=83, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=83, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=83, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=83, ip=************)[0m label: string
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=83, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=83, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,757 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,757 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,759 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,759 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,759 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=83, ip=************)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=83, ip=************)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=83, ip=************)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: double
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=83, ip=************)[0m crs: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=83, ip=************)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=83, ip=************)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: string
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=83, ip=************)[0m label: string
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=83, ip=************)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=83, ip=************)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=83, ip=************)[0m       child 1, value: double
[36m(process_batch_on_worker pid=83, ip=************)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=************)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=83, ip=************)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=83, ip=************)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,759 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,768 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,771 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,776 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,777 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,778 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,778 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,778 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,778 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=************)[0m 2025-06-26 01:19:39,779 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m /home/<USER>/anaconda3/lib/python3.12/site-packages/terrafloww/engine_core/process.py:178: RuntimeWarning: divide by zero encountered in divide
[36m(process_batch_on_worker pid=83, ip=***********)[0m   ndvi_np = np.where(denominator != 0, (nir_data - red_data) / denominator, 0)
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:39,977 - httpcore.http11 - DEBUG - receive_response_body.complete
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:39,977 - httpcore.http11 - DEBUG - response_closed.started
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:39,978 - httpcore.http11 - DEBUG - response_closed.complete
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:39,978 - terrafloww.engine_core.runtime_ray.worker - INFO - Fetching complete.
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:39,997 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,045 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,045 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,045 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,045 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,045 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,045 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,093 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,093 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,093 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,093 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,093 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,093 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,022 - terrafloww.engine_core.process - DEBUG - Applying Horizontal predictor (predictor=2)
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,026 - terrafloww.engine_core.runtime_ray.worker - INFO - Stacking bands and applying kernels for 1 spatial chunks...
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,026 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker Kernel Registry Contents: ['terrafloww.spectral.ndvi']
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,027 - terrafloww.engine_core.utils - DEBUG - >>> Entering _np_chunk_to_arrow_list_components
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,028 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Input chunk_array shape=(2, 779, 335), dtype=float32
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,028 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Target Arrow type=float, Target NumPy dtype=<class 'numpy.float32'>
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,029 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: flat_values dtype=float32, size=521930
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,029 - terrafloww.engine_core.utils - DEBUG - _np_chunk_to_arrow: Created values_array type=float
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,114 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,153 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,338 - terrafloww.engine_core.runtime_ray.worker - INFO - Creating batch with collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,355 - terrafloww.engine_core.runtime_ray.worker - INFO - Applying kernel 'terrafloww.spectral.ndvi' to chunk S2A_43PGQ_20241223_0_L2A_T0507_5a1b
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,355 - terrafloww.engine_core.runtime_ray.worker - INFO - Available bands in data: ['nir', 'red']
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,355 - terrafloww.engine_core.registry - INFO - Retrieved function 'terrafloww.spectral.ndvi' with signature: _ndvi_kernel{'batch': <class 'pyarrow.lib.RecordBatch'>, 'red_band': <class 'str'>, 'nir_band': <class 'str'>, 'return': <class 'pyarrow.lib.RecordBatch'>}
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,355 - terrafloww.engine_core.runtime_ray.worker - INFO - Attempting to call kernel: ID='terrafloww.spectral.ndvi', Function object: <function _ndvi_kernel at 0x7f63da860680>, Params: {'red_band': 'red', 'nir_band': 'nir'}, Type of Params: <class 'dict'>
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,601 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=83, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=83, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=83, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=83, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=83, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=83, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
INFO:app.flight_server:Received do_put data for execution: job_b63a48ee72064fe8ba40c0ab19611555
INFO:app.flight_server:Finished receiving data for job_b63a48ee72064fe8ba40c0ab19611555 from one worker.
INFO:app.flight_server:Received do_put data for execution: job_b63a48ee72064fe8ba40c0ab19611555
INFO:app.flight_server:Finished receiving data for job_b63a48ee72064fe8ba40c0ab19611555 from one worker.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,640 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=83, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=83, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=83, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=83, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=83, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=83, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,670 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,670 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,671 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,671 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,672 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=83, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=83, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=83, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=83, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=83, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=83, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,672 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,684 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,688 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,631 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,631 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,632 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,633 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,633 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=83, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=83, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=83, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=83, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=83, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=83, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=83, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=83, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=83, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=83, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=83, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=83, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,633 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,639 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,642 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,646 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,647 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,651 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,651 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,652 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,652 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,652 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,715 - terrafloww.engine_core.process - INFO - NDVI kernel: Processing collection: sentinel-2-l2a
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,715 - terrafloww.engine_core.process - INFO - NDVI kernel: Available bands: ['nir', 'red']
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,715 - terrafloww.engine_core.process - INFO - NDVI kernel: Requested bands - red: red, nir: nir
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,716 - terrafloww.engine_core.process - INFO - NDVI kernel: Band index mapping: {'nir': 0, 'b08': 0, 'red': 1, 'b04': 1}
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,716 - terrafloww.engine_core.process - INFO - NDVI kernel: Found red band at index 1 (name: red)
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,716 - terrafloww.engine_core.process - INFO - NDVI kernel: Found nir band at index 0 (name: nir)
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,691 - terrafloww.engine_core.runtime_ray.worker - INFO - Writer closed successfully
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,691 - terrafloww.engine_core.runtime_ray.worker - INFO - Successfully wrote batch of 1 rows to Flight
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,693 - terrafloww.engine_core.runtime_ray.worker - INFO - Worker async logic finished. Uploaded batch with 1 rows.
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,693 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,693 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,693 - httpcore.connection - DEBUG - close.started
[36m(process_batch_on_worker pid=83, ip=***********)[0m 2025-06-26 01:19:40,694 - httpcore.connection - DEBUG - close.complete
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:40,787 - terrafloww.engine_core.runtime_ray.worker - INFO - Kernel 'terrafloww.spectral.ndvi' added column 'ndvi' with type list<item: float>
INFO:app.flight_server:Received do_put data for execution: job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:41,288 - terrafloww.engine_core.runtime_ray.worker - DEBUG - Final assembled schema for batch: chunk_id: string not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=82, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=82, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=82, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=82, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=82, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=82, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=82, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=82, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=82, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:41,324 - terrafloww.engine_core.runtime_ray.worker - INFO - Streaming results via Flight to given address: grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:41,324 - terrafloww.engine_core.runtime_ray.worker - INFO - Uploading batch with 1 rows for execution job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:41,325 - terrafloww.engine_core.runtime_ray.worker - INFO - Connected to Flight server at grpc+tcp://terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:41,325 - terrafloww.engine_core.runtime_ray.worker - INFO - Created descriptor for execution_id: job_b63a48ee72064fe8ba40c0ab19611555
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:41,326 - terrafloww.engine_core.runtime_ray.worker - INFO - Created table with 1 rows and schema: chunk_id: string not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Unique ID for this processed multi-band chunk'
[36m(process_batch_on_worker pid=82, ip=***********)[0m raster_data: list<item: float> not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Flattened raster data (C*H*W) as float32'
[36m(process_batch_on_worker pid=82, ip=***********)[0m shape: fixed_size_list<item: int32>[3] not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, item: int32
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Shape [Channels, Height, Width]'
[36m(process_batch_on_worker pid=82, ip=***********)[0m bounds: fixed_size_list<item: double>[4] not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, item: double
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Chunk bounds [minx, miny, maxx, maxy]'
[36m(process_batch_on_worker pid=82, ip=***********)[0m crs: string not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Coordinate Reference System'
[36m(process_batch_on_worker pid=82, ip=***********)[0m datetime: timestamp[us, tz=UTC] not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Observation timestamp'
[36m(process_batch_on_worker pid=82, ip=***********)[0m bands: list<item: string> not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, item: string
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'List of band names in channel order'
[36m(process_batch_on_worker pid=82, ip=***********)[0m label: string
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Optional label'
[36m(process_batch_on_worker pid=82, ip=***********)[0m quality: map<string, double>
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, entries: struct<key: string not null, value: double> not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m       child 0, key: string not null
[36m(process_batch_on_worker pid=82, ip=***********)[0m       child 1, value: double
[36m(process_batch_on_worker pid=82, ip=***********)[0m   -- field metadata --
[36m(process_batch_on_worker pid=82, ip=***********)[0m   description: 'Quality/provenance metrics'
[36m(process_batch_on_worker pid=82, ip=***********)[0m ndvi: list<item: float>
[36m(process_batch_on_worker pid=82, ip=***********)[0m   child 0, item: float
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:41,326 - terrafloww.engine_core.runtime_ray.worker - INFO - Starting do_put call...
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:41,332 - terrafloww.engine_core.runtime_ray.worker - INFO - Got writer from do_put, writing table...
[36m(process_batch_on_worker pid=82, ip=***********)[0m 2025-06-26 01:19:41,335 - terrafloww.engine_core.runtime_ray.worker - INFO - Wrote table, closing writer...
INFO:app.flight_server:Finished receiving data for job_b63a48ee72064fe8ba40c0ab19611555 from one worker.
INFO:terrafloww.engine_core.runtime_ray.driver:All 72 workers for job_b63a48ee72064fe8ba40c0ab19611555 completed successfully.
INFO:app.flight_server:Received COMPLETION signal for execution: job_b63a48ee72064fe8ba40c0ab19611555
INFO:app.flight_server:Execution job_b63a48ee72064fe8ba40c0ab19611555 completed. Streaming results.
INFO:app.flight_server:Streaming table with 72 rows.
INFO:terrafloww.engine_core.runtime_ray.driver:Signaled completion to Flight server for execution job_b63a48ee72064fe8ba40c0ab19611555
