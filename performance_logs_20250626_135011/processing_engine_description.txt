Name:             terrafloww-processing-engine-d677dccb-nlkcp
Namespace:        terrafloww-platform
Priority:         0
Service Account:  default
Node:             data-pool-e0y1e6npd-t0iv9/**********
Start Time:       Thu, 26 Jun 2025 13:16:29 +0530
Labels:           app=terrafloww
                  component=processing-engine
                  pod-template-hash=d677dccb
Annotations:      kubectl.kubernetes.io/restartedAt: 2025-06-24T15:20:02+05:30
Status:           Running
IP:               ***********
IPs:
  IP:           ***********
Controlled By:  ReplicaSet/terrafloww-processing-engine-d677dccb
Containers:
  processing-engine:
    Container ID:   containerd://2cc4e4b982c95a82148c46cdb298eb794cdaa009d73b8b313b087ee7648b1e2e
    Image:          registry.digitalocean.com/terrafloww-dev/processing-engine:24175c2-********-131518
    Image ID:       registry.digitalocean.com/terrafloww-dev/processing-engine@sha256:23566d19cd2de51c78b7762718fc72ab11c9e06e7480e291c75a24cc9a467bfb
    Ports:          50051/TCP, 50052/TCP
    Host Ports:     0/TCP, 0/TCP
    State:          Running
      Started:      Thu, 26 Jun 2025 13:16:38 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     2
      memory:  4Gi
    Requests:
      cpu:      500m
      memory:   1Gi
    Liveness:   exec [python -c import grpc; import sys; sys.exit(0)] delay=30s timeout=10s period=30s #success=1 #failure=3
    Readiness:  exec [python -c import grpc; import sys; sys.exit(0)] delay=10s timeout=5s period=10s #success=1 #failure=3
    Environment:
      GRPC_PORT:                    50051
      FLIGHT_PORT:                  50052
      FLIGHT_SERVER_HOST:           0.0.0.0
      RAY_ADDRESS:                  ray://terrafloww-ray-cluster-head-svc:10001
      RAY_ENABLE_AUTO_CONNECT:      0
      FLIGHT_INTERNAL_SVC_ADDRESS:  terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052
      FLIGHT_CACHE_TTL_SECONDS:     3600
      TFW_OUTPUT_STORAGE_PATH:      /tmp/terrafloww/results
      PYTHONPATH:                   /app
      STAC_CATALOG_S3_BUCKET:       <set to the key 'bucket' in secret 'terrafloww-catalog-secrets'>    Optional: false
      STAC_CATALOG_S3_ENDPOINT:     <set to the key 'endpoint' in secret 'terrafloww-catalog-secrets'>  Optional: false
      STAC_CATALOG_S3_REGION:       <set to the key 'region' in secret 'terrafloww-catalog-secrets'>    Optional: false
      STAC_CATALOG_S3_PATH_PREFIX:  catalog
      DO_ACCESS_KEY_ID:             <set to the key 'access_key_id' in secret 'terrafloww-catalog-secrets'>      Optional: false
      DO_SECRET_ACCESS_KEY:         <set to the key 'secret_access_key' in secret 'terrafloww-catalog-secrets'>  Optional: false
      DO_REGION:                    <set to the key 'region' in secret 'terrafloww-catalog-secrets'>             Optional: false
      DO_SPACE_ENDPOINT:            <set to the key 'endpoint' in secret 'terrafloww-catalog-secrets'>           Optional: false
    Mounts:
      /tmp/terrafloww from tmp-storage (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-68d2x (ro)
Conditions:
  Type                        Status
  PodReadyToStartContainers   True 
  Initialized                 True 
  Ready                       True 
  ContainersReady             True 
  PodScheduled                True 
Volumes:
  tmp-storage:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     
    SizeLimit:  <unset>
  kube-api-access-68d2x:
    Type:                    Projected (a volume that contains injected data from multiple sources)
    TokenExpirationSeconds:  3607
    ConfigMapName:           kube-root-ca.crt
    Optional:                false
    DownwardAPI:             true
QoS Class:                   Burstable
Node-Selectors:              doks.digitalocean.com/node-pool=data-pool-e0y1e6npd
Tolerations:                 node.kubernetes.io/not-ready:NoExecute op=Exists for 300s
                             node.kubernetes.io/unreachable:NoExecute op=Exists for 300s
                             workload=data:NoSchedule
Events:
  Type    Reason     Age   From               Message
  ----    ------     ----  ----               -------
  Normal  Scheduled  33m   default-scheduler  Successfully assigned terrafloww-platform/terrafloww-processing-engine-d677dccb-nlkcp to data-pool-e0y1e6npd-t0iv9
  Normal  Pulling    33m   kubelet            Pulling image "registry.digitalocean.com/terrafloww-dev/processing-engine:24175c2-********-131518"
  Normal  Pulled     33m   kubelet            Successfully pulled image "registry.digitalocean.com/terrafloww-dev/processing-engine:24175c2-********-131518" in 7.718s (7.718s including waiting). Image size: 657869265 bytes.
  Normal  Created    33m   kubelet            Created container: processing-engine
  Normal  Started    33m   kubelet            Started container processing-engine
