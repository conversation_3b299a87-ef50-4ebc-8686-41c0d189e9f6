[2025-06-26 01:18:33,252 W 1 1] global_state_accessor.cc:435: Retrying to get node with node ID ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:18:34,254 W 1 1] global_state_accessor.cc:435: Retrying to get node with node ID ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
2025-06-26 01:18:33,120	INFO scripts.py:1152 -- [37mLocal node IP[39m: [1m10.108.6.199[22m
2025-06-26 01:18:35,309	SUCC scripts.py:1168 -- [32m--------------------[39m
2025-06-26 01:18:35,309	SUCC scripts.py:1169 -- [32mRay runtime started.[39m
2025-06-26 01:18:35,309	SUCC scripts.py:1170 -- [32m--------------------[39m
2025-06-26 01:18:35,309	INFO scripts.py:1172 -- To terminate the Ray runtime, run
2025-06-26 01:18:35,310	INFO scripts.py:1173 -- [1m  ray stop[22m
2025-06-26 01:18:35,310	INFO scripts.py:1181 -- [36m[1m--block[22m[39m
2025-06-26 01:18:35,310	INFO scripts.py:1182 -- This command will now block forever until terminated by a signal.
2025-06-26 01:18:35,310	INFO scripts.py:1185 -- Running subprocesses are monitored and a message will be printed if any of them terminate unexpectedly. Subprocesses exit with SIGTERM will be treated as graceful, thus NOT reported.
