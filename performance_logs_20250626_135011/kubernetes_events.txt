LAST SEEN   TYPE      REASON                    OBJECT                                                 MESSAGE
22m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-xtnmj to data-pool-e0y1e6npd-t0iv9
22m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-w5hjx to data-pool-e0y1e6npd-l3xcq
33m         Normal    Scheduled                 pod/terrafloww-processing-engine-d677dccb-nlkcp        Successfully assigned terrafloww-platform/terrafloww-processing-engine-d677dccb-nlkcp to data-pool-e0y1e6npd-t0iv9
29m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-mww9q   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match <PERSON><PERSON>'s node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
28m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-mww9q   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
28m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mww9q to data-pool-e0y1e6npd-l3xcq
11m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
13m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
13m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-l7ngv to data-pool-e0y1e6npd-l3xcq
11m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-5wvkr to data-pool-e0y1e6npd-t0iv9
11m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-kpd82 to data-pool-e0y1e6npd-l3xcq
2m7s        Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-jt2jz   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-jt2jz to data-pool-e0y1e6npd-l3xcq
13m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
2m7s        Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-gp5jp   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
13m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-fnpff to data-pool-e0y1e6npd-t0iv9
2m7s        Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-dz5l6   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-dz5l6 to data-pool-e0y1e6npd-t0iv9
28m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-85rtz to data-pool-e0y1e6npd-l3xcq
31m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-9gzmp to data-pool-e0y1e6npd-t0iv9
28m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-85rtz   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
46m         Normal    Scheduled                 pod/data-ingestion-stac-20250626-130329-b7rht          Successfully assigned terrafloww-platform/data-ingestion-stac-20250626-130329-b7rht to data-pool-e0y1e6npd-t0iv9
29m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-85rtz   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
29m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-85rtz   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cloudprovider.kubernetes.io/uninitialized: true}, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
12m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-dk2rf to data-pool-e0y1e6npd-l3xcq
2m7s        Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-cdbqq   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-cdbqq to data-pool-e0y1e6npd-t0iv9
31m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-85rtz   0/13 nodes are available: 1 Insufficient cpu, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/13 nodes are available: 11 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
13m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-4nzvf to data-pool-e0y1e6npd-l3xcq
35m         Normal    Scheduled                 pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Successfully assigned terrafloww-platform/terrafloww-processing-engine-555b7f7c4c-bgvj2 to data-pool-e0y1e6npd-t0iv9
116s        Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-gp5jp   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
29m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cloudprovider.kubernetes.io/uninitialized: true}, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
29m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
28m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
28m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
25m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-dqkqp to data-pool-e0y1e6npd-l3xcq
9m8s        Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mb5nw to data-pool-e0y1e6npd-l3xcq
31m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-mww9q   0/13 nodes are available: 1 Insufficient cpu, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/13 nodes are available: 11 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
31m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   0/13 nodes are available: 1 Insufficient cpu, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/13 nodes are available: 11 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
22m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-55f5k   0/14 nodes are available: 1 Insufficient cpu, 3 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 11 Preemption is not helpful for scheduling, 3 No preemption victims found for incoming pod.
22m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-c7jl5 to data-pool-e0y1e6npd-t0iv9
20m         Normal    Scheduled                 pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-55f5k to data-pool-e0y1e6npd-t0iv9
29m         Warning   FailedScheduling          pod/terrafloww-ray-cluster-worker-group-worker-mww9q   0/14 nodes are available: 1 Insufficient cpu, 1 node(s) had untolerated taint {node.cloudprovider.kubernetes.io/uninitialized: true}, 2 Insufficient memory, 3 node(s) had untolerated taint {dedicated: notebook}, 8 node(s) didn't match Pod's node affinity/selector. preemption: 0/14 nodes are available: 12 Preemption is not helpful for scheduling, 2 No preemption victims found for incoming pod.
46m         Normal    SuccessfulCreate          job/data-ingestion-stac-20250626-130329                Created pod: data-ingestion-stac-20250626-130329-b7rht
46m         Normal    Pulling                   pod/data-ingestion-stac-20250626-130329-b7rht          Pulling image "registry.digitalocean.com/terrafloww-dev/data-ingestion:latest"
46m         Normal    Pulled                    pod/data-ingestion-stac-20250626-130329-b7rht          Successfully pulled image "registry.digitalocean.com/terrafloww-dev/data-ingestion:latest" in 15.121s (15.121s including waiting). Image size: 858097857 bytes.
46m         Normal    Created                   pod/data-ingestion-stac-20250626-130329-b7rht          Created container: ingestion
46m         Normal    Started                   pod/data-ingestion-stac-20250626-130329-b7rht          Started container ingestion
44m         Normal    Completed                 job/data-ingestion-stac-20250626-130329                Job completed
35m         Normal    Killing                   pod/terrafloww-processing-engine-555b7f7c4c-5zd2p      Stopping container processing-engine
35m         Normal    SuccessfulCreate          replicaset/terrafloww-processing-engine-555b7f7c4c     Created pod: terrafloww-processing-engine-555b7f7c4c-bgvj2
35m         Normal    Pulling                   pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Pulling image "registry.digitalocean.com/terrafloww-dev/processing-engine:1750759555"
35m         Normal    Pulled                    pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Successfully pulled image "registry.digitalocean.com/terrafloww-dev/processing-engine:1750759555" in 1.203s (1.203s including waiting). Image size: 657939324 bytes.
35m         Normal    Created                   pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Created container: processing-engine
35m         Normal    Started                   pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Started container processing-engine
33m         Normal    SuccessfulCreate          replicaset/terrafloww-processing-engine-d677dccb       Created pod: terrafloww-processing-engine-d677dccb-nlkcp
33m         Normal    ScalingReplicaSet         deployment/terrafloww-processing-engine                Scaled up replica set terrafloww-processing-engine-d677dccb from 0 to 1
33m         Normal    Pulling                   pod/terrafloww-processing-engine-d677dccb-nlkcp        Pulling image "registry.digitalocean.com/terrafloww-dev/processing-engine:24175c2-20250626-131518"
33m         Normal    Created                   pod/terrafloww-processing-engine-d677dccb-nlkcp        Created container: processing-engine
33m         Normal    Pulled                    pod/terrafloww-processing-engine-d677dccb-nlkcp        Successfully pulled image "registry.digitalocean.com/terrafloww-dev/processing-engine:24175c2-20250626-131518" in 7.718s (7.718s including waiting). Image size: 657869265 bytes.
33m         Normal    Started                   pod/terrafloww-processing-engine-d677dccb-nlkcp        Started container processing-engine
33m         Normal    SuccessfulDelete          replicaset/terrafloww-processing-engine-555b7f7c4c     Deleted pod: terrafloww-processing-engine-555b7f7c4c-bgvj2
33m         Normal    Killing                   pod/terrafloww-processing-engine-555b7f7c4c-bgvj2      Stopping container processing-engine
33m         Normal    ScalingReplicaSet         deployment/terrafloww-processing-engine                Scaled down replica set terrafloww-processing-engine-555b7f7c4c from 1 to 0
31m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-dqkqp
31m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-9gzmp
31m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-85rtz
31m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mww9q
31m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Started container wait-gcs-ready
31m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Created container: wait-gcs-ready
31m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 776ms (776ms including waiting). Image size: ********** bytes.
31m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
31m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
31m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 724ms (724ms including waiting). Image size: ********** bytes.
31m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Started container ray-worker
31m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Created container: ray-worker
30m         Normal    TriggeredScaleUp          pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   pod triggered scale-up: [{916f3b54-4b2b-4165-8a5c-96f060b1f76f 2->3 (max: 3)}]
30m         Normal    TriggeredScaleUp          pod/terrafloww-ray-cluster-worker-group-worker-mww9q   pod triggered scale-up: [{916f3b54-4b2b-4165-8a5c-96f060b1f76f 2->3 (max: 3)}]
29m         Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-mww9q   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
29m         Normal    UpdatedLoadBalancer       service/terrafloww-processing-engine-lb                Updated load balancer with new hosts
28m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
28m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
27m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Started container wait-gcs-ready
27m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 56.452s (56.452s including waiting). Image size: ********** bytes.
27m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Created container: wait-gcs-ready
27m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Created container: wait-gcs-ready
27m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Started container wait-gcs-ready
27m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 56.441s (56.441s including waiting). Image size: ********** bytes.
27m         Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
27m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
27m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
27m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 688ms (688ms including waiting). Image size: ********** bytes.
27m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Started container ray-worker
27m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Started container ray-worker
27m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Created container: ray-worker
27m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 1.439s (1.439s including waiting). Image size: ********** bytes.
27m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Created container: ray-worker
25m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Stopping container ray-worker
25m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Stopping container ray-worker
25m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Stopping container ray-worker
25m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-9gzmp
25m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-9gzmp   Readiness probe failed:
25m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-85rtz
25m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mww9q
25m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-85rtz   Readiness probe failed:
25m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-mww9q   Readiness probe failed:
25m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mww9q; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:52:50 +0000 UTC,FinishedAt:2025-06-26 07:54:28 +0000 UTC,ContainerID:containerd://9bad90b4d5527b4b5723737d9762a428bfe4dcf83e8767d2cab258f4824fa4e2,}
25m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
25m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-85rtz; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:52:50 +0000 UTC,FinishedAt:2025-06-26 07:54:28 +0000 UTC,ContainerID:containerd://d6924dc6591a0a85f7be89111d9be8beaeaba889e40d05112108d67ef8d0bb07,}
25m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-9gzmp; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:48:50 +0000 UTC,FinishedAt:2025-06-26 07:54:28 +0000 UTC,ContainerID:containerd://db5a0c2651dce42239a18549d34c214ccca285b012cb1be62f1aac66e43c8965,}
25m         Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-mww9q; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:52:50 +0000 UTC,FinishedAt:2025-06-26 07:54:28 +0000 UTC,ContainerID:containerd://9bad90b4d5527b4b5723737d9762a428bfe4dcf83e8767d2cab258f4824fa4e2,}, pods "terrafloww-ray-cluster-worker-group-worker-mww9q" not found
25m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 930ms (930ms including waiting). Image size: ********** bytes.
25m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Created container: wait-gcs-ready
25m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Started container wait-gcs-ready
25m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
25m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 1.356s (1.356s including waiting). Image size: ********** bytes.
25m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Created container: ray-worker
25m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Started container ray-worker
25m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-lqptq   Stopping container ray-worker
25m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-lqptq
25m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-lqptq   Readiness probe errored: rpc error: code = Unknown desc = failed to exec in container: container is in CONTAINER_EXITED state
25m         Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-lqptq; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-24 10:02:32 +0000 UTC,FinishedAt:2025-06-26 07:54:59 +0000 UTC,ContainerID:containerd://6b6db90588b793e22a22371f497da8ea0860165f8dce93ad5006bde5bd28f0a4,}, pods "terrafloww-ray-cluster-worker-group-worker-lqptq" not found
22m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
22m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Created container: wait-gcs-ready
22m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Started container wait-gcs-ready
22m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 781ms (781ms including waiting). Image size: ********** bytes.
22m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
22m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
22m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Started container wait-gcs-ready
22m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Started container wait-gcs-ready
22m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 697ms (697ms including waiting). Image size: ********** bytes.
22m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Created container: wait-gcs-ready
22m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Created container: wait-gcs-ready
22m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 731ms (731ms including waiting). Image size: ********** bytes.
21m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
21m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Created container: ray-worker
21m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 918ms (918ms including waiting). Image size: ********** bytes.
21m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Started container ray-worker
21m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Started container ray-worker
21m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
21m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 680ms (680ms including waiting). Image size: ********** bytes.
21m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Created container: ray-worker
21m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
21m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Started container ray-worker
21m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Created container: ray-worker
21m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 753ms (753ms including waiting). Image size: ********** bytes.
21m         Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-55f5k   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
20m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Stopping container ray-worker
20m         Normal    DeletedWorkerPod          raycluster/terrafloww-ray-cluster                      Deleted pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-c7jl5
20m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-c7jl5   Readiness probe failed:
20m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-xtnmj   Stopping container ray-worker
20m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Stopping container ray-worker
20m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
20m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 715ms (715ms including waiting). Image size: ********** bytes.
20m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Created container: wait-gcs-ready
20m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Started container wait-gcs-ready
20m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-w5hjx   Readiness probe failed:
20m         Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-xtnmj; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:58:33 +0000 UTC,FinishedAt:2025-06-26 07:59:58 +0000 UTC,ContainerID:containerd://0bd6922f5ff93c092a938745e557e2acf78d7f34ba81e7244e60a3f0754a4fe1,}, pods "terrafloww-ray-cluster-worker-group-worker-xtnmj" not found
20m         Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-w5hjx; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 07:58:31 +0000 UTC,FinishedAt:2025-06-26 08:00:02 +0000 UTC,ContainerID:containerd://d73d00a0495e2840ccbbd5d7bc2979c2c21aadf63cf293c6cf259e9ebb665fb2,}, pods "terrafloww-ray-cluster-worker-group-worker-w5hjx" not found
20m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
20m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Started container ray-worker
20m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Created container: ray-worker
20m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 729ms (729ms including waiting). Image size: ********** bytes.
19m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Stopping container ray-worker
19m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-dqkqp   Readiness probe failed:
13m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
13m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
13m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
13m         Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-fnpff
13m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Created container: wait-gcs-ready
13m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Started container wait-gcs-ready
13m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Created container: wait-gcs-ready
13m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Started container wait-gcs-ready
13m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Created container: wait-gcs-ready
13m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 715ms (715ms including waiting). Image size: ********** bytes.
13m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 734ms (734ms including waiting). Image size: ********** bytes.
13m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 748ms (748ms including waiting). Image size: ********** bytes.
13m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Started container wait-gcs-ready
13m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
13m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Created container: ray-worker
13m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 781ms (781ms including waiting). Image size: ********** bytes.
13m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Started container ray-worker
13m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
13m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
13m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 740ms (740ms including waiting). Image size: ********** bytes.
13m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Started container ray-worker
13m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Created container: ray-worker
13m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 768ms (769ms including waiting). Image size: ********** bytes.
13m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Created container: ray-worker
13m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Started container ray-worker
13m         Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
12m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-l7ngv   Stopping container ray-worker
12m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Stopping container ray-worker
12m         Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Stopping container ray-worker
12m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-fnpff   Readiness probe failed:
12m         Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-l7ngv; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 08:06:44 +0000 UTC,FinishedAt:2025-06-26 08:08:10 +0000 UTC,ContainerID:containerd://122c0113da19d99f84eb92f512aa0b1ccfa36e6fd42715221bf0b85db3548fc1,}, pods "terrafloww-ray-cluster-worker-group-worker-l7ngv" not found
12m         Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-4nzvf   Readiness probe errored: rpc error: code = Unknown desc = failed to exec in container: container is in CONTAINER_EXITED state
12m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
12m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Started container wait-gcs-ready
12m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Created container: wait-gcs-ready
12m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 833ms (833ms including waiting). Image size: ********** bytes.
11m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
11m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Created container: ray-worker
11m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Started container ray-worker
11m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 768ms (768ms including waiting). Image size: ********** bytes.
11m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
11m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
11m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Created container: wait-gcs-ready
11m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 815ms (815ms including waiting). Image size: ********** bytes.
11m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 755ms (755ms including waiting). Image size: ********** bytes.
11m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Started container wait-gcs-ready
11m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Created container: wait-gcs-ready
11m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Started container wait-gcs-ready
11m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Started container ray-worker
11m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Created container: ray-worker
11m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 703ms (703ms including waiting). Image size: ********** bytes.
11m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
11m         Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
11m         Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Started container ray-worker
11m         Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 814ms (814ms including waiting). Image size: ********** bytes.
11m         Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Created container: ray-worker
11m         Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
9m13s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Stopping container ray-worker
9m9s        Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-kpd82   Readiness probe failed:
9m8s        Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-kpd82; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 08:08:56 +0000 UTC,FinishedAt:2025-06-26 08:11:08 +0000 UTC,ContainerID:containerd://59a3ac04cc97d7a0ff2598bc1490665b9145a62e51ca59f967be7c939e535ea6,}, pods "terrafloww-ray-cluster-worker-group-worker-kpd82" not found
9m8s        Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Stopping container ray-worker
9m8s        Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
9m7s        Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Started container wait-gcs-ready
9m7s        Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 780ms (780ms including waiting). Image size: ********** bytes.
9m7s        Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-5wvkr   Readiness probe failed:
9m7s        Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Created container: wait-gcs-ready
9m3s        Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-5wvkr; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 08:08:59 +0000 UTC,FinishedAt:2025-06-26 08:11:13 +0000 UTC,ContainerID:containerd://2adb682bcdd74f589d2e91fa70949da081f00527727ecb7da1312b804dc30851,}, pods "terrafloww-ray-cluster-worker-group-worker-5wvkr" not found
9m3s        Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Stopping container ray-worker
9m1s        Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-dk2rf   Readiness probe failed:
8m58s       Warning   FailedToDeleteWorkerPod   raycluster/terrafloww-ray-cluster                      Failed deleting worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-dk2rf; Pod status: Failed; Pod restart policy: Never; Ray container terminated status: &ContainerStateTerminated{ExitCode:1,Signal:0,Reason:Error,Message:,StartedAt:2025-06-26 08:08:29 +0000 UTC,FinishedAt:2025-06-26 08:11:18 +0000 UTC,ContainerID:containerd://5e4b5a879c95e84bd2b6bda99e3907d1b98e2fbe13950a8f1c5be9b1dfadde08,}, pods "terrafloww-ray-cluster-worker-group-worker-dk2rf" not found
8m50s       Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
8m49s       Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 744ms (744ms including waiting). Image size: ********** bytes.
8m49s       Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Created container: ray-worker
8m49s       Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-mb5nw   Started container ray-worker
8m42s       Normal    Killing                   pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Stopping container ray-worker
8m40s       Warning   Unhealthy                 pod/terrafloww-ray-cluster-worker-group-worker-55f5k   Readiness probe failed:
2m7s        Normal    CreatedWorkerPod          raycluster/terrafloww-ray-cluster                      (combined from similar events): Created worker Pod terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-jt2jz
2m6s        Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-jt2jz   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 697ms (697ms including waiting). Image size: ********** bytes.
2m6s        Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-cdbqq   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
2m6s        Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-jt2jz   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
2m6s        Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-dz5l6   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
2m6s        Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-jt2jz   Created container: wait-gcs-ready
2m5s        Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-cdbqq   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 1.221s (1.221s including waiting). Image size: ********** bytes.
2m5s        Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-cdbqq   Created container: wait-gcs-ready
2m5s        Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-cdbqq   Started container wait-gcs-ready
2m5s        Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-jt2jz   Started container wait-gcs-ready
2m5s        Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-dz5l6   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 764ms (764ms including waiting). Image size: ********** bytes.
2m5s        Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-dz5l6   Created container: wait-gcs-ready
2m5s        Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-dz5l6   Started container wait-gcs-ready
117s        Normal    NotTriggerScaleUp         pod/terrafloww-ray-cluster-worker-group-worker-gp5jp   pod didn't trigger scale-up: 1 node(s) didn't match Pod's node affinity/selector, 1 max node group size reached
108s        Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-jt2jz   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
107s        Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-dz5l6   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
107s        Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-jt2jz   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 765ms (765ms including waiting). Image size: ********** bytes.
107s        Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-jt2jz   Started container ray-worker
107s        Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-jt2jz   Created container: ray-worker
106s        Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-dz5l6   Created container: ray-worker
106s        Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-dz5l6   Started container ray-worker
106s        Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-dz5l6   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 702ms (702ms including waiting). Image size: ********** bytes.
106s        Normal    Pulling                   pod/terrafloww-ray-cluster-worker-group-worker-cdbqq   Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
105s        Normal    Started                   pod/terrafloww-ray-cluster-worker-group-worker-cdbqq   Started container ray-worker
105s        Normal    Pulled                    pod/terrafloww-ray-cluster-worker-group-worker-cdbqq   Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 646ms (646ms including waiting). Image size: ********** bytes.
105s        Normal    Created                   pod/terrafloww-ray-cluster-worker-group-worker-cdbqq   Created container: ray-worker
