Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 56+ pending tasks/actors
2025-06-26 01:18:41,113 - INFO - The autoscaler took 0.046 seconds to complete the update iteration.
2025-06-26 01:18:41,113	INFO autoscaler.py:463 -- The autoscaler took 0.046 seconds to complete the update iteration.
2025-06-26 01:18:41,114 - INFO - :event_summary:Resized to 8 CPUs.
2025-06-26 01:18:41,114	INFO monitor.py:434 -- :event_summary:Resized to 8 CPUs.
2025-06-26 01:18:46,120 - INFO - Refreshing K8s API client token and certs.
2025-06-26 01:18:46,120	INFO node_provider.py:277 -- Refreshing K8s API client token and certs.
2025-06-26 01:18:46,143 - INFO - Refreshing K8s API client token and certs.
2025-06-26 01:18:46,143	INFO node_provider.py:277 -- Refreshing K8s API client token and certs.
2025-06-26 01:18:46,174 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:18:46,174	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:18:46,356 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:18:46,356	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:18:46,405 - INFO - The autoscaler took 0.262 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:18:46,405	INFO autoscaler.py:147 -- The autoscaler took 0.262 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:18:46,406 - INFO - 
======== Autoscaler status: 2025-06-26 01:18:46.406067 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 53+ pending tasks/actors
2025-06-26 01:18:46,406	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:18:46.406067 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 53+ pending tasks/actors
2025-06-26 01:18:46,408 - INFO - The autoscaler took 0.265 seconds to complete the update iteration.
2025-06-26 01:18:46,408	INFO autoscaler.py:463 -- The autoscaler took 0.265 seconds to complete the update iteration.
2025-06-26 01:18:51,459 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:18:51,459	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:18:51,472 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:18:51,472	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:18:51,473 - INFO - The autoscaler took 0.038 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:18:51,473	INFO autoscaler.py:147 -- The autoscaler took 0.038 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:18:51,473 - INFO - 
======== Autoscaler status: 2025-06-26 01:18:51.473879 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 48+ pending tasks/actors
2025-06-26 01:18:51,473	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:18:51.473879 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 48+ pending tasks/actors
2025-06-26 01:18:51,475 - INFO - The autoscaler took 0.041 seconds to complete the update iteration.
2025-06-26 01:18:51,475	INFO autoscaler.py:463 -- The autoscaler took 0.041 seconds to complete the update iteration.
2025-06-26 01:18:56,532 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:18:56,532	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:18:56,547 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:18:56,547	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:18:56,549 - INFO - The autoscaler took 0.044 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:18:56,549	INFO autoscaler.py:147 -- The autoscaler took 0.044 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:18:56,549 - INFO - 
======== Autoscaler status: 2025-06-26 01:18:56.549845 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 41+ pending tasks/actors
2025-06-26 01:18:56,549	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:18:56.549845 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 41+ pending tasks/actors
2025-06-26 01:18:56,552 - INFO - The autoscaler took 0.048 seconds to complete the update iteration.
2025-06-26 01:18:56,552	INFO autoscaler.py:463 -- The autoscaler took 0.048 seconds to complete the update iteration.
2025-06-26 01:19:01,616 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:01,616	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:01,633 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:01,633	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:01,633 - INFO - The autoscaler took 0.045 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:01,633	INFO autoscaler.py:147 -- The autoscaler took 0.045 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:01,634 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:01.634755 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 36+ pending tasks/actors
2025-06-26 01:19:01,634	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:01.634755 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 36+ pending tasks/actors
2025-06-26 01:19:01,637 - INFO - The autoscaler took 0.049 seconds to complete the update iteration.
2025-06-26 01:19:01,637	INFO autoscaler.py:463 -- The autoscaler took 0.049 seconds to complete the update iteration.
2025-06-26 01:19:06,690 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:06,690	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:06,705 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:06,705	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:06,705 - INFO - The autoscaler took 0.042 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:06,705	INFO autoscaler.py:147 -- The autoscaler took 0.042 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:06,706 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:06.706255 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 30+ pending tasks/actors
2025-06-26 01:19:06,706	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:06.706255 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 30+ pending tasks/actors
2025-06-26 01:19:06,708 - INFO - The autoscaler took 0.045 seconds to complete the update iteration.
2025-06-26 01:19:06,708	INFO autoscaler.py:463 -- The autoscaler took 0.045 seconds to complete the update iteration.
2025-06-26 01:19:11,762 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:11,762	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:11,776 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:11,776	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:11,776 - INFO - The autoscaler took 0.04 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:11,776	INFO autoscaler.py:147 -- The autoscaler took 0.04 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:11,777 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:11.777260 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 27+ pending tasks/actors
2025-06-26 01:19:11,777	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:11.777260 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 27+ pending tasks/actors
2025-06-26 01:19:11,779 - INFO - The autoscaler took 0.042 seconds to complete the update iteration.
2025-06-26 01:19:11,779	INFO autoscaler.py:463 -- The autoscaler took 0.042 seconds to complete the update iteration.
2025-06-26 01:19:16,839 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:16,839	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:16,857 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:16,857	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:16,859 - INFO - The autoscaler took 0.049 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:16,859	INFO autoscaler.py:147 -- The autoscaler took 0.049 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:16,859 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:16.859529 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 20+ pending tasks/actors
2025-06-26 01:19:16,859	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:16.859529 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 20+ pending tasks/actors
2025-06-26 01:19:16,861 - INFO - The autoscaler took 0.052 seconds to complete the update iteration.
2025-06-26 01:19:16,861	INFO autoscaler.py:463 -- The autoscaler took 0.052 seconds to complete the update iteration.
2025-06-26 01:19:21,925 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:21,925	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:21,937 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:21,937	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:21,938 - INFO - The autoscaler took 0.044 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:21,938	INFO autoscaler.py:147 -- The autoscaler took 0.044 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:21,939 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:21.939014 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 13+ pending tasks/actors
2025-06-26 01:19:21,939	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:21.939014 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 13+ pending tasks/actors
2025-06-26 01:19:21,940 - INFO - The autoscaler took 0.047 seconds to complete the update iteration.
2025-06-26 01:19:21,940	INFO autoscaler.py:463 -- The autoscaler took 0.047 seconds to complete the update iteration.
2025-06-26 01:19:26,990 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:26,990	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:27,004 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:27,004	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:27,005 - INFO - The autoscaler took 0.039 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:27,005	INFO autoscaler.py:147 -- The autoscaler took 0.039 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:27,005 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:27.005500 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 8+ pending tasks/actors
2025-06-26 01:19:27,005	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:27.005500 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 {'CPU': 1.0}: 8+ pending tasks/actors
2025-06-26 01:19:27,007 - INFO - The autoscaler took 0.041 seconds to complete the update iteration.
2025-06-26 01:19:27,007	INFO autoscaler.py:463 -- The autoscaler took 0.041 seconds to complete the update iteration.
2025-06-26 01:19:32,058 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:32,058	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:32,071 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:32,071	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:32,071 - INFO - The autoscaler took 0.037 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:32,071	INFO autoscaler.py:147 -- The autoscaler took 0.037 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:32,072 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:32.072485 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:32,072	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:32.072485 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 8.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:32,074 - INFO - The autoscaler took 0.039 seconds to complete the update iteration.
2025-06-26 01:19:32,074	INFO autoscaler.py:463 -- The autoscaler took 0.039 seconds to complete the update iteration.
2025-06-26 01:19:37,130 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:37,130	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:37,144 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:37,144	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:37,145 - INFO - The autoscaler took 0.042 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:37,145	INFO autoscaler.py:147 -- The autoscaler took 0.042 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:37,146 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:37.145909 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 4.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:37,146	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:37.145909 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 4.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:37,147 - INFO - The autoscaler took 0.044 seconds to complete the update iteration.
2025-06-26 01:19:37,147	INFO autoscaler.py:463 -- The autoscaler took 0.044 seconds to complete the update iteration.
2025-06-26 01:19:42,218 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:42,218	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:42,235 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:42,235	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:42,235 - INFO - The autoscaler took 0.06 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:42,235	INFO autoscaler.py:147 -- The autoscaler took 0.06 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:42,236 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:42.236180 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:42,236	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:42.236180 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:42,238 - INFO - The autoscaler took 0.062 seconds to complete the update iteration.
2025-06-26 01:19:42,238	INFO autoscaler.py:463 -- The autoscaler took 0.062 seconds to complete the update iteration.
2025-06-26 01:19:47,245 - INFO - Refreshing K8s API client token and certs.
2025-06-26 01:19:47,245	INFO node_provider.py:277 -- Refreshing K8s API client token and certs.
2025-06-26 01:19:47,265 - INFO - Refreshing K8s API client token and certs.
2025-06-26 01:19:47,265	INFO node_provider.py:277 -- Refreshing K8s API client token and certs.
2025-06-26 01:19:47,291 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:47,291	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:47,306 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:47,306	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:47,307 - INFO - The autoscaler took 0.042 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:47,307	INFO autoscaler.py:147 -- The autoscaler took 0.042 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:47,308 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:47.308125 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:47,308	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:47.308125 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:47,309 - INFO - The autoscaler took 0.045 seconds to complete the update iteration.
2025-06-26 01:19:47,309	INFO autoscaler.py:463 -- The autoscaler took 0.045 seconds to complete the update iteration.
2025-06-26 01:19:52,365 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:52,365	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:52,383 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:52,383	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:52,384 - INFO - The autoscaler took 0.043 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:52,384	INFO autoscaler.py:147 -- The autoscaler took 0.043 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:52,384 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:52.384528 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:52,384	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:52.384528 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:52,386 - INFO - The autoscaler took 0.045 seconds to complete the update iteration.
2025-06-26 01:19:52,386	INFO autoscaler.py:463 -- The autoscaler took 0.045 seconds to complete the update iteration.
2025-06-26 01:19:57,446 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:57,446	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:19:57,460 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:19:57,460	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:19:57,461 - INFO - The autoscaler took 0.042 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:57,461	INFO autoscaler.py:147 -- The autoscaler took 0.042 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:19:57,461 - INFO - 
======== Autoscaler status: 2025-06-26 01:19:57.461672 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:57,461	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:19:57.461672 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:19:57,463 - INFO - The autoscaler took 0.044 seconds to complete the update iteration.
2025-06-26 01:19:57,463	INFO autoscaler.py:463 -- The autoscaler took 0.044 seconds to complete the update iteration.
2025-06-26 01:20:02,564 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:20:02,564	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:20:02,607 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:20:02,607	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:20:02,609 - INFO - The autoscaler took 0.086 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:20:02,609	INFO autoscaler.py:147 -- The autoscaler took 0.086 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:20:02,610 - INFO - 
======== Autoscaler status: 2025-06-26 01:20:02.609879 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:20:02,610	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:20:02.609879 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:20:02,614 - INFO - The autoscaler took 0.092 seconds to complete the update iteration.
2025-06-26 01:20:02,614	INFO autoscaler.py:463 -- The autoscaler took 0.092 seconds to complete the update iteration.
2025-06-26 01:20:07,676 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:20:07,676	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:20:07,691 - INFO - Fetched pod data at resource version 33602810.
2025-06-26 01:20:07,691	INFO node_provider.py:390 -- Fetched pod data at resource version 33602810.
2025-06-26 01:20:07,692 - INFO - The autoscaler took 0.042 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:20:07,692	INFO autoscaler.py:147 -- The autoscaler took 0.042 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:20:07,692 - INFO - 
======== Autoscaler status: 2025-06-26 01:20:07.692807 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:20:07,692	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:20:07.692807 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:20:07,694 - INFO - The autoscaler took 0.045 seconds to complete the update iteration.
2025-06-26 01:20:07,694	INFO autoscaler.py:463 -- The autoscaler took 0.045 seconds to complete the update iteration.
2025-06-26 01:20:12,746 - INFO - Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:20:12,746	INFO node_provider.py:372 -- Listing pods for RayCluster terrafloww-ray-cluster in namespace terrafloww-platform at pods resource version >= 32570794.
2025-06-26 01:20:12,759 - INFO - Fetched pod data at resource version 33603364.
2025-06-26 01:20:12,759	INFO node_provider.py:390 -- Fetched pod data at resource version 33603364.
2025-06-26 01:20:12,760 - INFO - The autoscaler took 0.038 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:20:12,760	INFO autoscaler.py:147 -- The autoscaler took 0.038 seconds to fetch the list of non-terminated nodes.
2025-06-26 01:20:12,760 - INFO - 
======== Autoscaler status: 2025-06-26 01:20:12.760328 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:20:12,760	INFO autoscaler.py:408 -- 
======== Autoscaler status: 2025-06-26 01:20:12.760328 ========
Node status
---------------------------------------------------------------
Active:
 1 headgroup
 4 worker-group
Pending:
 IP not yet assigned: worker-group, pending
Recent failures:
 (no failures)

Resources
---------------------------------------------------------------
Total Usage:
 0.0/8.0 CPU
 0B/22.00GiB memory
 19.11KiB/5.14GiB object_store_memory

Total Constraints:
 (no request_resources() constraints)
Total Demands:
 (no resource demands)
2025-06-26 01:20:12,762 - INFO - The autoscaler took 0.04 seconds to complete the update iteration.
2025-06-26 01:20:12,762	INFO autoscaler.py:463 -- The autoscaler took 0.04 seconds to complete the update iteration.
