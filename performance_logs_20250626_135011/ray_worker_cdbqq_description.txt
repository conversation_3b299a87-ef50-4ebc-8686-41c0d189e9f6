Name:             terrafloww-ray-cluster-worker-group-worker-cdbqq
Namespace:        terrafloww-platform
Priority:         0
Service Account:  default
Node:             data-pool-e0y1e6npd-t0iv9/10.122.0.6
Start Time:       Thu, 26 Jun 2025 13:48:10 +0530
Labels:           app=terrafloww
                  app.kubernetes.io/created-by=kuberay-operator
                  app.kubernetes.io/name=kuberay
                  component=ray-worker
                  ray.io/cluster=terrafloww-ray-cluster
                  ray.io/group=worker-group
                  ray.io/identifier=terrafloww-ray-cluster-worker
                  ray.io/is-ray-node=yes
                  ray.io/node-type=worker
Annotations:      <none>
Status:           Running
IP:               ***********
IPs:
  IP:           ***********
Controlled By:  RayCluster/terrafloww-ray-cluster
Init Containers:
  wait-gcs-ready:
    Container ID:  containerd://3619048676d90e45e3450c75f4a090588e68069e2b33e890eab84afee5e1271f
    Image:         registry.digitalocean.com/terrafloww-dev/ray-custom:**********
    Image ID:      registry.digitalocean.com/terrafloww-dev/ray-custom@sha256:****************************************************************
    Port:          <none>
    Host Port:     <none>
    Command:
      /bin/bash
      -lc
      --
    Args:
      
                            SECONDS=0
                            while true; do
                              if (( SECONDS <= 120 )); then
                                if ray health-check --address terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379 > /dev/null 2>&1; then
                                  echo "GCS is ready."
                                  break
                                fi
                                echo "$SECONDS seconds elapsed: Waiting for GCS to be ready."
                              else
                                if ray health-check --address terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379; then
                                  echo "GCS is ready. Any error messages above can be safely ignored."
                                  break
                                fi
                                echo "$SECONDS seconds elapsed: Still waiting for GCS to be ready. For troubleshooting, refer to the FAQ at https://github.com/ray-project/kuberay/blob/master/docs/guidance/FAQ.md."
                              fi
                              sleep 5
                            done
                          
    State:          Terminated
      Reason:       Completed
      Exit Code:    0
      Started:      Thu, 26 Jun 2025 13:48:12 +0530
      Finished:     Thu, 26 Jun 2025 13:48:31 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     200m
      memory:  256Mi
    Requests:
      cpu:     200m
      memory:  256Mi
    Environment:
      RAY_DISABLE_IMPORT_WARNING:  1
      RAY_DEDUP_LOGS:              0
      RAY_USAGE_STATS_ENABLED:     0
      FLIGHT_HOST:                 terrafloww-processing-engine-svc
      FLIGHT_PORT:                 50052
      FQ_RAY_IP:                   terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local
      RAY_IP:                      terrafloww-ray-cluster-head-svc
    Mounts:
      /dev/shm from shared-mem (rw)
      /tmp/ray from ray-logs (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-dh7k9 (ro)
Containers:
  ray-worker:
    Container ID:  containerd://e63d5f785f42aaeff130edc8be74a400661f36341046cb337bd2abbd619d12bc
    Image:         registry.digitalocean.com/terrafloww-dev/ray-custom:**********
    Image ID:      registry.digitalocean.com/terrafloww-dev/ray-custom@sha256:****************************************************************
    Port:          8080/TCP
    Host Port:     0/TCP
    Command:
      /bin/bash
      -lc
      --
    Args:
      ulimit -n 65536; ray start  --address=terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379  --block  --dashboard-agent-listen-port=52365  --memory=**********  --metrics-export-port=8080  --num-cpus=2 
    State:          Running
      Started:      Thu, 26 Jun 2025 13:48:32 +0530
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:     2
      memory:  4Gi
    Requests:
      cpu:      1
      memory:   2Gi
    Liveness:   exec [bash -c wget -T 2 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success] delay=30s timeout=2s period=5s #success=1 #failure=120
    Readiness:  exec [bash -c wget -T 2 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success] delay=10s timeout=2s period=5s #success=1 #failure=10
    Environment:
      RAY_DISABLE_IMPORT_WARNING:           1
      RAY_DEDUP_LOGS:                       0
      RAY_USAGE_STATS_ENABLED:              0
      FLIGHT_HOST:                          terrafloww-processing-engine-svc
      FLIGHT_PORT:                          50052
      FQ_RAY_IP:                            terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local
      RAY_IP:                               terrafloww-ray-cluster-head-svc
      RAY_CLUSTER_NAME:                      (v1:metadata.labels['ray.io/cluster'])
      RAY_CLOUD_INSTANCE_ID:                terrafloww-ray-cluster-worker-group-worker-cdbqq (v1:metadata.name)
      RAY_NODE_TYPE_NAME:                    (v1:metadata.labels['ray.io/group'])
      KUBERAY_GEN_RAY_START_CMD:            ray start  --address=terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379  --block  --dashboard-agent-listen-port=52365  --memory=**********  --metrics-export-port=8080  --num-cpus=2 
      RAY_PORT:                             6379
      RAY_ADDRESS:                          terrafloww-ray-cluster-head-svc.terrafloww-platform.svc.cluster.local:6379
      RAY_USAGE_STATS_KUBERAY_IN_USE:       1
      RAY_DASHBOARD_ENABLE_K8S_DISK_USAGE:  1
    Mounts:
      /dev/shm from shared-mem (rw)
      /tmp/ray from ray-logs (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-dh7k9 (ro)
Conditions:
  Type                        Status
  PodReadyToStartContainers   True 
  Initialized                 True 
  Ready                       True 
  ContainersReady             True 
  PodScheduled                True 
Volumes:
  ray-logs:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     
    SizeLimit:  <unset>
  shared-mem:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     Memory
    SizeLimit:  2Gi
  kube-api-access-dh7k9:
    Type:                    Projected (a volume that contains injected data from multiple sources)
    TokenExpirationSeconds:  3607
    ConfigMapName:           kube-root-ca.crt
    Optional:                false
    DownwardAPI:             true
QoS Class:                   Burstable
Node-Selectors:              doks.digitalocean.com/node-pool=data-pool-e0y1e6npd
Tolerations:                 node.kubernetes.io/not-ready:NoExecute op=Exists for 300s
                             node.kubernetes.io/unreachable:NoExecute op=Exists for 300s
                             workload=data:NoSchedule
Events:
  Type    Reason     Age   From               Message
  ----    ------     ----  ----               -------
  Normal  Scheduled  2m3s  default-scheduler  Successfully assigned terrafloww-platform/terrafloww-ray-cluster-worker-group-worker-cdbqq to data-pool-e0y1e6npd-t0iv9
  Normal  Pulling    2m3s  kubelet            Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
  Normal  Pulled     2m2s  kubelet            Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 1.221s (1.221s including waiting). Image size: 1339265896 bytes.
  Normal  Created    2m2s  kubelet            Created container: wait-gcs-ready
  Normal  Started    2m2s  kubelet            Started container wait-gcs-ready
  Normal  Pulling    103s  kubelet            Pulling image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********"
  Normal  Pulled     102s  kubelet            Successfully pulled image "registry.digitalocean.com/terrafloww-dev/ray-custom:**********" in 646ms (646ms including waiting). Image size: 1339265896 bytes.
  Normal  Created    102s  kubelet            Created container: ray-worker
  Normal  Started    102s  kubelet            Started container ray-worker
