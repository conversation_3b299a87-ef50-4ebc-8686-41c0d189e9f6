[2025-06-26 01:18:34,696 W 1 1] global_state_accessor.cc:435: Retrying to get node with node ID 5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:18:35,698 W 1 1] global_state_accessor.cc:435: Retrying to get node with node ID 5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
2025-06-26 01:18:34,530	INFO scripts.py:1152 -- [37mLocal node IP[39m: [1m10.108.6.34[22m
2025-06-26 01:18:36,744	SUCC scripts.py:1168 -- [32m--------------------[39m
2025-06-26 01:18:36,744	SUCC scripts.py:1169 -- [32mRay runtime started.[39m
2025-06-26 01:18:36,744	SUCC scripts.py:1170 -- [32m--------------------[39m
2025-06-26 01:18:36,745	INFO scripts.py:1172 -- To terminate the <PERSON> runtime, run
2025-06-26 01:18:36,745	INFO scripts.py:1173 -- [1m  ray stop[22m
2025-06-26 01:18:36,745	INFO scripts.py:1181 -- [36m[1m--block[22m[39m
2025-06-26 01:18:36,745	INFO scripts.py:1182 -- This command will now block forever until terminated by a signal.
2025-06-26 01:18:36,745	INFO scripts.py:1185 -- Running subprocesses are monitored and a message will be printed if any of them terminate unexpectedly. Subprocesses exit with SIGTERM will be treated as graceful, thus NOT reported.
