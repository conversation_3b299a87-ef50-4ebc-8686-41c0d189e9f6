# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Analyze Ray Worker Timing from Logs

This script analyzes the actual timing patterns from Ray worker logs
to understand where time is being spent in the data processing pipeline.
"""

import re
from datetime import datetime
from typing import List, Dict, Tuple


def parse_timestamp(log_line: str) -> datetime:
    """Extract timestamp from log line."""
    # Pattern: 2025-06-26 01:19:32,492
    timestamp_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})'
    match = re.search(timestamp_pattern, log_line)
    if match:
        timestamp_str = match.group(1)
        return datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
    return None


def analyze_single_worker_task(log_file: str) -> None:
    """Analyze timing for a single worker task."""
    print("🔍 ANALYZING SINGLE WORKER TASK TIMING")
    print("=" * 60)
    
    with open(log_file, 'r') as f:
        lines = f.readlines()
    
    # Find a complete worker task (pid=83, ip=***********)
    worker_lines = [line for line in lines if 'pid=83, ip=***********' in line]
    
    if not worker_lines:
        print("❌ No worker lines found")
        return
    
    print(f"📊 Found {len(worker_lines)} log lines for worker pid=83")
    
    # Key timing events to track
    timing_events = []
    
    for line in worker_lines:
        timestamp = parse_timestamp(line)
        if not timestamp:
            continue
            
        # Track key events
        if 'Fetching data for' in line and 'URLs concurrently' in line:
            timing_events.append(('data_fetch_start', timestamp, line.strip()))
        elif 'httpx - INFO - HTTP Request: GET' in line and 'sentinel-cogs' in line:
            timing_events.append(('http_request', timestamp, line.strip()))
        elif 'Fetching complete' in line:
            timing_events.append(('data_fetch_complete', timestamp, line.strip()))
        elif 'Applying Horizontal predictor' in line:
            timing_events.append(('tiff_decode', timestamp, line.strip()))
        elif 'Stacking bands and applying kernels' in line:
            timing_events.append(('kernel_start', timestamp, line.strip()))
        elif 'NDVI kernel: Processing collection' in line:
            timing_events.append(('ndvi_start', timestamp, line.strip()))
        elif 'Kernel \'terrafloww.spectral.ndvi\' added column' in line:
            timing_events.append(('ndvi_complete', timestamp, line.strip()))
        elif 'Streaming results via Flight' in line:
            timing_events.append(('flight_start', timestamp, line.strip()))
        elif 'Successfully wrote batch' in line and 'Flight' in line:
            timing_events.append(('flight_complete', timestamp, line.strip()))
        elif 'Worker async logic finished' in line:
            timing_events.append(('task_complete', timestamp, line.strip()))
    
    if not timing_events:
        print("❌ No timing events found")
        return
    
    print(f"\n📋 Found {len(timing_events)} timing events")
    print("\nTiming Analysis:")
    print("-" * 80)
    
    # Calculate durations between events
    for i, (event_type, timestamp, log_line) in enumerate(timing_events):
        if i == 0:
            print(f"{timestamp.strftime('%H:%M:%S.%f')[:-3]} | START     | {event_type}")
            start_time = timestamp
        else:
            duration = (timestamp - timing_events[i-1][1]).total_seconds()
            total_duration = (timestamp - start_time).total_seconds()
            print(f"{timestamp.strftime('%H:%M:%S.%f')[:-3]} | +{duration:>6.3f}s | {event_type} (total: {total_duration:.3f}s)")
    
    # Analyze phase durations
    print(f"\n📊 PHASE DURATION ANALYSIS")
    print("-" * 40)
    
    phase_durations = {}
    
    # Find specific phase pairs
    fetch_start = next((ts for event, ts, _ in timing_events if event == 'data_fetch_start'), None)
    fetch_complete = next((ts for event, ts, _ in timing_events if event == 'data_fetch_complete'), None)
    
    if fetch_start and fetch_complete:
        phase_durations['Data Fetching'] = (fetch_complete - fetch_start).total_seconds()
    
    ndvi_start = next((ts for event, ts, _ in timing_events if event == 'ndvi_start'), None)
    ndvi_complete = next((ts for event, ts, _ in timing_events if event == 'ndvi_complete'), None)
    
    if ndvi_start and ndvi_complete:
        phase_durations['NDVI Computation'] = (ndvi_complete - ndvi_start).total_seconds()
    
    flight_start = next((ts for event, ts, _ in timing_events if event == 'flight_start'), None)
    flight_complete = next((ts for event, ts, _ in timing_events if event == 'flight_complete'), None)
    
    if flight_start and flight_complete:
        phase_durations['Flight Upload'] = (flight_complete - flight_start).total_seconds()
    
    # Calculate total task duration
    if timing_events:
        total_duration = (timing_events[-1][1] - timing_events[0][1]).total_seconds()
        phase_durations['Total Task'] = total_duration
    
    # Display phase analysis
    for phase, duration in phase_durations.items():
        percentage = (duration / total_duration * 100) if total_duration > 0 else 0
        print(f"{phase:>20}: {duration:>8.3f}s ({percentage:>5.1f}%)")


def analyze_http_requests(log_file: str) -> None:
    """Analyze HTTP request patterns and timing."""
    print(f"\n🌐 HTTP REQUEST ANALYSIS")
    print("=" * 60)
    
    with open(log_file, 'r') as f:
        lines = f.readlines()
    
    # Find all HTTP requests
    http_requests = []
    
    for line in lines:
        if 'httpx - INFO - HTTP Request: GET' in line and 'sentinel-cogs' in line:
            timestamp = parse_timestamp(line)
            if timestamp:
                # Extract URL and response info
                if 'B04.tif' in line:
                    band = 'B04 (Red)'
                elif 'B08.tif' in line:
                    band = 'B08 (NIR)'
                else:
                    band = 'Unknown'
                
                # Extract response code and size
                if '206 Partial Content' in line:
                    status = '206 Partial Content'
                else:
                    status = 'Unknown'
                
                http_requests.append((timestamp, band, status, line.strip()))
    
    print(f"📊 Found {len(http_requests)} HTTP requests")
    
    if not http_requests:
        return
    
    # Group by band
    red_requests = [req for req in http_requests if 'B04' in req[1]]
    nir_requests = [req for req in http_requests if 'B08' in req[1]]
    
    print(f"🔴 Red band (B04) requests: {len(red_requests)}")
    print(f"🟢 NIR band (B08) requests: {len(nir_requests)}")
    
    # Analyze request timing patterns
    if len(http_requests) >= 2:
        first_request = http_requests[0][0]
        last_request = http_requests[-1][0]
        total_request_time = (last_request - first_request).total_seconds()
        
        print(f"⏱️ Total HTTP request timespan: {total_request_time:.3f}s")
        print(f"📈 Average time between requests: {total_request_time / (len(http_requests) - 1):.3f}s")
    
    # Show sample requests
    print(f"\n📋 Sample HTTP Requests:")
    for i, (timestamp, band, status, log_line) in enumerate(http_requests[:5]):
        print(f"  {i+1}. {timestamp.strftime('%H:%M:%S.%f')[:-3]} | {band} | {status}")


def analyze_data_processing_overhead(log_file: str) -> None:
    """Analyze data processing and serialization overhead."""
    print(f"\n⚙️ DATA PROCESSING OVERHEAD ANALYSIS")
    print("=" * 60)
    
    with open(log_file, 'r') as f:
        content = f.read()
    
    # Count key operations
    operations = {
        'Arrow List Conversions': content.count('_np_chunk_to_arrow_list_components'),
        'Schema Assemblies': content.count('Final assembled schema for batch'),
        'Flight Connections': content.count('Connected to Flight server'),
        'Flight Uploads': content.count('Successfully wrote batch'),
        'TIFF Decodings': content.count('Applying Horizontal predictor'),
        'Kernel Applications': content.count('Applying kernel'),
    }
    
    print("📊 Operation Counts:")
    for operation, count in operations.items():
        print(f"  {operation:>25}: {count:>3} times")
    
    # Look for memory/serialization patterns
    if 'raster_data: list<item: float>' in content:
        print(f"\n💾 Data Serialization:")
        print(f"  ✅ Using Arrow list format for raster data")
        print(f"  ✅ Float32 data type (efficient)")
    
    # Check for any error patterns
    error_patterns = [
        'RuntimeWarning: divide by zero',
        'Error',
        'Exception',
        'Failed'
    ]
    
    errors_found = []
    for pattern in error_patterns:
        if pattern in content:
            errors_found.append(pattern)
    
    if errors_found:
        print(f"\n⚠️ Issues Found:")
        for error in errors_found:
            print(f"  🔸 {error}")
    else:
        print(f"\n✅ No errors or warnings detected")


def main():
    """Main analysis function."""
    log_file = "performance_logs_20250626_135011/processing_engine_logs.txt"
    
    print("🔍 RAY WORKER TIMING ANALYSIS")
    print("=" * 80)
    print(f"📁 Analyzing: {log_file}")
    print("=" * 80)
    
    try:
        analyze_single_worker_task(log_file)
        analyze_http_requests(log_file)
        analyze_data_processing_overhead(log_file)
        
        print("\n" + "=" * 80)
        print("✅ Worker Timing Analysis Complete!")
        print("=" * 80)
        
    except FileNotFoundError:
        print(f"❌ Log file not found: {log_file}")
    except Exception as e:
        print(f"❌ Error analyzing logs: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
