{"cells": [{"cell_type": "markdown", "id": "3efe505a", "metadata": {}, "source": ["(document-tag-to-refer-to)=\n", "\n", "# Creating an Example\n", "\n", "This is an example template file for writing Jupyter Notebooks in markdown, using MyST.\n", "For more information on MyST notebooks, see the\n", "[MyST-NB documentation](https://myst-nb.readthedocs.io/en/latest/index.html).\n", "If you want to learn more about the MyST parser, see the\n", "[MyST documentation](https://myst-parser.readthedocs.io/en/latest/).\n", "\n", "MyST is common markdown compliant, so if you can use plain markdown here.\n", "In case you need to execute restructured text (`rSt`) directives, you can use `{eval-rst}` to execute the code.\n", "For instance, a here's a note written in rSt:\n", "\n", "```{eval-rst}\n", ".. note::\n", "\n", "   A note written in reStructuredText.\n", "```\n", "\n", "```{margin}\n", "You can create margins with this syntax for smaller notes that don't make it into the main\n", "text.\n", "```\n", "\n", "You can also easily define footnotes.[^example]\n", "\n", "[^example]: This is a footnote.\n", "\n", "## Adding code cells"]}, {"cell_type": "code", "execution_count": null, "id": "6b6ccdcf", "metadata": {}, "outputs": [], "source": ["import ray\n", "import ray.rllib.agents.ppo as ppo\n", "from ray import serve\n", "\n", "def train_ppo_model():\n", "    trainer = ppo.PPOTrainer(\n", "        config={\"framework\": \"torch\", \"num_workers\": 0},\n", "        env=\"CartPole-v0\",\n", "    )\n", "    # Train for one iteration\n", "    trainer.train()\n", "    trainer.save(\"/tmp/rllib_checkpoint\")\n", "    return \"/tmp/rllib_checkpoint/checkpoint_000001/checkpoint-1\"\n", "\n", "\n", "checkpoint_path = train_ppo_model()"]}, {"cell_type": "markdown", "id": "c21b9968", "metadata": {}, "source": ["## Hiding and removing cells\n", "\n", "You can hide cells, so that they will toggle when you click on the cell header.\n", "You can use different `:tags:` like `hide-cell`, `hide-input`, or `hide-output` to hide cell content,\n", "and you can use `remove-cell`, `remove-input`, or `remove-output` to remove the cell completely when rendered.\n", "Those cells will still show up in the notebook itself."]}, {"cell_type": "code", "execution_count": null, "id": "607e444a", "metadata": {"tags": ["hide-cell"]}, "outputs": [], "source": ["# This can be useful if you don't want to clutter the page with details.\n", "\n", "import ray\n", "import ray.rllib.agents.ppo as ppo\n", "from ray import serve"]}, {"cell_type": "markdown", "id": "0f4c428c", "metadata": {}, "source": [":::{tip}\n", "Here's a quick tip.\n", ":::\n", "\n", "\n", ":::{note}\n", "And this is a note.\n", ":::\n", "\n", "The following cell will be removed and not render:"]}, {"cell_type": "code", "execution_count": null, "id": "f008643b", "metadata": {"tags": ["remove-cell"]}, "outputs": [], "source": ["ray.shutdown()"]}, {"cell_type": "markdown", "id": "c206f666", "metadata": {}, "source": ["## Equations\n", "\n", "\\begin{equation}\n", "\\frac {\\partial u}{\\partial x} + \\frac{\\partial v}{\\partial y} = - \\, \\frac{\\partial w}{\\partial z}\n", "\\end{equation}\n", "\n", "\\begin{align*}\n", "2x - 5y &=  8 \\\\\n", "3x + 9y &=  -12\n", "\\end{align*}"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}