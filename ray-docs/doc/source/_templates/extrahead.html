<!-- Extra header to include at the top of each template.
Kept separately so that it can easily be included in any templates
that need to be overridden for individual pages; e.g. included
both in the usual template (layout.html) as well as (index.html). -->

<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link
  href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;900&family=Roboto:wght@400;500;700&display=swap"
  rel="stylesheet"
/>
<link
  rel="stylesheet"
  title="light"
  href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css"
  disabled="disabled"
/>
<link
  rel="stylesheet"
  title="dark"
  href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css"
  disabled="disabled"
/>
<link
  href="https://cdn.jsdelivr.net/npm/remixicon@4.1.0/fonts/remixicon.css"
  rel="stylesheet"
/>

<!-- Used for text embedded in html on the index page  -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>

<!-- Parser used to call hljs on responses from Ray Assistant -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

<!-- Sanitizer for Ray Assistant AI -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/dompurify/2.3.3/purify.min.js"></script>

<!-- Fathom - beautiful, simple website analytics -->
<script src="https://deer.ray.io/script.js" data-site="WYYANYOS" defer></script>
<!-- / Fathom -->

<!-- Google Tag Manager -->
<script>
  (function (w, d, s, l, i) {
    w[l] = w[l] || [];
    w[l].push({
      'gtm.start': new Date().getTime(),
      event: 'gtm.js',
    });
    var f = d.getElementsByTagName(s)[0],
      j = d.createElement(s),
      dl = l != 'dataLayer' ? '&l=' + l : '';
    j.async = true;
    j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
    f.parentNode.insertBefore(j, f);
  })(window, document, 'script', 'dataLayer', 'GTM-P8H6KQG');
</script>
<!-- End Google Tag Manager -->
<!-- Data to be shared with JS on every page -->
<script>
  window.data = {
      copyIconSrc: "{{ pathto('_static/copy-button.svg', 1) }}"
  };
</script>
