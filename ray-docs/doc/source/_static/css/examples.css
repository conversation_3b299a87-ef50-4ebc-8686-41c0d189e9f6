html {
  --remix-icon-color: #ffffff;
}

html[data-theme='dark'] {
  --example-color: color-mix(in srgb, #ffffff, transparent 90%);
}

html[data-theme='light'] {
  --example-color: #ffffff;
}

#examples-search-input-label {
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

#examples-search-icon {
  fill: var(--pst-color-text-base);
  margin: 0em 1em;
}

#examples-search-input {
  width: 100%;
  color: var(--pst-color-text-base);
  border: none;
  background-color: transparent;
}

#examples-search-input:focus-visible {
  outline: none;
}

#examples-search-input::placeholder {
  color: var(--pst-color-text-muted);
  opacity: 1;
}

#no-matches {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

#no-matches-inner-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

#no-matches.hidden,
.gallery-item.hidden {
  display: none !important;
}

.examples-search-area {
  display: flex;
  flex-direction: row;
  padding: 0.75em 0em;
  border: 1px solid var(--pst-color-border);
  border-radius: 4px;
  background: var(--pst-color-background);
}

.content {
  display: flex;
  flex-direction: column;
  width: 70%;
  justify-content: center;
}

.content-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.example-tags {
  color: var(--pst-color-text-muted);
  text-transform: uppercase;
}

.example-list-area {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2em;
}

@media (min-width: 960px) {
  .example-list-area {
    grid-template-columns: 1fr 1fr;
  }
}

.example-text-area {
  display: flex;
  flex-direction: column;
}

#dropdown-area {
  display: flex;
  flex-direction: row;
  gap: 2em;
  margin: 1.5em 0em 2.5em 0em;
}

.filter-dropdown {
  border: 1px solid var(--dropdown-border-color);
  border-radius: 4px;
}

.dropdown-checkbox {
  display: none;
  padding: 0.5em 1.5em;
}

.dropdown-label {
  user-select: none;
  border: 1px solid var(--pst-color-border);
  border-radius: 4px;
  padding: 0.5em 1em;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1em;
}

/* When the dropdown is exposed, highlight the background of the parent div label */
.dropdown-checkbox[type='checkbox']:checked ~ .dropdown-label {
  background-color: var(--pst-color-on-background);
}

.dropdown-content {
  display: none;
  position: absolute;
  background: var(--pst-color-on-background);
  padding: 0.5em 1em;
  border: 1px solid var(--pst-color-border);
  border-radius: 4px;
  flex-direction: column;
  gap: 1em;
  z-index: 1;
}

/* Show dropdown when the checkbox is clicked */
.dropdown-checkbox[type='checkbox']:checked ~ .dropdown-content {
  display: flex !important;
}

/* Checkboxes */
.checkbox-container {
  display: block;
  position: relative;
  padding-left: 35px;
  cursor: pointer;
  user-select: none;
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  /* Color of an empty checkbox square */
  background-color: var(--pst-color-surface);
}
.checkbox-container input:checked ~ .checkmark {
  /* Color of checkbox when checked */
  background-color: var(--pst-color-primary-highlight);
}
.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}
.checkmark:after {
  content: '';
  position: absolute;
  display: none;
}
.checkbox-container .checkmark:after {
  left: 10px;
  top: 6px;
  width: 6px;
  height: 12px;
  border: solid var(--pst-color-border);
  border-width: 0 3px 3px 0;
  transform: rotate(45deg);
}

.example.hidden {
  display: none;
}

.example-icon-area {
  width: 70px;
  height: 70px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.example-icon {
  border-radius: 10px !important; /* Competes against pydata-sphinx-theme img styles */
  background-color: transparent !important; /* Competes against pydata-sphinx-theme img styles */
  min-width: 75px;
  min-height: 75px;
}

.remix-icon {
  position: absolute;
  font-size: 28px;
  color: var(--remix-icon-color);
}

.example-link {
  color: var(--pst-color-text-base) !important;
  text-decoration: none;
  display: flex;
  flex-direction: row;
  gap: 1em;
  align-items: center;
  padding: 1em;
}

.example {
  border-radius: 4px;
  background-color: color-mix(
    in srgb,
    var(--pst-color-on-background) 70%,
    transparent
  );
  box-shadow: 0px 4px 15px color-mix(in srgb, black 5%, transparent);
}

.example-other-keywords {
  display: none;
}

.community-text {
  color: var(--pst-color-text-muted);
}

.community-emojis {
  padding-left: 0.5em;
}
