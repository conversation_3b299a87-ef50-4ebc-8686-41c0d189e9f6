/* Pandas dataframe css */
/* Taken from: https://github.com/spatialaudio/nbsphinx/blob/fb3ba670fc1ba5f54d4c487573dbc1b4ecf7e9ff/src/nbsphinx.py#L587-L619 */

table.dataframe {
  border: none !important;
  border-collapse: collapse;
  border-spacing: 0;
  border-color: transparent;
  color: black;
  font-size: 12px;
  table-layout: fixed;
}
table.dataframe thead {
  border-bottom: 1px solid black;
  vertical-align: bottom;
}
table.dataframe tr,
table.dataframe th,
table.dataframe td {
  text-align: right;
  vertical-align: middle;
  padding: 0.5em 0.5em;
  line-height: normal;
  white-space: normal;
  max-width: none;
  border: none;
}
table.dataframe th {
  font-weight: bold;
}
table.dataframe tbody tr:nth-child(odd) {
  background: #f5f5f5;
}
table.dataframe tbody tr:hover {
  background: rgba(66, 165, 245, 0.2);
}
