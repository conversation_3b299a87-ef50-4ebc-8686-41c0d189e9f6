.. _ray-job-submission-sdk-ref:

Python SDK API Reference
========================

.. currentmodule:: ray.job_submission

For an overview with examples see :ref:`<PERSON> Jobs <jobs-overview>`.

For the CLI reference see :ref:`Ray Job Submission CLI Reference <ray-job-submission-cli-ref>`.

.. _job-submission-client-ref:

JobSubmissionClient
-------------------

.. autosummary::
   :nosignatures:
   :toctree: doc/

   JobSubmissionClient

.. autosummary::
   :nosignatures:
   :toctree: doc/

   JobSubmissionClient.submit_job
   JobSubmissionClient.stop_job
   JobSubmissionClient.get_job_status
   JobSubmissionClient.get_job_info
   JobSubmissionClient.list_jobs
   JobSubmissionClient.get_job_logs
   JobSubmissionClient.tail_job_logs
   JobSubmissionClient.delete_job

.. _job-status-ref:

JobStatus
---------

.. autosummary::
   :nosignatures:
   :toctree: doc/

   JobStatus

.. _job-info-ref:

JobInfo
-------

.. autosummary::
   :nosignatures:
   :toctree: doc/

   JobInfo

.. _job-details-ref:

JobDetails
----------

.. autosummary::
   :nosignatures:
   :toctree: doc/

   JobDetails

.. _job-type-ref:

JobType
-------

.. autosummary::
   :nosignatures:
   :toctree: doc/

   JobType

.. _driver-info-ref:

DriverInfo
----------

.. autosummary::
   :nosignatures:
   :toctree: doc/

   DriverInfo
