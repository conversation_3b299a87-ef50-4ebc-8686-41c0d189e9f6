(kuberay-examples)=

# Examples

```{toctree}
:hidden:

examples/ml-example
examples/gpu-training-example
examples/mnist-training-example
examples/stable-diffusion-rayservice
examples/tpu-serve-stable-diffusion
examples/mobilenet-rayservice
examples/text-summarizer-rayservice
examples/rayjob-batch-inference-example
examples/rayjob-kueue-priority-scheduling
examples/rayjob-kueue-gang-scheduling
examples/distributed-checkpointing-with-gcsfuse
examples/modin-example
examples/vllm-rayservice
```


This section presents example Ray workloads to try out on your Kubernetes cluster.

- {ref}`kuberay-ml-example` (CPU-only)
- {ref}`kuberay-gpu-training-example`
- {ref}`kuberay-mnist-training-example` (CPU-only)
- {ref}`kuberay-mobilenet-rayservice-example` (CPU-only)
- {ref}`kuberay-stable-diffusion-rayservice-example`
- {ref}`kuberay-tpu-stable-diffusion-example`
- {ref}`kuberay-text-summarizer-rayservice-example`
- {ref}`kuberay-batch-inference-example`
- {ref}`kuberay-kueue-priority-scheduling-example`
- {ref}`kuberay-kueue-gang-scheduling-example`
- {ref}`kuberay-distributed-checkpointing-gcsefuse`
- {ref}`kuberay-modin-example`
- {ref}`kuberay-vllm-rayservice-example`
