[kuberay-operator-pod-name]
regex: kuberay-operator-[a-z0-9]{10}-[a-z0-9]{5}
replace: KUBERAY-OPERATOR-POD-NAME

[raycluster-head-pod-name]
regex: raycluster-kuberay-workergroup-worker-[a-z0-9]{5}
replace: RAYCLUSTER-HEAD-POD-NAME

[raycluster-autoscaler-head-pod-name]
regex: raycluster-autoscaler-head-[a-z0-9]{5}
replace: RAYCLUSTER-AUTOSCALER-HEAD-POD-NAME

[raycluster-worker-pod-name]
regex: raycluster-kuberay-head-[a-z0-9]{5}
replace: RAYCLUSTER-WORKER-POD-NAME

[raycluster-autoscaler-small-group-worker-pod-name]
regex: raycluster-autoscaler-small-group-worker-[a-z0-9]{5}
replace: RAYCLUSTER-AUTOSCALER-SMALL-GROUP-WORKER-POD-NAME

[rayservice-sample-raycluster-name]
regex: rayservice-sample-raycluster-[a-z0-9]{5}(?!-)
replace: RAYSERVICE-SAMPLE-RAYCLUSTER-NAME

[rayservice-sample-head-pod-name]
regex: rayservice-sample-raycluster-[a-z0-9]{5}-head-[a-z0-9]{5}
replace: RAYSERVICE-SAMPLE-HEAD-POD-NAME

[rayservice-sample-worker-pod-name]
regex: rayservice-sample-raycluster-[a-z0-9]{5}-small-group-worker-[a-z0-9]{5}
replace: RAYSERVICE-SAMPLE-WORKER-POD-NAME

[rayservice-sample-k8s-svc]
regex: rayservice-sample-raycluster-[a-z0-9]{5}-head-svc
replace: RAYSERVICE-SAMPLE-K8S-SVC

[raycluster-external-redis-head-name]
regex: raycluster-external-redis-head-[a-z0-9]{5}
replace: RAYCLUSTER-EXTERNAL-REDIS-HEAD-POD-NAME

[raycluster-external-redis-worker-name]
regex: raycluster-external-redis-small-group-worker-[a-z0-9]{5}
replace: RAYCLUSTER-EXTERNAL-REDIS-WORKER-POD-NAME

[raycluster-redis-name]
regex: redis-[a-z0-9]{10}-[a-z0-9]{5}
replace: RAYCLUSTER-REDIS-POD-NAME

[rayjob-submit-name]
regex: raysubmit_[a-zA-Z0-9]{16}
replace: RAYJOB-SUBMIT-NAME

[rayjob-name]
regex: rayjob-[a-zA-Z0-9-]+-[a-z0-9]{5}
replace: RAYJOB-NAME

[rayjob-cluster-name]
regex: rayjob-[a-zA-Z0-9-]+-raycluster-[a-z0-9]{5}
replace: RAYJOB-CLUSTER-NAME

[rayjob-cluster-head-pod-name]
regex: rayjob-[a-zA-Z0-9-]+-raycluster-[a-z0-9]{5}-head-[a-z0-9]{5}
replace: RAYJOB-CLUSTER-HEAD-POD-NAME

[rayjob-cluster-small-group-worker-pod-name]
regex: rayjob-[a-zA-Z0-9-]+-raycluster-[a-z0-9]{5}-small-group-worker-[a-z0-9]{5}
replace: RAYJOB-CLUSTER-SMALL-GROUP-WORKER-POD-NAME

[time-duration]
regex: \d+m\d+s|\d+m|\d+s
replace: TIME-DURATION

[iso-time-stamp]
regex: \d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}[.,]?\d*
replace: ISO-TIME-STAMP

[ctime-time-stamp]
regex: [A-Z][a-z]{2}\s[A-Z][a-z]{2}\s+\d{1,2}\s\d{2}:\d{2}:\d{2}\s\d{4}
replace: CTIME-TIME-STAMP

[elapsed-time]
regex: \d+\.\d+\sseconds
replace: ELAPSED-TIME

[record-stamp]
regex: \d+-\d+-\d+T\d+:\d+:\d+Z
replace: TIME-STAMP

[IPV4]
regex: (?:\d{1,3}\.){3}\d{1,3}(\s*)
replace: IPV4

[python-logger-prefix]
regex: \d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2},\d{3}\s+[A-Z]+\s+[\w\.]+:\d+\s+--
replace: PYTHON-LOGGER-PREFIX

[ray-cluster-resources-object-store-memory]
regex: 'object_store_memory'\s*:\s*\d+(?:\.\d+)?\}
replace: RAY-CLUSTER-RESOURCES-OBJECT-STORE-MEMORY

[raycluster-autoscaler-object-store-memory]
regex: 0B/\d+\.\d+\s*[GM]iB object_store_memory
replace: RAYCLUSTER-AUTOSCALER-OBJECT-STORE-MEMORY
