{"cells": [{"cell_type": "markdown", "id": "493bc073-1a70-4d01-ac33-9d7d5c17fa17", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["(kuberay-rayservice-quickstart)=\n", "\n", "# RayService Quickstart\n", "\n", "## Prerequisites\n", "\n", "This guide mainly focuses on the behavior of KubeRay v1.3.0 and Ray 2.41.0.\n", "\n", "## What's a RayService?\n", "\n", "A RayService manages these components:\n", "\n", "* **RayCluster**: Manages resources in a Kubernetes cluster.\n", "* **Ray Serve Applications**: Manages users' applications.\n", "\n", "## What does the RayService provide?\n", "\n", "* **Kubernetes-native support for Ray clusters and Ray Serve applications:** After using a Kubernetes configuration to define a Ray cluster and its Ray Serve applications, you can use `kubectl` to create the cluster and its applications.\n", "* **In-place updating for Ray Serve applications:** See [RayService](kuberay-rayservice) for more details.\n", "* **Zero downtime upgrading for Ray clusters:** See [RayService](kuberay-rayservice) for more details.\n", "* **High-availabilable services:** See [RayService high availability](kuberay-rayservice-ha) for more details.\n", "\n", "## Example: Serve two simple Ray Serve applications using RayService\n", "\n", "## Step 1: Create a Kubernetes cluster with Kind"]}, {"cell_type": "code", "execution_count": 1, "id": "533f6367-2433-4162-afca-a7477c681554", "metadata": {"editable": true, "scrolled": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-output"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating cluster \"kind\" ...\n", " \u001b[32m✓\u001b[0m Ensuring node image (kindest/node:v1.26.0) 🖼\n", " \u001b[32m✓\u001b[0m Preparing nodes 📦 7l\n", " \u001b[32m✓\u001b[0m Writing configuration 📜7l\n", " \u001b[32m✓\u001b[0m Starting control-plane 🕹️7l\n", " \u001b[32m✓\u001b[0m Installing CNI 🔌7l\n", " \u001b[32m✓\u001b[0m Installing StorageClass 💾\n", "Set kubectl context to \"kind-kind\"\n", "You can now use your cluster with:\n", "\n", "kubectl cluster-info --context kind-kind\n", "\n", "Have a nice day! 👋\n"]}], "source": ["kind create cluster --image=kindest/node:v1.26.0"]}, {"cell_type": "markdown", "id": "6c59162c-37c0-4127-bfcc-ea0a6f608094", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Step 2: Install the KubeRay operator\n", "\n", "Follow [this document](kuberay-operator-deploy) to install the latest stable KubeRay operator from the Helm repository.\n", "Note that the YAML file in this example uses `serveConfigV2` to specify a multi-application Serve configuration, available starting from KubeRay v0.6.0."]}, {"cell_type": "code", "execution_count": 2, "id": "34fff725-2788-4661-8efe-f081f00b901c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME: kuberay-operator\n", "LAST DEPLOYED: Sat Apr 12 00:10:50 2025\n", "NAMESPACE: default\n", "STATUS: deployed\n", "REVISION: 1\n", "TEST SUITE: None\n", "deployment.apps/kuberay-operator condition met\n"]}], "source": ["../scripts/doctest-utils.sh install_kuberay_operator"]}, {"cell_type": "markdown", "id": "f8688f39-2348-4a2a-8bc0-e890b014968f", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Step 3: Install a RayService"]}, {"cell_type": "code", "execution_count": 3, "id": "066b7f68-d4ce-490e-b110-938bc946dfd7", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-output"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["rayservice.ray.io/rayservice-sample created\n"]}], "source": ["kubectl apply -f https://raw.githubusercontent.com/ray-project/kuberay/v1.3.0/ray-operator/config/samples/ray-service.sample.yaml"]}, {"cell_type": "code", "execution_count": 4, "id": "f33e1307-3c73-45a5-a732-d0f6b75fc330", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["rayservice.ray.io/rayservice-sample condition met\n", "pod/rayservice-sample-raycluster-czjtm-head-ldxl7 condition met\n", "pod/rayservice-sample-raycluster-czjtm-small-group-worker-pk88k condition met\n"]}], "source": ["kubectl wait --for=condition=Ready rayservice/rayservice-sample --timeout=500s\n", "export HEAD_POD=$(kubectl get pods --selector=ray.io/node-type=head -o custom-columns=POD:metadata.name --no-headers)\n", "export WORKER_POD=$(kubectl get pods --selector=ray.io/node-type=worker -o custom-columns=POD:metadata.name --no-headers)\n", "kubectl wait --for=condition=Ready pod/$HEAD_POD --timeout=500s\n", "kubectl wait --for=condition=Ready pod/$WORKER_POD --timeout=500s"]}, {"cell_type": "markdown", "id": "44db71fd-5edb-4dd1-b498-281e56328998", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Step 4: Verify the Kubernetes cluster status"]}, {"cell_type": "code", "execution_count": 5, "id": "e5182685-af70-4dba-aa19-c8d86bb29961", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME                SERVICE STATUS   NUM SERVE ENDPOINTS\n", "rayservice-sample   Running          2\n"]}], "source": ["# List all RayService custom resources in the `default` namespace.\n", "kubectl get rayservice"]}, {"cell_type": "code", "execution_count": 6, "id": "0359f430-8db8-4740-b65d-8810e90c290e", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME                                 DESIRED WORKERS   A<PERSON><PERSON><PERSON>LE WORKERS   CPUS    MEMORY   GPUS   STATUS   AGE\n", "rayservice-sample-raycluster-czjtm   1                 1                   2500m   4Gi      0      ready    4m21s\n"]}], "source": ["# List all RayCluster custom resources in the `default` namespace.\n", "kubectl get raycluster"]}, {"cell_type": "code", "execution_count": 7, "id": "2301a88b-30a7-489f-b0cd-91aeaaaa616d", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME                                                          READY   STATUS    RESTARTS   AGE\n", "rayservice-sample-raycluster-czjtm-head-ldxl7                 1/1     Running   0          4m21s\n", "rayservice-sample-raycluster-czjtm-small-group-worker-pk88k   1/1     Running   0          4m21s\n"]}], "source": ["# List all Ray Pods in the `default` namespace.\n", "kubectl get pods -l=ray.io/is-ray-node=yes"]}, {"cell_type": "code", "execution_count": 8, "id": "72a22ed8-aca7-4400-8375-9d2d17d6bbec", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["lastTransitionTime: 2025-04-11T16:17:01Z\n", "message: Number of serve endpoints is greater than 0\n", "observedGeneration: 1\n", "reason: NonZeroServeEndpoints\n", "status: True\n", "type: Ready\n"]}], "source": ["# Check the `Ready` condition of the RayService.\n", "# The RayService is ready to serve requests when the condition is `True`.\n", "# Users can also use `kubectl describe rayservices.ray.io rayservice-sample` to check the condition section\n", "kubectl get rayservice rayservice-sample -o json | jq -r '.status.conditions[] | select(.type==\"Ready\") | to_entries[] | \"\\(.key): \\(.value)\"'"]}, {"cell_type": "code", "execution_count": 9, "id": "b4b6f535-a9a9-4073-9fd6-f942c6447b90", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["kuberay-operator\n", "kubernetes\n", "rayservice-sample-head-svc\n", "rayservice-sample-raycluster-czjtm-head-svc\n", "rayservice-sample-serve-svc\n"]}], "source": ["# List services in the `default` namespace.\n", "kubectl get services -o json | jq -r '.items[].metadata.name'"]}, {"cell_type": "markdown", "id": "d39fab18-5125-4d49-8f83-00e72e12d980", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["When the Ray Serve applications are healthy and ready, KubeRay creates a head service and a Ray Serve service for the RayService custom resource. For example, `rayservice-sample-head-svc` and `rayservice-sample-serve-svc`.\n", "\n", "> **What do these services do?**\n", "\n", "- **`rayservice-sample-head-svc`**  \n", "  This service points to the **head pod** of the active RayCluster and is typically used to view the **Ray Dashboard** (port `8265`).\n", "\n", "- **`rayservice-sample-serve-svc`**  \n", "  This service exposes the **HTTP interface** of Ray Serve, typically on port `8000`.  \n", "  Use this service to send HTTP requests to your deployed Serve applications (e.g., REST API, ML inference, etc.).\n", "\n", "## Step 5: Verify the status of the Serve applications"]}, {"cell_type": "code", "execution_count": 10, "id": "3efa3bd1-ed2f-42c9-a866-52bcecc58364", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-output"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1] 19005\n"]}], "source": ["# (1) Forward the dashboard port to localhost.\n", "# (2) Check the Serve page in the Ray dashboard at http://localhost:8265/#/serve.\n", "kubectl port-forward svc/rayservice-sample-head-svc 8265:8265 > /dev/null &"]}, {"cell_type": "markdown", "id": "3e7b2dc1-16a6-4ab7-992d-926219235aef", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["* Refer to [rayservice-troubleshooting.md](kuberay-raysvc-troubleshoot) for more details on RayService observability.\n", "Below is a screenshot example of the Serve page in the Ray dashboard.\n", "  ![Ray Serve Dashboard](../images/dashboard_serve.png)\n", "\n", "## Step 6: Send requests to the Serve applications by the Kubernetes serve service"]}, {"cell_type": "code", "execution_count": 11, "id": "96c11684-d72c-45e3-86d1-81e2d8d2311d", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-output"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pod/curl created\n"]}], "source": ["# Step 6.1: Run a curl Pod.\n", "# If you already have a curl Pod, you can use `kubectl exec -it <curl-pod> -- sh` to access the Pod.\n", "kubectl run curl --image=radial/busyboxplus:curl --command -- tail -f /dev/null"]}, {"cell_type": "code", "execution_count": 12, "id": "3aab94d6-2362-47e0-b2f6-55acc0b7a5a2", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pod/curl condition met\n"]}], "source": ["kubectl wait --for=condition=Ready pod/curl --timeout=60s"]}, {"cell_type": "code", "execution_count": 13, "id": "85bbcb56-cee8-4c85-b3ad-895e42d774f4", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15 pizzas please!\n"]}], "source": ["# Step 6.3: Send a request to the calculator app.\n", "kubectl exec curl -- curl -sS -X POST -H 'Content-Type: application/json' rayservice-sample-serve-svc:8000/calc/ -d '[\"MUL\", 3]'"]}, {"cell_type": "code", "execution_count": 14, "id": "3a166a22-ae1c-4510-bf4e-9f468d4b9c6b", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6\n"]}], "source": ["# Step 6.2: Send a request to the fruit stand app.\n", "kubectl exec curl -- curl -sS -X POST -H 'Content-Type: application/json' rayservice-sample-serve-svc:8000/fruit/ -d '[\"MANGO\", 2]'"]}, {"cell_type": "markdown", "id": "627d1250-d609-4f38-be2b-b5668311fc5d", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Step 7: Clean up the Kubernetes cluster"]}, {"cell_type": "code", "execution_count": 15, "id": "527bdd35-5379-4f6d-8ff4-588fbe9e86bf", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-output"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1]+  Terminated: 15          kubectl port-forward svc/rayservice-sample-head-svc 8265:8265 > /dev/null\n", "Deleting cluster \"kind\" ...\n", "Deleted nodes: [\"kind-control-plane\"]\n"]}], "source": ["# Kill the `kubectl port-forward` background job in the earlier step\n", "killall kubectl\n", "kind delete cluster"]}, {"cell_type": "markdown", "id": "7eaea7d9-**************-e09ea0c67437", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Next steps\n", "\n", "* See [RayService](kuberay-rayservice) document for the full list of RayService features, including in-place update, zero downtime upgrade, and high-availability.\n", "* See [RayService troubleshooting guide](kuberay-raysvc-troubleshoot) if you encounter any issues.\n", "* See [Examples](kuberay-examples) for more RayService examples.\n", "The [MobileNet example](kuberay-mobilenet-rayservice-example) is a good example to start with because it doesn't require GPUs and is easy to run on a local machine."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "bash", "name": "bash"}, "language_info": {"codemirror_mode": "shell", "file_extension": ".sh", "mimetype": "text/x-sh", "name": "bash"}}, "nbformat": 4, "nbformat_minor": 5}