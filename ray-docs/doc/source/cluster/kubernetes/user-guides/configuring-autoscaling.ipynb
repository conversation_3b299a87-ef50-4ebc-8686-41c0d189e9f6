{"cells": [{"cell_type": "markdown", "id": "ecb0bb53-a8c2-465b-a27a-a822d7594197", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["(kuberay-autoscaling)=\n", "\n", "# KubeRay Autoscaling\n", "\n", "This guide explains how to configure the Ray Autoscaler on Kubernetes.\n", "The Ray Autoscaler is a Ray cluster process that automatically scales a cluster up and down based on resource demand.\n", "The Autoscaler does this by adjusting the number of nodes (Ray Pods) in the cluster based on the resources required by tasks, actors, or placement groups.\n", "\n", "The Autoscaler utilizes logical resource requests, indicated in `@ray.remote` and shown in `ray status`, not the physical machine utilization, to scale.\n", "If you launch an actor, task, or placement group and resources are insufficient, the Autoscaler queues the request.\n", "It adjusts the number of nodes to meet queue demands and removes idle nodes that have no tasks, actors, or objects over time.\n", "\n", "```{admonition} When to use Autoscaling?\n", "Autoscaling can reduce workload costs, but adds node launch overheads and can be tricky to configure.\n", "We recommend starting with non-autoscaling clusters if you're new to <PERSON>.\n", "```\n", "\n", "```{admonition} Ray Autoscaling V2 alpha with KubeRay (@ray 2.10.0)\n", "With Ray 2.10, Ray Autoscaler V2 alpha is available with KubeRay. It has improvements on observability and stability. Please see the [section](kuberay-autoscaler-v2) for more details.\n", "```\n", "\n", "## Overview\n", "\n", "The following diagram illustrates the integration of the Ray Autoscaler with the KubeRay operator.\n", "Although depicted as a separate entity for clarity, the Ray Autoscaler is actually a sidecar container within the Ray head Pod in the actual implementation.\n", "\n", "```{eval-rst}\n", ".. image:: ../images/AutoscalerOperator.svg\n", "    :align: center\n", "..\n", "    Find the source document here (https://docs.google.com/drawings/d/1LdOg9JQuN5AOII-vDpSaFBsTeg0JGWcsbyNNLP1yovg/edit)\n", "```\n", "\n", "```{admonition} 3 levels of autoscaling in KubeRay\n", "  * **Ray actor/task**: Some Ray libraries, like Ray Serve, can automatically adjust the number of Serve replicas (i.e., Ray actors) based on the incoming request volume.\n", "  * **Ray node**: Ray Autoscaler automatically adjusts the number of Ray nodes (i.e., Ray Pods) based on the resource demand of Ray actors/tasks.\n", "  * **Kubernetes node**: If the Kubernetes cluster lacks sufficient resources for the new Ray Pods that the Ray Autoscaler creates, the Kubernetes Autoscaler can provision a new Kubernetes node. ***You must configure the Kubernetes Autoscaler yourself.***\n", "```\n", "\n", "* The Autoscaler scales up the cluster through the following sequence of events:\n", "  1. A user submits a Ray workload.\n", "  2. The Ray head container aggregates the workload resource requirements and communicates them to the Ray Autoscaler sidecar.\n", "  3. The Autoscaler decides to add a Ray worker Pod to satisfy the workload's resource requirement.\n", "  4. The Autoscaler requests an additional worker Pod by incrementing the RayCluster CR's `replicas` field.\n", "  5. The KubeRay operator creates a Ray worker Pod to match the new `replicas` specification.\n", "  6. The Ray scheduler places the user's workload on the new worker Po<PERSON>.\n", "\n", "* The Autoscaler also scales down the cluster by removing idle worker Pods.\n", "If it finds an idle worker Pod, it reduces the count in the RayCluster CR's `replicas` field and adds the identified Pods to the CR's `workersToDelete` field.\n", "Then, the KubeRay operator deletes the Pods in the `workersToDelete` field.\n", "\n", "## Quickstart\n", "\n", "### Step 1: Create a Kubernetes cluster with Kind"]}, {"cell_type": "code", "execution_count": 1, "id": "8828092c-bcd7-4bf7-b29a-0dc79c96e8e9", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-output"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating cluster \"kind\" ...\n", " \u001b[32m✓\u001b[0m Ensuring node image (kindest/node:v1.26.0) 🖼\n", " \u001b[32m✓\u001b[0m Preparing nodes 📦 7l\n", " \u001b[32m✓\u001b[0m Writing configuration 📜\n", " \u001b[32m✓\u001b[0m Starting control-plane 🕹️7l\n", " \u001b[32m✓\u001b[0m Installing CNI 🔌7l\n", " \u001b[32m✓\u001b[0m Installing StorageClass 💾\n", "Set kubectl context to \"kind-kind\"\n", "You can now use your cluster with:\n", "\n", "kubectl cluster-info --context kind-kind\n", "\n", "Not sure what to do next? 😅  Check out https://kind.sigs.k8s.io/docs/user/quick-start/\n"]}], "source": ["kind create cluster --image=kindest/node:v1.26.0"]}, {"cell_type": "markdown", "id": "eeacb970-82b4-48c5-9918-c59bedbef460", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["### Step 2: Install the KubeRay operator\n", "\n", "Follow [this document](kuberay-operator-deploy) to install the latest stable KubeRay operator via Helm repository."]}, {"cell_type": "code", "execution_count": 2, "id": "fc8ed200-bd63-49f2-a324-5e71d4ff092e", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME: kuberay-operator\n", "LAST DEPLOYED: Sat Apr 12 23:04:37 2025\n", "NAMESPACE: default\n", "STATUS: deployed\n", "REVISION: 1\n", "TEST SUITE: None\n", "deployment.apps/kuberay-operator condition met\n"]}], "source": ["../scripts/doctest-utils.sh install_kuberay_operator"]}, {"cell_type": "markdown", "id": "82382255-c8fa-4829-a9a2-8b633436f562", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["### Step 3: Create a RayCluster custom resource with autoscaling enabled"]}, {"cell_type": "code", "execution_count": 3, "id": "273d9d2b-4e5f-40e3-be2c-d32098577a86", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-output"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["raycluster.ray.io/raycluster-autoscaler created\n", "configmap/ray-example created\n"]}], "source": ["kubectl apply -f https://raw.githubusercontent.com/ray-project/kuberay/v1.3.0/ray-operator/config/samples/ray-cluster.autoscaler.yaml"]}, {"cell_type": "markdown", "id": "3b86ba2b-f9fc-4a24-853b-68dfdcf0db67", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["### Step 4: Verify the Kubernetes cluster status"]}, {"cell_type": "code", "execution_count": 4, "id": "c0a22879-9bd0-462f-9e0c-c9d3b8a2e473", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["raycluster.ray.io/raycluster-autoscaler condition met\n"]}], "source": ["kubectl wait --for=condition=RayClusterProvisioned raycluster/raycluster-autoscaler --timeout=500s"]}, {"cell_type": "code", "execution_count": 5, "id": "68e826a6-024b-4ad1-8aa3-b0bb1f4c6a3a", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME                               READY   STATUS    RESTARTS   AGE\n", "raycluster-autoscaler-head-7cl8z   2/2     Running   0          31s\n"]}], "source": ["# Step 4.1: List all Ray Pods in the `default` namespace.\n", "kubectl get pods -l=ray.io/is-ray-node=yes"]}, {"cell_type": "code", "execution_count": 6, "id": "450a41c5-6816-4411-8d4c-99f39945f25b", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME               DATA   AGE\n", "kube-root-ca.crt   1      55s\n", "ray-example        2      31s\n"]}], "source": ["# Step 4.2: Check the ConfigMap in the `default` namespace.\n", "kubectl get configmaps"]}, {"cell_type": "markdown", "id": "2a2496a5-6160-4e28-aaf3-2aad55bc5777", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["The RayCluster has one head Pod and zero worker Pods. The head Pod has two containers: a Ray head container and a Ray Autoscaler sidecar container.\n", "Additionally, the [ray-cluster.autoscaler.yaml](https://github.com/ray-project/kuberay/blob/v1.3.0/ray-operator/config/samples/ray-cluster.autoscaler.yaml) includes a ConfigMap named `ray-example` that houses two Python scripts: `detached_actor.py` and `terminate_detached_actor.py`.\n", "\n", "* `detached_actor.py` is a Python script that creates a detached actor which requires 1 CPU.\n", "  ```py\n", "  import ray\n", "  import sys\n", "\n", "  @ray.remote(num_cpus=1)\n", "  class Actor:\n", "    pass\n", "\n", "  ray.init(namespace=\"default_namespace\")\n", "  Actor.options(name=sys.argv[1], lifetime=\"detached\").remote()\n", "  ```\n", "\n", "* `terminate_detached_actor.py` is a Python script that terminates a detached actor.\n", "  ```py\n", "  import ray\n", "  import sys\n", "\n", "  ray.init(namespace=\"default_namespace\")\n", "  detached_actor = ray.get_actor(sys.argv[1])\n", "  ray.kill(detached_actor)\n", "  ```\n", "\n", "### Step 5: <PERSON><PERSON> Ray<PERSON>luster scale-up by creating detached actors"]}, {"cell_type": "code", "execution_count": 7, "id": "f3e35aad-9afd-4f19-9b0c-62a80e3a30a0", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulted container \"ray-head\" out of: ray-head, autoscaler\n", "2025-04-12 08:05:43,156\tINFO worker.py:1514 -- Using address 127.0.0.1:6379 set in the environment variable RAY_ADDRESS\n", "2025-04-12 08:05:43,156\tINFO worker.py:1654 -- Connecting to existing Ray cluster at address: **********:6379...\n", "2025-04-12 08:05:43,164\tINFO worker.py:1832 -- Connected to Ray cluster. View the dashboard at \u001b[1m\u001b[32m**********:8265 \u001b[39m\u001b[22m\n"]}], "source": ["# Step 5.1: Create a detached actor \"actor1\" which requires 1 CPU.\n", "export HEAD_POD=$(kubectl get pods --selector=ray.io/node-type=head -o custom-columns=POD:metadata.name --no-headers)\n", "kubectl exec -it $HEAD_POD -- python3 /home/<USER>/samples/detached_actor.py actor1"]}, {"cell_type": "code", "execution_count": 8, "id": "73d74d6b-865e-4d74-b095-84d42de0073e", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["raycluster.ray.io/raycluster-autoscaler condition met\n", "raycluster-autoscaler-small-group-worker-bhppb\n", "pod/raycluster-autoscaler-small-group-worker-bhppb condition met\n"]}], "source": ["kubectl wait --for=jsonpath='{.status.availableWorkerReplicas}'=1 raycluster/raycluster-autoscaler --timeout=500s\n", "WORKER_POD1=$(kubectl get pods --selector=ray.io/node-type=worker -o custom-columns=POD:metadata.name --no-headers)\n", "echo $WORKER_POD1\n", "\n", "kubectl wait --for=condition=ready pod/$WORKER_POD1 --timeout=500s"]}, {"cell_type": "code", "execution_count": 9, "id": "a5090936-f67e-4806-83f8-1716e800e462", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME                                             READY   STATUS    RESTARTS   AGE\n", "raycluster-autoscaler-head-7cl8z                 2/2     Running   0          61s\n", "raycluster-autoscaler-small-group-worker-bhppb   1/1     Running   0          25s\n"]}], "source": ["# Step 5.2: The Ray Autoscaler creates a new worker Po<PERSON>.\n", "kubectl get pods -l=ray.io/is-ray-node=yes"]}, {"cell_type": "code", "execution_count": 10, "id": "541b1895-de3b-4d70-adac-fb86235ba3b9", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulted container \"ray-head\" out of: ray-head, autoscaler\n", "2025-04-12 08:06:13,084\tINFO worker.py:1514 -- Using address 127.0.0.1:6379 set in the environment variable RAY_ADDRESS\n", "2025-04-12 08:06:13,084\tINFO worker.py:1654 -- Connecting to existing Ray cluster at address: **********:6379...\n", "2025-04-12 08:06:13,092\tINFO worker.py:1832 -- Connected to Ray cluster. View the dashboard at \u001b[1m\u001b[32m**********:8265 \u001b[39m\u001b[22m\n"]}], "source": ["# Step 5.3: Create a detached actor which requires 1 CPU.\n", "kubectl exec -it $HEAD_POD -- python3 /home/<USER>/samples/detached_actor.py actor2"]}, {"cell_type": "code", "execution_count": 11, "id": "efac8b85-134a-4781-83de-5b2f692a1653", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["raycluster.ray.io/raycluster-autoscaler condition met\n", "raycluster-autoscaler-small-group-worker-2q8nc\n", "pod/raycluster-autoscaler-small-group-worker-2q8nc condition met\n"]}], "source": ["kubectl wait --for=jsonpath='{.status.availableWorkerReplicas}'=2 raycluster/raycluster-autoscaler --timeout=500s\n", "WORKER_POD2=$(kubectl get pods \\\n", "    --selector=ray.io/node-type=worker \\\n", "    --field-selector=\"metadata.name!=$WORKER_POD1\" \\\n", "    -o custom-columns=POD:metadata.name \\\n", "    --no-headers\n", ")\n", "echo $WORKER_POD2\n", "\n", "kubectl wait --for=condition=ready pod/$WORKER_POD2 --timeout=500s"]}, {"cell_type": "code", "execution_count": 12, "id": "b6ca119c-5b55-4bbb-b788-0328501a80b3", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME                                             READY   STATUS    RESTARTS   AGE\n", "raycluster-autoscaler-head-7cl8z                 2/2     Running   0          91s\n", "raycluster-autoscaler-small-group-worker-2q8nc   1/1     Running   0          25s\n", "raycluster-autoscaler-small-group-worker-bhppb   1/1     Running   0          55s\n"]}], "source": ["kubectl get pods -l=ray.io/is-ray-node=yes"]}, {"cell_type": "code", "execution_count": 13, "id": "a7f029e8-8fd9-432a-928a-5907807ff71e", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulted container \"ray-head\" out of: ray-head, autoscaler\n", "\n", "======== List: 2025-04-12 08:06:44.187704 ========\n", "Stats:\n", "------------------------------\n", "Total: 2\n", "\n", "Table:\n", "------------------------------\n", "    ACTOR_ID                          CLASS_NAME    STATE      JOB_ID  NAME    NODE_ID                                                     PID  RAY_NAMESPACE\n", " 0  27f2f892afa25b8a62ee2e9901000000  Actor         ALIVE    01000000  actor1  57cc51db8bd1e9174275fd480055f7bf79f9cc68c9b7a458447264da    112  default_namespace\n", " 1  3f0bce88efe9ff45358e42ff02000000  Actor         ALIVE    02000000  actor2  b84fc1fb511009412cc5964f288540ee9cc396a1b39125b6470ba38d    113  default_namespace\n", "\n", "\u001b[0m\n"]}], "source": ["# Step 5.4: List all actors in the Ray cluster.\n", "kubectl exec -it $HEAD_POD -- ray list actors"]}, {"cell_type": "markdown", "id": "52d902b2-d5d0-4d91-a83a-529683203ecd", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["The Ray Autoscaler generates a new worker Pod for each new detached actor.\n", "This is because the `rayStartParams` field in the Ray head specifies `num-cpus: \"0\"`, preventing the Ray scheduler from scheduling any Ray actors or tasks on the Ray head Pod.\n", "In addition, each Ray worker Pod has a capacity of 1 CPU, so the Autoscaler creates a new worker Pod to satisfy the resource requirement of the detached actor which requires 1 CPU.\n", "\n", "* Using detached actors isn't necessary to trigger cluster scale-up.\n", "Normal actors and tasks can also initiate it.\n", "[Detached actors](actor-lifetimes) remain persistent even after the job's driver process exits, which is why the Autoscaler doesn't scale down the cluster automatically when the `detached_actor.py` process exits, making it more convenient for this tutorial.\n", "\n", "* In this RayCluster custom resource, each Ray worker Pod possesses only 1 logical CPU from the perspective of the Ray Autoscaler.\n", "Therefore, if you create a detached actor with `@ray.remote(num_cpus=2)`, the Autoscaler doesn't initiate the creation of a new worker Pod because the capacity of the existing Pod is limited to 1 CPU.\n", "\n", "* (Advanced) The Ray Autoscaler also offers a [Python SDK](ref-autoscaler-sdk), enabling advanced users, like Ray maintainers, to request resources directly from the Autoscaler. Generally, most users don't need to use the SDK.\n", "\n", "### Step 6: <PERSON><PERSON> Ray<PERSON>luster scale-down by terminating detached actors"]}, {"cell_type": "code", "execution_count": 14, "id": "1c3a1cdc-49ef-4f6e-9617-8bbd1c6f3323", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulted container \"ray-head\" out of: ray-head, autoscaler\n", "2025-04-12 08:06:44,711\tINFO worker.py:1514 -- Using address 127.0.0.1:6379 set in the environment variable RAY_ADDRESS\n", "2025-04-12 08:06:44,711\tINFO worker.py:1654 -- Connecting to existing Ray cluster at address: **********:6379...\n", "2025-04-12 08:06:44,719\tINFO worker.py:1832 -- Connected to Ray cluster. View the dashboard at \u001b[1m\u001b[32m**********:8265 \u001b[39m\u001b[22m\n"]}], "source": ["# Step 6.1: Terminate the detached actor \"actor1\".\n", "kubectl exec -it $HEAD_POD -- python3 /home/<USER>/samples/terminate_detached_actor.py actor1"]}, {"cell_type": "code", "execution_count": 15, "id": "5b6c293c-b393-45c0-9947-9ded6fc4a30f", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pod/raycluster-autoscaler-small-group-worker-bhppb condition met\n"]}], "source": ["kubectl wait --for=delete pod/$WORKER_POD1 --timeout=500s"]}, {"cell_type": "code", "execution_count": 16, "id": "447bead1-91bd-460f-bfd9-f39a3e5c1547", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME                                             READY   STATUS    RESTARTS   AGE\n", "raycluster-autoscaler-head-7cl8z                 2/2     Running   0          2m38s\n", "raycluster-autoscaler-small-group-worker-2q8nc   1/1     Running   0          92s\n"]}], "source": ["# Step 6.2: A worker Pod will be deleted after `idleTimeoutSeconds` (default 60s) seconds.\n", "kubectl get pods -l=ray.io/is-ray-node=yes"]}, {"cell_type": "code", "execution_count": 17, "id": "584ac526-65b2-405d-832a-5b4a0d0f65b5", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulted container \"ray-head\" out of: ray-head, autoscaler\n", "2025-04-12 08:07:50,121\tINFO worker.py:1514 -- Using address 127.0.0.1:6379 set in the environment variable RAY_ADDRESS\n", "2025-04-12 08:07:50,121\tINFO worker.py:1654 -- Connecting to existing Ray cluster at address: **********:6379...\n", "2025-04-12 08:07:50,129\tINFO worker.py:1832 -- Connected to Ray cluster. View the dashboard at \u001b[1m\u001b[32m**********:8265 \u001b[39m\u001b[22m\n"]}], "source": ["# Step 6.3: Terminate the detached actor \"actor2\".\n", "kubectl exec -it $HEAD_POD -- python3 /home/<USER>/samples/terminate_detached_actor.py actor2"]}, {"cell_type": "code", "execution_count": 18, "id": "b2285c2d-b25d-4494-9698-3026b135b40d", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pod/raycluster-autoscaler-small-group-worker-2q8nc condition met\n"]}], "source": ["kubectl wait --for=delete pod/$WORKER_POD2 --timeout=500s"]}, {"cell_type": "code", "execution_count": 19, "id": "64c6dd30-2550-4f4e-834b-fd6c0f8b0084", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NAME                               READY   STATUS    RESTARTS   AGE\n", "raycluster-autoscaler-head-7cl8z   2/2     Running   0          3m43s\n"]}], "source": ["# Step 6.4: A worker Pod will be deleted after `idleTimeoutSeconds` (default 60s) seconds.\n", "kubectl get pods -l=ray.io/is-ray-node=yes"]}, {"cell_type": "markdown", "id": "4c5bbbb5-2870-4251-8d18-a68131953229", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["### Step 7: <PERSON> observability"]}, {"cell_type": "code", "execution_count": 20, "id": "7f621d66-659f-4649-8a07-8a12302380fa", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["======== Autoscaler status: 2025-04-12 08:08:53.443864 ========\n", "Node status\n", "---------------------------------------------------------------\n", "Active:\n", " 1 headgroup\n", "Pending:\n", " (no pending nodes)\n", "Recent failures:\n", " (no failures)\n", "\n", "Resources\n", "---------------------------------------------------------------\n", "Usage:\n", " 0.0/1.0 CPU\n", " 0B/2.79GiB memory\n", " 0B/738.05MiB object_store_memory\n", "\n", "Demands:\n", " (no resource demands)\n", "\u001b[0m\n"]}], "source": ["# Method 1: \"ray status\"\n", "kubectl exec $HEAD_POD -it -c ray-head -- ray status"]}, {"cell_type": "code", "execution_count": 21, "id": "1742fa82-b8b4-4c2d-b1f8-c44f5413de0a", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 0B/738.05MiB object_store_memory\n", "\n", "Demands:\n", " (no resource demands)\n", "2025-04-12 08:08:53,477 - INFO - StandardAutoscaler: Terminating the node with id raycluster-autoscaler-small-group-worker-2q8nc and ip **********. (idle)\n", "2025-04-12 08:08:53,477\tINFO autoscaler.py:589 -- StandardAutoscaler: Terminating the node with id raycluster-autoscaler-small-group-worker-2q8nc and ip **********. (idle)\n", "2025-04-12 08:08:53,477 - INFO - Node last used: Sat Apr 12 08:07:50 2025.\n", "2025-04-12 08:08:53,477\tINFO autoscaler.py:543 -- Node last used: Sat Apr 12 08:07:50 2025.\n", "2025-04-12 08:08:53,477 - INFO - Draining 1 raylet(s).\n", "2025-04-12 08:08:53,477\tINFO autoscaler.py:675 -- Draining 1 raylet(s).\n", "2025-04-12 08:08:53,478 - INFO - NodeProvider: raycluster-autoscaler-small-group-worker-2q8nc: Terminating node\n", "2025-04-12 08:08:53,478\tINFO node_provider.py:173 -- NodeProvider: raycluster-autoscaler-small-group-worker-2q8nc: Terminating node\n", "2025-04-12 08:08:53,478 - INFO - Autoscaler is submitting the following patch to RayCluster raycluster-autoscaler in namespace default.\n", "2025-04-12 08:08:53,478\tINFO node_provider.py:406 -- Autoscaler is submitting the following patch to RayCluster raycluster-autoscaler in namespace default.\n", "2025-04-12 08:08:53,478 - INFO - [{'op': 'replace', 'path': '/spec/workerGroupSpecs/0/replicas', 'value': 0}, {'op': 'replace', 'path': '/spec/workerGroupSpecs/0/scaleStrategy', 'value': {'workersToDelete': ['raycluster-autoscaler-small-group-worker-2q8nc']}}]\n", "2025-04-12 08:08:53,478\tINFO node_provider.py:410 -- [{'op': 'replace', 'path': '/spec/workerGroupSpecs/0/replicas', 'value': 0}, {'op': 'replace', 'path': '/spec/workerGroupSpecs/0/scaleStrategy', 'value': {'workersToDelete': ['raycluster-autoscaler-small-group-worker-2q8nc']}}]\n", "2025-04-12 08:08:53,488 - INFO - The autoscaler took 0.026 seconds to complete the update iteration.\n", "2025-04-12 08:08:53,488\tINFO autoscaler.py:461 -- The autoscaler took 0.026 seconds to complete the update iteration.\n", "2025-04-12 08:08:53,488 - INFO - :event_summary:Removing 1 nodes of type small-group (idle).\n", "2025-04-12 08:08:53,488\tINFO monitor.py:433 -- :event_summary:Removing 1 nodes of type small-group (idle).\n"]}], "source": ["# Method 2: \"kubectl logs\"\n", "kubectl logs $HEAD_POD -c autoscaler | tail -n 20"]}, {"cell_type": "markdown", "id": "f597678a-084c-4d97-8074-8d2b38648734", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["### Step 8: Clean up the Kubernetes cluster"]}, {"cell_type": "code", "execution_count": 22, "id": "92d74542-1984-4519-adde-641b05f9efe8", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["nbval-ignore-output", "remove-output"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Deleting cluster \"kind\" ...\n", "Deleted nodes: [\"kind-control-plane\"]\n"]}], "source": ["kind delete cluster"]}, {"cell_type": "markdown", "id": "53a89723-3117-404a-bd85-db7de81b4208", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["(kuberay-autoscaling-config)=\n", "## KubeRay Autoscaling Configurations\n", "\n", "The [ray-cluster.autoscaler.yaml](https://github.com/ray-project/kuberay/blob/v1.3.0/ray-operator/config/samples/ray-cluster.autoscaler.yaml) used in the quickstart example contains detailed comments about the configuration options.\n", "***It's recommended to read this section in conjunction with the YAML file.***\n", "\n", "### 1. <PERSON><PERSON><PERSON> autoscaling\n", "\n", "* **`enableInTreeAutoscaling`**: By setting `enableInTreeAutoscaling: true`, the KubeRay operator automatically configures an autoscaling sidecar container for the Ray head Pod.\n", "* **`minReplicas` / `maxReplicas` / `replicas`**:\n", "Set the `minReplicas` and `maxReplicas` fields to define the range for `replicas` in an autoscaling `workerGroup`.\n", "Typically, you would initialize both `replicas` and `minReplicas` with the same value during the deployment of an autoscaling cluster.\n", "Subsequently, the Ray Autoscaler adjusts the `replicas` field as it adds or removes Pods from the cluster.\n", "\n", "### 2. Scale-up and scale-down speed\n", "\n", "If necessary, you can regulate the pace of adding or removing nodes from the cluster.\n", "For applications with numerous short-lived tasks, considering a more conservative approach to adjusting the upscaling and downscaling speeds might be beneficial.\n", "\n", "Utilize the `RayCluster` CR's `autoscalerOptions` field to accomplish this. This field encompasses the following sub-fields:\n", "\n", "* **`upscalingMode`**: This controls the rate of scale-up process. The valid values are:\n", "  - `Conservative`: Upscaling is rate-limited; the number of pending worker Pods is at most the number of worker pods connected to the Ray cluster.\n", "  - `Default`: Upscaling isn't rate-limited.\n", "  - `Aggressive`: An alias for <PERSON><PERSON><PERSON>; upscaling isn't rate-limited.\n", "\n", "* **`idleTimeoutSeconds`** (default 60s):\n", "This denotes the waiting time in seconds before scaling down an idle worker pod.\n", "A worker node is idle when it has no active tasks, actors, or referenced objects, either stored in-memory or spilled to disk.\n", "\n", "### 3. Autoscaler sidecar container\n", "\n", "The `autoscalerOptions` field also provides options for configuring the Autoscaler container. Usually, it's not necessary to specify these options.\n", "\n", "* **`resources`**:\n", "The `resources` sub-field of `autoscalerOptions` sets optional resource overrides for the Autoscaler sidecar container.\n", "These overrides should be specified in the standard [container resource\n", "spec format](https://kubernetes.io/docs/reference/kubernetes-api/workload-resources/Pod-v1/#resources).\n", "The default values are indicated below:\n", "  ```yaml\n", "  resources:\n", "    limits:\n", "      cpu: \"500m\"\n", "      memory: \"512Mi\"\n", "    requests:\n", "      cpu: \"500m\"\n", "      memory: \"512Mi\"\n", "  ```\n", "\n", "* **`image`**:\n", "This field overrides the Autoscaler container image.\n", "The container uses the same **image** as the Ray container by default.\n", "\n", "* **`imagePullPolicy`**:\n", "This field overrides the Autoscaler container's image pull policy.\n", "The default is `IfNotPresent`.\n", "\n", "* **`env`** and **`envFrom`**:\n", "These fields specify Autoscaler container environment variables.\n", "These fields should be formatted following the [Kubernetes API](https://kubernetes.io/docs/reference/kubernetes-api/workload-resources/Pod-v1/#environment-variables)\n", "for container environment variables.\n", "\n", "### 4. Set the `rayStartParams` and the resource limits for the Ray container\n", "\n", "```{admonition} Resource limits are optional starting from Ray 2.41.0\n", "Starting from Ray 2.41.0, the Ray Autoscaler can read resource specifications from `rayStartParams`, resource limits, or resource requests of the Ray container. You must specify at least one of these fields.\n", "Earlier versions only support `rayStartParams` or resource limits, and don't recognize resource requests.\n", "```\n", "\n", "The Ray Autoscaler reads the `rayStartParams` field or the Ray container's resource limits in the RayCluster custom resource specification to determine the Ray Pod's resource requirements.\n", "The information regarding the number of CPUs is essential for the Ray Autoscaler to scale the cluster.\n", "Therefore, without this information, the Ray Autoscaler reports an error and fails to start.\n", "Take [ray-cluster.autoscaler.yaml](https://github.com/ray-project/kuberay/blob/v1.3.0/ray-operator/config/samples/ray-cluster.autoscaler.yaml) as an example below:\n", "\n", "* If users set `num-cpus` in `rayStartParams`, Ray Autoscaler would work regardless of the resource limits on the container.\n", "* If users don't set `rayStartParams`, the Ray container must have a specified CPU resource limit.\n", "\n", "```yaml\n", "headGroupSpec:\n", "  rayStartParams:\n", "    num-cpus: \"0\"\n", "  template:\n", "    spec:\n", "      containers:\n", "      - name: ray-head\n", "        resources:\n", "          # The Ray Autoscaler still functions if you comment out the `limits` field for the\n", "          # head container, as users have already specified `num-cpus` in `rayStartParams`.\n", "          limits:\n", "            cpu: \"1\"\n", "            memory: \"2G\"\n", "          requests:\n", "            cpu: \"1\"\n", "            memory: \"2G\"\n", "...\n", "workerGroupSpecs:\n", "- groupName: small-group\n", "  rayStartParams: {}\n", "  template:\n", "    spec:\n", "      containers:\n", "      - name: ray-worker\n", "        resources:\n", "          limits:\n", "            # The Ray Autoscaler versions older than 2.41.0 will fail to start if the CPU resource limit for the worker\n", "            # container is commented out because `rayStartParams` is empty.\n", "            # The Ray Autoscaler starting from 2.41.0 will not fail but use the resource requests if the resource\n", "            # limits are commented out and `rayStartParams` is empty.\n", "            cpu: \"1\"\n", "            memory: \"1G\"\n", "          requests:\n", "            cpu: \"1\"\n", "            memory: \"1G\"\n", "```\n", "\n", "\n", "## Next steps\n", "\n", "See [(Advanced) Understanding the Ray Autoscaler in the Context of Kubernetes](ray-k8s-autoscaler-comparison) for more details about the relationship between the Ray Autoscaler and Kubernetes autoscalers.\n", "\n", "(kuberay-autoscaler-v2)=\n", "### Autoscaler V2 with <PERSON><PERSON><PERSON><PERSON>\n", "\n", "#### Prerequisites\n", "\n", "* KubeRay v1.3.0 and the latest Ray version are the preferred setup for Autoscaler V2.\n", "\n", "The release of Ray 2.10.0 introduces the alpha version of Ray Autoscaler V2 integrated with KubeRay, bringing enhancements in terms of observability and stability:\n", "\n", "\n", "1. **Observability**: The Autoscaler V2 provides instance level tracing for each Ray worker's lifecycle, making it easier to debug and understand the Autoscaler behavior. It also reports the idle information about each node, including details on why nodes are idle or active:\n", "\n", "```bash\n", "\n", "> ray status -v\n", "\n", "======== Autoscaler status: 2024-03-08 21:06:21.023751 ========\n", "GCS request time: 0.003238s\n", "\n", "Node status\n", "---------------------------------------------------------------\n", "Active:\n", " 1 node_40f427230584b2d9c9f113d8db51d10eaf914aa9bf61f81dc7fabc64\n", "Idle:\n", " 1 node_2d5fd3d4337ba5b5a8c3106c572492abb9a8de2dee9da7f6c24c1346\n", "Pending:\n", " (no pending nodes)\n", "Recent failures:\n", " (no failures)\n", "\n", "Resources\n", "---------------------------------------------------------------\n", "Total Usage:\n", " 1.0/64.0 CPU\n", " 0B/72.63GiB memory\n", " 0B/33.53GiB object_store_memory\n", "\n", "Total Demands:\n", " (no resource demands)\n", "\n", "Node: 40f427230584b2d9c9f113d8db51d10eaf914aa9bf61f81dc7fabc64\n", " Usage:\n", "  1.0/32.0 CPU\n", "  0B/33.58GiB memory\n", "  0B/16.79GiB object_store_memory\n", " # New in autoscaler V2: activity information\n", " Activity:\n", "  Busy workers on node.\n", "  Resource: CPU currently in use.\n", "\n", "Node: 2d5fd3d4337ba5b5a8c3106c572492abb9a8de2dee9da7f6c24c1346\n", " # New in autoscaler V2: idle information\n", " Idle: 107356 ms\n", " Usage:\n", "  0.0/32.0 CPU\n", "  0B/39.05GiB memory\n", "  0B/16.74GiB object_store_memory\n", " Activity:\n", "  (no activity)\n", "```\n", "\n", "2. **Stability**\n", "Autoscaler V2 makes significant improvements to idle node handling. The V1 autoscaler could stop nodes that became active during termination processing, potentially failing tasks or actors. V2 uses <PERSON>'s graceful draining mechanism, which safely stops idle nodes without disrupting ongoing work.\n", "\n", "[ray-cluster.autoscaler-v2.yaml](https://github.com/ray-project/kuberay/blob/master/ray-operator/config/samples/ray-cluster.autoscaler-v2.yaml) is an example YAML file of a RayCluster with Autoscaler V2 enabled.\n", "\n", "```yaml\n", "# Change 1: Select the Ray version to either the nightly build or version 2.10.0+\n", "spec:\n", "  # Specify Ray version 2.10.0 or use the nightly build.\n", "  rayVersion: '2.X.Y'\n", "...\n", "\n", "\n", "# Change 2: Enable Autoscaler V2 by setting the RAY_enable_autoscaler_v2 environment variable on the Ray head container.\n", "  headGroupSpec:\n", "    template:\n", "      spec:\n", "        containers:\n", "        - name: ray-head\n", "          image: rayproject/ray:2.X.Y\n", "          # Include the environment variable.\n", "          env:\n", "            - name: RAY_enable_autoscaler_v2\n", "              value: \"1\"\n", "        restartPolicy: Never # Prevent container restart to maintain Ray health.\n", "\n", "\n", "# Change 3: Prevent Kubernetes from restarting Ray worker pod containers, enabling correct instance management by <PERSON>.\n", "  workerGroupSpecs:\n", "  - replicas: 1\n", "    template:\n", "      spec:\n", "        restartPolicy: Never\n", "        ...\n", "```"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "bash", "name": "bash"}, "language_info": {"codemirror_mode": "shell", "file_extension": ".sh", "mimetype": "text/x-sh", "name": "bash"}}, "nbformat": 4, "nbformat_minor": 5}