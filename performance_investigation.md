# Terrafloww Platform Performance Investigation

**Date**: 2025-06-26
**Scope**: NDVI Time Series Processing Performance Analysis
**Baseline**: 72 scenes processed in 123.20 seconds (8.04s average per Ray task)

## Executive Summary

Deep performance analysis reveals the root cause of the 20x performance gap compared to pure Python async (6 seconds vs 123 seconds). The bottleneck is **network I/O latency**, not computational overhead or scaling issues.

**Critical Findings:**
- 🚨 **Network I/O bottleneck**: 6.77s data fetching vs 0.06s NDVI computation (113x ratio)
- 📊 **Effective bandwidth**: ~0.4 MB/s (should be 10-100x faster)
- ✅ **Ray parallelism works perfectly**: 19 concurrent tasks, zero scheduling delays
- ✅ **COG spatial windowing is efficient**: 1-3MB downloads for 800MB scenes
- ✅ **NDVI computation is lightning fast**: 0.06s per spatial window
- ✅ **Arrow serialization is efficient**: Near zero-copy performance

## Performance Baseline

### Test Configuration
- **Dataset**: Bangalore AOI (77.55-77.58°E, 13.01-13.08°N)
- **Time Range**: Full year 2024 (72 Sentinel-2 L2A scenes)
- **Processing**: Red + NIR bands → NDVI calculation
- **Total Time**: 123.20 seconds
- **Ray Tasks**: 254 total executions, 8.04s average duration

### Ray Timeline Analysis
- **Maximum concurrent tasks**: 19 (excellent parallelism)
- **Average concurrent tasks**: 10.4
- **Workers utilized**: 18 total workers
- **Scheduling delays**: 0.000s (Ray scheduling is perfect)
- **Task duration range**: 3.47s - 10.93s

## Root Cause Analysis

### Single Ray Task Breakdown (8.15s total)
From detailed log analysis of worker pid=83:

1. **Worker async logic starting**: 01:19:32,566
2. **Data fetching (2 COG tiles)**: 01:19:32,577 → 01:19:39,343 (**6.77s - 83% of time**)
3. **NDVI computation**: ~0.06s (0.7% of time)
4. **Flight upload**: ~0.02s (0.2% of time)
5. **Worker async logic finished**: 01:19:40,651

### Network I/O Analysis
- **Data transferred**: ~3MB per task (1.3MB B04 + 1.6MB B08)
- **Effective bandwidth**: 3MB ÷ 6.77s = **0.44 MB/s**
- **Expected bandwidth**: 10-100 MB/s (20-200x faster)
- **Bottleneck**: Network latency between DigitalOcean K8s → AWS S3 us-west-2

## Corrected Understanding

### What's Working Perfectly
1. **COG Spatial Windowing**: 1-3MB downloads from 800MB scenes (99.6% efficiency)
2. **Ray Parallelism**: 19 concurrent tasks, zero scheduling overhead
3. **NDVI Computation**: 0.06s per spatial window (lightning fast)
4. **Arrow Serialization**: Near zero-copy list format
5. **Different Spatial Windows**: Workers process different scenes/dates (no redundancy)

### What's NOT the Problem
- ❌ **Scaling**: Ray auto-scaling works perfectly
- ❌ **COG Downloads**: Spatial windowing is highly efficient
- ❌ **Redundant I/O**: Workers process different spatial windows
- ❌ **Serialization**: Arrow format is optimized
- ❌ **Head Node Config**: 0 CPUs is Anyscale best practice

## Detailed Performance Analysis

### Ray Worker Execution Pattern
**Evidence from logs**: Each worker processes **2 window specs** (spatial chunks) per task

**Sample Task Timeline (pid=83)**:
```
01:19:32,566 - Worker async logic starting for 2 window specs
01:19:32,577 - Fetching data for 2 URLs concurrently...
01:19:39,343 - Fetching complete (6.77s data loading)
01:19:40,651 - Worker async logic finished (8.15s total)
```

### HTTP Request Analysis
**COG Downloads per task**:
- B04.tif (Red band): ~1.3MB via range request
- B08.tif (NIR band): ~1.6MB via range request
- **Total**: ~3MB per task
- **Source**: sentinel-cogs.s3.us-west-2.amazonaws.com
- **Method**: HTTP/1.1 206 Partial Content (efficient spatial windowing)

### Network Latency Root Cause

**Problem**: 0.44 MB/s effective bandwidth for S3 downloads
**Expected**: 10-100 MB/s (20-200x faster)

**Evidence**:
- Content-Range: bytes *********-*********/********* (1.3MB in 6.77s)
- Multiple HTTP connections per task (B04.tif + B08.tif)
- Cross-region latency: DigitalOcean → AWS us-west-2

### Why Pure Python Async is 20x Faster (6s vs 123s)

**Likely optimizations in pure Python**:
1. **HTTP Connection Pooling**: Reuses connections across requests
2. **Better HTTP Client**: aiohttp vs httpx performance differences
3. **Local Caching**: Repeated access to same COG tiles
4. **Network Path**: Different routing or CDN usage
5. **Concurrent Downloads**: More efficient async I/O patterns

## Critical Performance Issues

### 1. Network I/O Latency (Primary Bottleneck)
- **Impact**: 6.77s data loading vs 0.06s computation (113x ratio)
- **Root Cause**: 0.44 MB/s effective bandwidth (should be 10-100x faster)
- **Evidence**: Cross-region latency DigitalOcean K8s → AWS S3 us-west-2
- **Solution Priority**: HIGH - Network optimization needed

### 2. HTTP Connection Inefficiency
- **Impact**: New connections for each COG download
- **Evidence**: Multiple TCP handshakes per task in logs
- **Missing**: Connection pooling and keep-alive optimization
- **Solution Priority**: HIGH - Implement connection pooling

### 3. Suboptimal HTTP Client Configuration
- **Impact**: httpx may not be optimized for high-latency scenarios
- **Evidence**: Pure Python async achieves 20x better performance
- **Missing**: Optimized async HTTP client settings
- **Solution Priority**: MEDIUM - HTTP client optimization

## Optimization Recommendations

### High Impact (10x+ improvement potential)
1. **🌐 HTTP Connection Pooling**
   - **Action**: Implement persistent HTTP connections across requests
   - **Implementation**: Configure httpx with connection pooling
   - **Expected Gain**: 5-10x reduction in connection overhead
   - **Effort**: Medium (code changes)

2. **🚀 Network Path Optimization**
   - **Action**: Use S3 Transfer Acceleration or CloudFront CDN
   - **Implementation**: Update S3 endpoints to use accelerated transfer
   - **Expected Gain**: 3-5x bandwidth improvement
   - **Effort**: Low (configuration change)

3. **⚡ Async HTTP Client Optimization**
   - **Action**: Switch to aiohttp or optimize httpx configuration
   - **Implementation**: Replace HTTP client with optimized settings
   - **Expected Gain**: 2-3x request efficiency
   - **Effort**: Medium (code changes)

### Medium Impact (2-5x improvement potential)
4. **� COG Tile Caching**
   - **Action**: Cache frequently accessed COG tiles
   - **Implementation**: Redis or shared volume cache
   - **Expected Gain**: 2-5x for repeated spatial windows
   - **Effort**: High (infrastructure changes)

### Target Performance
**Goal**: Achieve 6-second performance (20x improvement) by addressing network I/O bottleneck

## Technical Evidence

### Ray Timeline Data
- **Total events analyzed**: 1,072 timeline events
- **Task executions**: 254 total
- **Duration statistics**:
  - Average: 8.04s
  - Median: 9.00s
  - Range: 3.47s - 10.93s
  - Standard deviation: 2.26s

### Worker Distribution
**18 workers utilized with task distribution**:
- Worker 0: 28 tasks, 8.11s avg
- Worker 3: 38 tasks, 7.91s avg
- Worker 8: 36 tasks, 8.33s avg
- Others: 2-24 tasks each

### Longest Running Tasks
1. Task f090d516: 10.93s
2. Task d98c35d9: 10.71s
3. Task deaf5df7: 10.56s
4. Task 6e34b295: 10.51s
5. Task 1bb72792: 10.46s

## Key Insights

### What Works Well
1. **Ray Parallelism**: 19 concurrent tasks, zero scheduling delays
2. **COG Efficiency**: 99.6% spatial windowing efficiency (3MB from 800MB scenes)
3. **NDVI Speed**: 0.06s computation time (lightning fast)
4. **Arrow Format**: Near zero-copy serialization
5. **Auto-scaling**: Ray scales from 1→18 workers seamlessly

### The Real Problem
**Network I/O latency dominates execution time**:
- 6.77s data loading vs 0.06s computation (113:1 ratio)
- 0.44 MB/s effective bandwidth (should be 10-100x faster)
- Cross-region latency: DigitalOcean → AWS us-west-2

### Comparison to Pure Python Async
**20x performance gap (123s vs 6s) likely due to**:
1. HTTP connection pooling
2. Better async I/O patterns
3. Local caching strategies
4. Optimized HTTP client configuration
5. Different network routing

## Implementation Priority

### Week 1: HTTP Optimization
1. Implement connection pooling in worker code
2. Configure httpx for persistent connections
3. Test S3 Transfer Acceleration

### Month 1: Network Infrastructure
1. Deploy CloudFront CDN for COG access
2. Implement Redis caching layer
3. Optimize async HTTP client

### Target: 6-second NDVI time series processing (20x improvement)
