[ ] NAME:Platform Deployment - Cloud-Native Integration DESCRIPTION:Complete platform deployment with cloud-native STAC catalog and end-to-end integration testing
-[x] NAME:TF-21: Create custom Ray Docker image with platform dependencies DESCRIPTION:Build custom Ray image with geospatial dependencies and platform libraries (tfw_engine_core, tfw_ray_utils). Target <2GB image size with Ray 2.9.0 base.
-[x] NAME:TF-20: Install KubeRay Operator and deploy Ray cluster DESCRIPTION:Deploy KubeRay operator on DigitalOcean K8s and create RayCluster with auto-scaling (1-5 workers) using the custom Ray image.
-[x] NAME:TF-22: Deploy processing engine as Kubernetes service DESCRIPTION:Create K8s deployment and service manifests for processing engine with gRPC (50051) and Flight (50052) ports exposed.
-[x] NAME:Adapt test_ndvi.py for cloud endpoints DESCRIPTION:Modify test_ndvi.py to use K8s service endpoints instead of localhost, with environment variable support for flexible configuration.
-[/] NAME:TF-70: Implement Cloud-Native STAC Delta Table Catalog DESCRIPTION:Critical blocker - implement cloud-native STAC catalog on DigitalOcean Spaces to replace local file system dependency. Highest priority.
--[/] NAME:TF-71: Configure DigitalOcean Spaces bucket for catalog storage DESCRIPTION:Create DigitalOcean Spaces bucket, configure access credentials, create Kubernetes secrets. Foundation for cloud catalog. (2-3 hours)
--[ ] NAME:TF-72: Refactor catalog client for S3-compatible cloud storage DESCRIPTION:Major code refactoring - update catalog_client.py, planner.py, deployment configs for cloud storage. Add S3 dependencies. (4-6 hours)
---[ ] NAME:Add S3 dependencies and configuration DESCRIPTION:Update requirements.txt with s3fs/boto3, add S3 configuration classes to catalog_client.py
---[ ] NAME:Implement S3StorageBackend in catalog_client.py DESCRIPTION:Create S3StorageBackend class with error handling and retry logic, modify CatalogClient for multiple backends
---[ ] NAME:Update planner.py for cloud catalog paths DESCRIPTION:Modify catalog path initialization and error handling for network-based catalog access
---[ ] NAME:Update Kubernetes deployment configuration DESCRIPTION:Add S3 environment variables to processing-engine-deployment.yaml, create Kubernetes secrets
---[ ] NAME:Rebuild and redeploy processing engine DESCRIPTION:Build new processing engine image with S3 support, deploy to Kubernetes, validate connectivity
--[ ] NAME:TF-73: Populate cloud STAC Delta Table with Sentinel-2 metadata DESCRIPTION:Create data ingestion pipeline, populate Delta Lake tables in cloud storage, validate data accessibility. (2-3 hours)
-[ ] NAME:TF-19: Complete end-to-end integration testing DESCRIPTION:Validate Flight/SDK integration works with cloud catalog. Blocked until TF-70 completion. (1-2 hours)
--[ ] NAME:Validate end-to-end NDVI workflow with cloud catalog DESCRIPTION:Test complete workflow from SDK through cloud processing engine to cloud catalog
--[ ] NAME:TF-53: Native Flight client API tests DESCRIPTION:Test direct PyArrow Flight operations, schema validation, data transfer protocols
--[ ] NAME:TF-54: Failure simulation and retry tests DESCRIPTION:Test failure scenarios, network interruptions, retry logic, error handling
--[ ] NAME:TF-55: Performance and concurrency testing DESCRIPTION:Benchmark performance, test concurrent operations, validate scalability