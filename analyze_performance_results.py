# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Performance Analysis Script

Analyzes the collected performance metrics and logs to generate
insights and recommendations for optimization.
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, List


def load_performance_metrics(filename: str) -> Dict[str, Any]:
    """Load performance metrics from JSON file."""
    with open(filename, 'r') as f:
        return json.load(f)


def analyze_timing_breakdown(metrics: Dict[str, Any]) -> None:
    """Analyze timing breakdown from performance metrics."""
    print("🔍 TIMING BREAKDOWN ANALYSIS")
    print("=" * 50)
    
    total_duration = metrics['total_duration']
    phases = metrics['phases']
    
    print(f"Total Test Duration: {total_duration:.2f} seconds")
    print("\nPhase Breakdown:")
    print("-" * 30)
    
    for phase_name, phase_data in phases.items():
        if 'duration' in phase_data:
            duration = phase_data['duration']
            percentage = (duration / total_duration) * 100
            print(f"{phase_name:>20}: {duration:>8.2f}s ({percentage:>5.1f}%)")
    
    # Analyze backend processing details
    if 'backend_processing' in phases:
        backend_info = phases['backend_processing'].get('info', {})
        if 'total_scenes' in backend_info:
            scenes = backend_info['total_scenes']
            backend_duration = phases['backend_processing']['duration']
            rate = scenes / backend_duration
            print(f"\nBackend Processing Analysis:")
            print(f"  Scenes Processed: {scenes}")
            print(f"  Processing Rate: {rate:.2f} scenes/second")
            print(f"  Time per Scene: {backend_duration/scenes:.2f} seconds/scene")


def analyze_detailed_timings(metrics: Dict[str, Any]) -> None:
    """Analyze detailed timing measurements."""
    print("\n🔬 DETAILED TIMING ANALYSIS")
    print("=" * 50)
    
    detailed_timings = metrics.get('detailed_timings', [])
    
    if not detailed_timings:
        print("No detailed timings available")
        return
    
    # Group timings by operation
    timing_groups = {}
    for timing in detailed_timings:
        operation = timing['operation']
        duration = timing['duration']
        
        if operation not in timing_groups:
            timing_groups[operation] = []
        timing_groups[operation].append(duration)
    
    print("Operation Timing Summary:")
    print("-" * 40)
    
    for operation, durations in timing_groups.items():
        avg_duration = sum(durations) / len(durations)
        total_duration = sum(durations)
        count = len(durations)
        
        print(f"{operation:>25}: {avg_duration:>8.3f}s avg, {total_duration:>8.3f}s total ({count} calls)")


def analyze_data_quality(metrics: Dict[str, Any]) -> None:
    """Analyze data quality metrics."""
    print("\n📊 DATA QUALITY ANALYSIS")
    print("=" * 50)
    
    phases = metrics.get('phases', {})
    
    # Backend processing info
    if 'backend_processing' in phases:
        backend_info = phases['backend_processing'].get('info', {})
        print("Backend Processing:")
        print(f"  Total Scenes: {backend_info.get('total_scenes', 'N/A')}")
        print(f"  Result Columns: {backend_info.get('columns', 'N/A')}")
    
    # Data processing info
    if 'data_processing' in phases:
        processing_info = phases['data_processing'].get('info', {})
        print("\nData Processing:")
        print(f"  Unique Dates: {processing_info.get('unique_dates', 'N/A')}")
        print(f"  NDVI Range: {processing_info.get('ndvi_range', 'N/A')}")
        
        breakdown = processing_info.get('processing_breakdown', {})
        if breakdown:
            print("  Processing Breakdown:")
            for step, duration in breakdown.items():
                print(f"    {step}: {duration:.3f}s")
    
    # Results analysis
    if 'results_analysis' in phases:
        results_info = phases['results_analysis'].get('info', {})
        print("\nResults Analysis:")
        print(f"  Total Pixels: {results_info.get('total_pixels', 'N/A'):,}")
        print(f"  Valid Pixels: {results_info.get('valid_pixels', 'N/A'):,}")
        print(f"  Validity Rate: {results_info.get('pixel_validity_rate', 0)*100:.1f}%")
        
        seasonal = results_info.get('seasonal_analysis', {})
        if seasonal:
            print("  Seasonal NDVI:")
            for season, ndvi in seasonal.items():
                print(f"    {season.capitalize()}: {ndvi:.3f}")


def generate_optimization_recommendations() -> None:
    """Generate optimization recommendations based on analysis."""
    print("\n🚀 OPTIMIZATION RECOMMENDATIONS")
    print("=" * 50)
    
    recommendations = [
        {
            'priority': 'HIGH',
            'title': 'Enable Ray Head Node Computation',
            'description': 'Configure head node with --num-cpus=2 instead of --num-cpus=0',
            'impact': '+25% compute capacity',
            'effort': 'Low (config change)'
        },
        {
            'priority': 'HIGH', 
            'title': 'Implement COG Data Caching',
            'description': 'Add shared cache to avoid redundant S3 downloads',
            'impact': '60-80% reduction in data loading time',
            'effort': 'Medium (infrastructure)'
        },
        {
            'priority': 'MEDIUM',
            'title': 'Optimize Worker Scaling',
            'description': 'Start with more workers to avoid auto-scaling delay',
            'impact': '10-20% faster job startup',
            'effort': 'Low (config change)'
        },
        {
            'priority': 'MEDIUM',
            'title': 'Implement Data Locality',
            'description': 'Schedule tasks based on data location to reduce transfers',
            'impact': '15-25% reduction in network I/O',
            'effort': 'High (algorithm changes)'
        }
    ]
    
    for rec in recommendations:
        print(f"\n[{rec['priority']}] {rec['title']}")
        print(f"  Description: {rec['description']}")
        print(f"  Impact: {rec['impact']}")
        print(f"  Effort: {rec['effort']}")


def main():
    """Main analysis function."""
    print("📊 TERRAFLOWW PERFORMANCE ANALYSIS")
    print("=" * 60)
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Find the latest performance metrics file
    metrics_files = [f for f in os.listdir('.') if f.startswith('performance_metrics_') and f.endswith('.json')]
    
    if not metrics_files:
        print("❌ No performance metrics files found")
        return
    
    latest_file = sorted(metrics_files)[-1]
    print(f"📁 Analyzing: {latest_file}")
    
    try:
        metrics = load_performance_metrics(latest_file)
        
        analyze_timing_breakdown(metrics)
        analyze_detailed_timings(metrics)
        analyze_data_quality(metrics)
        generate_optimization_recommendations()
        
        print("\n" + "=" * 60)
        print("✅ Analysis Complete!")
        print("📋 See performance_investigation.md for detailed findings")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error analyzing metrics: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
