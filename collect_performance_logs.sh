#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Performance Investigation Log Collection Script
# Collects logs from all relevant Kubernetes pods and Ray components

set -e

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_DIR="performance_logs_${TIMESTAMP}"

echo "🔍 Starting performance log collection..."
echo "📁 Creating log directory: ${LOG_DIR}"
mkdir -p "${LOG_DIR}"

# Function to collect pod logs
collect_pod_logs() {
    local pod_name=$1
    local container_name=$2
    local log_file=$3
    
    echo "📋 Collecting logs from ${pod_name}/${container_name}..."
    
    if [ -n "$container_name" ]; then
        kubectl logs "$pod_name" -n terrafloww-platform -c "$container_name" --tail=1000 > "${LOG_DIR}/${log_file}" 2>&1 || echo "Failed to collect logs from ${pod_name}/${container_name}"
    else
        kubectl logs "$pod_name" -n terrafloww-platform --tail=1000 > "${LOG_DIR}/${log_file}" 2>&1 || echo "Failed to collect logs from ${pod_name}"
    fi
}

# Function to collect pod descriptions
collect_pod_descriptions() {
    local pod_name=$1
    local desc_file=$2
    
    echo "📝 Collecting description for ${pod_name}..."
    kubectl describe pod "$pod_name" -n terrafloww-platform > "${LOG_DIR}/${desc_file}" 2>&1 || echo "Failed to describe ${pod_name}"
}

# Function to collect resource usage
collect_resource_usage() {
    echo "📊 Collecting resource usage..."
    
    # Pod resource usage
    kubectl top pods -n terrafloww-platform > "${LOG_DIR}/pod_resource_usage.txt" 2>&1 || echo "kubectl top pods failed"
    
    # Node resource usage
    kubectl top nodes > "${LOG_DIR}/node_resource_usage.txt" 2>&1 || echo "kubectl top nodes failed"
    
    # Pod status
    kubectl get pods -n terrafloww-platform -o wide > "${LOG_DIR}/pod_status.txt" 2>&1
    
    # Ray cluster status
    kubectl get raycluster -n terrafloww-platform -o yaml > "${LOG_DIR}/ray_cluster_config.yaml" 2>&1 || echo "Failed to get RayCluster config"
}

# Collect Processing Engine logs
echo "🔧 Collecting Processing Engine logs..."
PROCESSING_ENGINE_POD=$(kubectl get pods -n terrafloww-platform -l app=terrafloww,component=processing-engine -o jsonpath='{.items[0].metadata.name}')
if [ -n "$PROCESSING_ENGINE_POD" ]; then
    collect_pod_logs "$PROCESSING_ENGINE_POD" "" "processing_engine_logs.txt"
    collect_pod_descriptions "$PROCESSING_ENGINE_POD" "processing_engine_description.txt"
else
    echo "❌ Processing Engine pod not found"
fi

# Collect Ray Head logs
echo "🧠 Collecting Ray Head logs..."
RAY_HEAD_POD=$(kubectl get pods -n terrafloww-platform -l ray.io/node-type=head -o jsonpath='{.items[0].metadata.name}')
if [ -n "$RAY_HEAD_POD" ]; then
    collect_pod_logs "$RAY_HEAD_POD" "ray-head" "ray_head_logs.txt"
    collect_pod_logs "$RAY_HEAD_POD" "autoscaler" "ray_autoscaler_logs.txt"
    collect_pod_descriptions "$RAY_HEAD_POD" "ray_head_description.txt"
else
    echo "❌ Ray Head pod not found"
fi

# Collect Ray Worker logs
echo "👷 Collecting Ray Worker logs..."
RAY_WORKER_PODS=$(kubectl get pods -n terrafloww-platform -l ray.io/node-type=worker -o jsonpath='{.items[*].metadata.name}')
if [ -n "$RAY_WORKER_PODS" ]; then
    for worker_pod in $RAY_WORKER_PODS; do
        worker_index=$(echo "$worker_pod" | grep -o '[^-]*$')
        collect_pod_logs "$worker_pod" "ray-worker" "ray_worker_${worker_index}_logs.txt"
        collect_pod_descriptions "$worker_pod" "ray_worker_${worker_index}_description.txt"
    done
else
    echo "❌ No Ray Worker pods found"
fi

# Collect resource usage
collect_resource_usage

# Collect Ray cluster information via Ray CLI (if accessible)
echo "📡 Collecting Ray cluster status..."
if command -v ray &> /dev/null; then
    # Try to get Ray status (requires Ray head to be accessible)
    timeout 10 ray status --address=127.0.0.1:6379 > "${LOG_DIR}/ray_status.txt" 2>&1 || echo "Ray status command failed or timed out"
else
    echo "Ray CLI not available"
fi

# Collect Kubernetes events
echo "📅 Collecting Kubernetes events..."
kubectl get events -n terrafloww-platform --sort-by='.lastTimestamp' > "${LOG_DIR}/kubernetes_events.txt" 2>&1

# Create summary file
echo "📋 Creating summary file..."
cat > "${LOG_DIR}/collection_summary.txt" << EOF
Performance Log Collection Summary
==================================
Timestamp: ${TIMESTAMP}
Collection Date: $(date)

Collected Components:
- Processing Engine: ${PROCESSING_ENGINE_POD:-"NOT FOUND"}
- Ray Head: ${RAY_HEAD_POD:-"NOT FOUND"}  
- Ray Workers: ${RAY_WORKER_PODS:-"NOT FOUND"}

Log Files:
- processing_engine_logs.txt
- ray_head_logs.txt
- ray_autoscaler_logs.txt
- ray_worker_*_logs.txt
- pod_resource_usage.txt
- node_resource_usage.txt
- pod_status.txt
- ray_cluster_config.yaml
- kubernetes_events.txt

Analysis Notes:
- Check ray_head_logs.txt for task scheduling
- Check ray_worker_*_logs.txt for task execution
- Check processing_engine_logs.txt for workflow orchestration
- Check pod_resource_usage.txt for resource bottlenecks
EOF

echo "✅ Log collection complete!"
echo "📁 Logs saved to: ${LOG_DIR}/"
echo "📋 Summary: ${LOG_DIR}/collection_summary.txt"

# Make the directory easily accessible
ln -sf "${LOG_DIR}" "latest_performance_logs"
echo "🔗 Symlink created: latest_performance_logs -> ${LOG_DIR}"
