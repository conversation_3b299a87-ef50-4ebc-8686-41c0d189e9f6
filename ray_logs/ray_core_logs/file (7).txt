[2025-06-26 01:17:12,271 I 112 112] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 112
[2025-06-26 01:17:12,274 I 112 112] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-06-26 01:17:12,277 I 112 112] grpc_server.cc:141: driver server started, listening on port 10005.
[2025-06-26 01:17:12,280 I 112 112] core_worker.cc:542: Initializing worker at address: ************:10005 worker_id=04000000ffffffffffffffffffffffffffffffffffffffffffffffff node_id=11d1fceddfd4a7d70539f452d9f1c4f91705b1be5f541b4b015ed1f5
[2025-06-26 01:17:12,282 I 112 112] task_event_buffer.cc:287: Reporting task events to GCS every 1000ms.
[2025-06-26 01:17:12,284 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 13 total (8 active)
Queueing time: mean = 12.774 us, max = 68.925 us, min = 44.182 us, total = 166.064 us
Execution time:  mean = 178.968 us, total = 2.327 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 6 total (5 active, 1 running), Execution time: mean = 3.968 us, total = 23.809 us, Queueing time: mean = 7.364 us, max = 44.182 us, min = 44.182 us, total = 44.182 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 533.834 us, total = 533.834 us, Queueing time: mean = 68.925 us, max = 68.925 us, min = 68.925 us, total = 68.925 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 766.392 us, total = 766.392 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 13.902 us, max = 37.945 us, min = 17.665 us, total = 55.610 us
Execution time:  mean = 477.142 us, total = 1.909 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 31.386 us, total = 31.386 us, Queueing time: mean = 37.945 us, max = 37.945 us, min = 37.945 us, total = 37.945 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 1.681 ms, total = 1.681 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 1
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=11d1fceddfd4a7d70539f452d9f1c4f91705b1be5f541b4b015ed1f5
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:5107: Number of alive nodes:1
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=374025dd58ceacb2d89386dc3a38408dc32b1456276f0fcd84952fd1
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=374025dd58ceacb2d89386dc3a38408dc32b1456276f0fcd84952fd1
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=107d8554c9ce898888fbcca7095612438976ed120c786ea7a3b82940
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=107d8554c9ce898888fbcca7095612438976ed120c786ea7a3b82940
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=6985e19ea27e856c07dc8ab917ef47ca7ac856fdd8daa6e3682941a9
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=6985e19ea27e856c07dc8ab917ef47ca7ac856fdd8daa6e3682941a9
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=e90de5051af7eab045a5db8c90b1509053a14387b123a1d3416ab7b2
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=e90de5051af7eab045a5db8c90b1509053a14387b123a1d3416ab7b2
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=aeb88ee76014cf968e47e7c5541354741a624a0d7183bbd80e7cc5f8
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=aeb88ee76014cf968e47e7c5541354741a624a0d7183bbd80e7cc5f8
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=8c4d80205327b4f323d879c99025b7243851b812f3c5de83998be538
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=8c4d80205327b4f323d879c99025b7243851b812f3c5de83998be538
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=6d647dce398845972a47ce100b2b655a442fcea0c1e67051f484861f
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=6d647dce398845972a47ce100b2b655a442fcea0c1e67051f484861f
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=c60c555fc603cb22a64b905e730341251113eb722cf57f73c495e3fb
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=c60c555fc603cb22a64b905e730341251113eb722cf57f73c495e3fb
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=77967f0e6d34a5fa26747c3c66d5a8d7b80570e71ff0bfb0320af5c8
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=77967f0e6d34a5fa26747c3c66d5a8d7b80570e71ff0bfb0320af5c8
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=71f196cf3e35c23d0086046ebee4d8b57afb729e123bf51e32afab43
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=71f196cf3e35c23d0086046ebee4d8b57afb729e123bf51e32afab43
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=45dcdd8fc5701ea50b0cef58c35281c586bc903f5a425e0f3c9ed66e
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=45dcdd8fc5701ea50b0cef58c35281c586bc903f5a425e0f3c9ed66e
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=22dcf4c94eb49e5029d861bbd4193e4e05721c90987ce6702bef89a1
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=22dcf4c94eb49e5029d861bbd4193e4e05721c90987ce6702bef89a1
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=5ee3f7e3f9d46150aadf44e4c4321a8a15c86ea8af08e6b6653774a2
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=5ee3f7e3f9d46150aadf44e4c4321a8a15c86ea8af08e6b6653774a2
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=f91843d4faa36d81732f614a02a8f8db1a8afd5e9f428b1d4271e85a
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=f91843d4faa36d81732f614a02a8f8db1a8afd5e9f428b1d4271e85a
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,285 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=cc56480aa1d01d213e5567748e50a120eecea0f10356a110b2b49300
[2025-06-26 01:17:12,285 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=cc56480aa1d01d213e5567748e50a120eecea0f10356a110b2b49300
[2025-06-26 01:17:12,285 W 112 701599] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:12,287 I 112 112] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-06-26 01:17:12,287 I 112 112] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-06-26 01:17:12,288 I 112 112] event.cc:331: Set ray event level to warning
[2025-06-26 01:17:13,128 W 112 112] actor_manager.cc:110: Failed to look up actor with name 'datasets_stats_actor'. This could because 1. You are trying to look up a named actor you didn't create. 2. The named actor died. 3. You did not use a namespace matching the namespace of the actor.
[2025-06-26 01:17:13,129 W 112 112] actor_manager.cc:110: Failed to look up actor with name 'datasets_stats_actor'. This could because 1. You are trying to look up a named actor you didn't create. 2. The named actor died. 3. You did not use a namespace matching the namespace of the actor.
[2025-06-26 01:17:13,321 I 112 112] actor_task_submitter.cc:73: Set actor max pending calls to -1 actor_id=95868628bee24beed493ac7204000000
[2025-06-26 01:17:13,326 I 112 701599] actor_manager.cc:218: received notification on actor, state: PENDING_CREATION, ip address: , port: 0, num_restarts: 0, death context type=CONTEXT_NOT_SET actor_id=95868628bee24beed493ac7204000000 worker_id=NIL_ID node_id=11d1fceddfd4a7d70539f452d9f1c4f91705b1be5f541b4b015ed1f5
[2025-06-26 01:17:14,718 I 112 701599] actor_manager.cc:218: received notification on actor, state: ALIVE, ip address: ************, port: 10006, num_restarts: 0, death context type=CONTEXT_NOT_SET actor_id=95868628bee24beed493ac7204000000 worker_id=b52478d950e8d9c1f73e090a2abd4f2a5feafb005feb31be89ace1a9 node_id=11d1fceddfd4a7d70539f452d9f1c4f91705b1be5f541b4b015ed1f5
[2025-06-26 01:17:14,983 W 112 112] reference_count.cc:431: Tried to decrease ref count for nonexistent object ID: ffffffffffffffff95868628bee24beed493ac720400000001000000
[2025-06-26 01:18:12,285 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 889 total (7 active)
Queueing time: mean = 101.060 us, max = 20.312 ms, min = -0.000 s, total = 89.842 ms
Execution time:  mean = 4.524 ms, total = 4.022 s
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 14.074 us, total = 8.445 ms, Queueing time: mean = 123.027 us, max = 20.312 ms, min = -0.000 s, total = 73.816 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 1.053 ms, total = 63.200 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 25.104 us, total = 1.506 ms, Queueing time: mean = 31.791 us, max = 69.599 us, min = 13.429 us, total = 1.907 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 228.452 us, total = 13.707 ms, Queueing time: mean = 77.298 us, max = 348.213 us, min = 20.676 us, total = 4.638 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 13 total (0 active), Execution time: mean = 395.357 us, total = 5.140 ms, Queueing time: mean = 60.059 us, max = 76.222 us, min = 43.863 us, total = 780.766 us
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 13 total (0 active), Execution time: mean = 88.122 us, total = 1.146 ms, Queueing time: mean = 36.261 us, max = 83.721 us, min = 18.356 us, total = 471.392 us
	CoreWorkerService.grpc_client.PushTask - 13 total (0 active), Execution time: mean = 6.131 ms, total = 79.702 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ActorTaskSubmitter::SubmitTask - 13 total (0 active), Execution time: mean = 261.522 us, total = 3.400 ms, Queueing time: mean = 99.784 us, max = 206.405 us, min = 35.725 us, total = 1.297 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 177.155 us, total = 2.126 ms, Queueing time: mean = 83.365 us, max = 271.662 us, min = 11.584 us, total = 1.000 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 7.931 us, total = 47.588 us, Queueing time: mean = 48.288 us, max = 82.415 us, min = 38.157 us, total = 289.725 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 4 total (0 active), Execution time: mean = 50.849 us, total = 203.394 us, Queueing time: mean = 275.892 us, max = 789.217 us, min = 21.477 us, total = 1.104 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 4 total (0 active), Execution time: mean = 639.006 us, total = 2.556 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 1.217 s, total = 2.435 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 308.622 us, total = 308.622 us, Queueing time: mean = 25.924 us, max = 25.924 us, min = 25.924 us, total = 25.924 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 42.829 us, max = 658.748 us, min = 12.207 us, total = 7.752 ms
Execution time:  mean = 407.110 us, total = 73.687 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 273.640 us, total = 16.418 ms, Queueing time: mean = 82.877 us, max = 456.389 us, min = 28.899 us, total = 4.973 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 35.808 us, total = 2.148 ms, Queueing time: mean = 46.031 us, max = 658.748 us, min = 12.207 us, total = 2.762 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 915.401 us, total = 54.924 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.00578785 MiB
	total number of task attempts sent: 31
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:18:34,283 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:18:34,283 I 112 701599] core_worker.cc:5107: Number of alive nodes:1
[2025-06-26 01:18:35,715 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:18:36,626 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:19:12,286 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 1744 total (7 active)
Queueing time: mean = 86.851 us, max = 20.312 ms, min = -0.000 s, total = 151.468 ms
Execution time:  mean = 49.333 ms, total = 86.037 s
Event stats:
	CoreWorker.RecoverObjects - 1199 total (1 active), Execution time: mean = 13.804 us, total = 16.551 ms, Queueing time: mean = 101.915 us, max = 20.312 ms, min = -0.000 s, total = 122.196 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 231.728 us, total = 27.807 ms, Queueing time: mean = 89.097 us, max = 541.964 us, min = 20.676 us, total = 10.692 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 979.080 us, total = 117.490 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 26.152 us, total = 3.138 ms, Queueing time: mean = 33.805 us, max = 186.175 us, min = 12.514 us, total = 4.057 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 124.537 us, total = 2.989 ms, Queueing time: mean = 80.974 us, max = 271.662 us, min = 11.584 us, total = 1.943 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 23 total (0 active), Execution time: mean = 385.067 us, total = 8.857 ms, Queueing time: mean = 57.512 us, max = 76.222 us, min = 43.863 us, total = 1.323 ms
	ActorTaskSubmitter::SubmitTask - 23 total (0 active), Execution time: mean = 264.997 us, total = 6.095 ms, Queueing time: mean = 96.869 us, max = 206.405 us, min = 30.810 us, total = 2.228 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 23 total (0 active), Execution time: mean = 85.231 us, total = 1.960 ms, Queueing time: mean = 38.343 us, max = 91.922 us, min = 18.231 us, total = 881.882 us
	CoreWorkerService.grpc_client.PushTask - 23 total (0 active), Execution time: mean = 4.237 ms, total = 97.443 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 10.603 us, total = 127.240 us, Queueing time: mean = 53.386 us, max = 82.415 us, min = 16.955 us, total = 640.634 us
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 8 total (0 active), Execution time: mean = 557.719 us, total = 4.462 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 8 total (0 active), Execution time: mean = 43.568 us, total = 348.541 us, Queueing time: mean = 191.479 us, max = 789.217 us, min = 21.477 us, total = 1.532 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 5 total (1 active), Execution time: mean = 16.868 s, total = 84.342 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 4 total (0 active), Execution time: mean = 203.569 us, total = 814.274 us, Queueing time: mean = 227.832 us, max = 546.649 us, min = 25.476 us, total = 911.329 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 3 total (0 active), Execution time: mean = 57.007 us, total = 171.021 us, Queueing time: mean = 156.361 us, max = 177.011 us, min = 134.145 us, total = 469.082 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 276.534 us, total = 553.069 us, Queueing time: mean = 41.648 us, max = 83.297 us, min = 83.297 us, total = 83.297 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 40.636 us, max = 658.748 us, min = 6.246 us, total = 14.670 ms
Execution time:  mean = 407.248 us, total = 147.017 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 273.994 us, total = 32.879 ms, Queueing time: mean = 76.645 us, max = 456.389 us, min = 6.246 us, total = 9.197 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 34.988 us, total = 4.199 ms, Queueing time: mean = 45.454 us, max = 658.748 us, min = 10.578 us, total = 5.455 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 914.525 us, total = 109.743 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.00986958 MiB
	total number of task attempts sent: 51
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:20:12,286 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 2575 total (7 active)
Queueing time: mean = 87.115 us, max = 20.312 ms, min = -0.000 s, total = 224.322 ms
Execution time:  mean = 33.463 ms, total = 86.167 s
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 13.906 us, total = 25.017 ms, Queueing time: mean = 102.055 us, max = 20.312 ms, min = -0.000 s, total = 183.597 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 236.238 us, total = 42.523 ms, Queueing time: mean = 84.316 us, max = 541.964 us, min = -0.000 s, total = 15.177 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 1.133 ms, total = 203.989 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 26.061 us, total = 4.691 ms, Queueing time: mean = 41.300 us, max = 916.005 us, min = 12.514 us, total = 7.434 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 101.242 us, total = 3.645 ms, Queueing time: mean = 87.416 us, max = 287.116 us, min = 11.584 us, total = 3.147 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 29 total (0 active), Execution time: mean = 388.400 us, total = 11.264 ms, Queueing time: mean = 60.141 us, max = 116.083 us, min = 43.863 us, total = 1.744 ms
	ActorTaskSubmitter::SubmitTask - 29 total (0 active), Execution time: mean = 255.244 us, total = 7.402 ms, Queueing time: mean = 98.441 us, max = 206.405 us, min = 30.810 us, total = 2.855 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 29 total (0 active), Execution time: mean = 87.773 us, total = 2.545 ms, Queueing time: mean = 41.550 us, max = 102.243 us, min = 18.231 us, total = 1.205 ms
	CoreWorkerService.grpc_client.PushTask - 29 total (0 active), Execution time: mean = 3.764 ms, total = 109.156 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 9.947 us, total = 179.050 us, Queueing time: mean = 58.654 us, max = 158.988 us, min = 3.844 us, total = 1.056 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 12 total (0 active), Execution time: mean = 500.721 us, total = 6.009 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 12 total (0 active), Execution time: mean = 42.064 us, total = 504.767 us, Queueing time: mean = 172.855 us, max = 789.217 us, min = 15.335 us, total = 2.074 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 5 total (1 active), Execution time: mean = 16.868 s, total = 84.342 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 4 total (0 active), Execution time: mean = 203.569 us, total = 814.274 us, Queueing time: mean = 227.832 us, max = 546.649 us, min = 25.476 us, total = 911.329 us
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 335.972 us, total = 1.008 ms, Queueing time: mean = 46.895 us, max = 83.297 us, min = 57.389 us, total = 140.686 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 3 total (0 active), Execution time: mean = 57.007 us, total = 171.021 us, Queueing time: mean = 156.361 us, max = 177.011 us, min = 134.145 us, total = 469.082 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 40.181 us, max = 658.748 us, min = 6.246 us, total = 21.738 ms
Execution time:  mean = 452.805 us, total = 244.968 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 264.036 us, total = 47.527 ms, Queueing time: mean = 80.830 us, max = 456.389 us, min = 6.246 us, total = 14.549 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 34.092 us, total = 6.137 ms, Queueing time: mean = 39.840 us, max = 658.748 us, min = 10.578 us, total = 7.171 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 1.062 ms, total = 191.109 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0123186 MiB
	total number of task attempts sent: 63
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:20:38,136 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:20:38,136 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:20:38,136 I 112 701599] core_worker.cc:5107: Number of alive nodes:2
[2025-06-26 01:20:43,257 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:20:43,257 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:20:43,259 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:20:43,259 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:21:05,529 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=1debeedd775f559e844e3f300a626c3ae54f84472517a3fe5853f7f0
[2025-06-26 01:21:08,662 I 112 701599] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:21:08,662 I 112 701599] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:21:12,287 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 3396 total (7 active)
Queueing time: mean = 84.345 us, max = 20.312 ms, min = -0.000 s, total = 286.437 ms
Execution time:  mean = 70.167 ms, total = 238.289 s
Event stats:
	CoreWorker.RecoverObjects - 2398 total (1 active), Execution time: mean = 14.011 us, total = 33.598 ms, Queueing time: mean = 96.972 us, max = 20.312 ms, min = -0.000 s, total = 232.539 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 240.505 us, total = 57.721 ms, Queueing time: mean = 85.116 us, max = 541.964 us, min = -0.000 s, total = 20.428 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 1.088 ms, total = 261.059 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 26.302 us, total = 6.312 ms, Queueing time: mean = 44.269 us, max = 916.005 us, min = 12.514 us, total = 10.625 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 84.995 us, total = 4.080 ms, Queueing time: mean = 88.355 us, max = 287.116 us, min = 11.584 us, total = 4.241 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 29 total (0 active), Execution time: mean = 388.400 us, total = 11.264 ms, Queueing time: mean = 60.141 us, max = 116.083 us, min = 43.863 us, total = 1.744 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 29 total (0 active), Execution time: mean = 87.773 us, total = 2.545 ms, Queueing time: mean = 41.550 us, max = 102.243 us, min = 18.231 us, total = 1.205 ms
	ActorTaskSubmitter::SubmitTask - 29 total (0 active), Execution time: mean = 255.244 us, total = 7.402 ms, Queueing time: mean = 98.441 us, max = 206.405 us, min = 30.810 us, total = 2.855 ms
	CoreWorkerService.grpc_client.PushTask - 29 total (0 active), Execution time: mean = 3.764 ms, total = 109.156 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 9.695 us, total = 232.679 us, Queueing time: mean = 73.584 us, max = 243.712 us, min = 3.844 us, total = 1.766 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 16 total (0 active), Execution time: mean = 497.695 us, total = 7.963 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 16 total (0 active), Execution time: mean = 39.385 us, total = 630.161 us, Queueing time: mean = 153.866 us, max = 789.217 us, min = 15.335 us, total = 2.462 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 23.638 s, total = 236.377 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 221.813 us, total = 1.996 ms, Queueing time: mean = 241.620 us, max = 546.649 us, min = 21.711 us, total = 2.175 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 438.955 us, total = 1.756 ms, Queueing time: mean = 55.490 us, max = 83.297 us, min = 57.389 us, total = 221.962 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 41.201 us, max = 658.748 us, min = 6.246 us, total = 29.706 ms
Execution time:  mean = 437.075 us, total = 315.131 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 257.917 us, total = 61.900 ms, Queueing time: mean = 84.826 us, max = 456.389 us, min = 6.246 us, total = 20.358 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 32.896 us, total = 7.895 ms, Queueing time: mean = 38.874 us, max = 658.748 us, min = 10.404 us, total = 9.330 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 1.021 ms, total = 245.140 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0123186 MiB
	total number of task attempts sent: 63
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:22:12,288 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 4203 total (7 active)
Queueing time: mean = 80.934 us, max = 20.312 ms, min = -0.000 s, total = 340.166 ms
Execution time:  mean = 56.715 ms, total = 238.373 s
Event stats:
	CoreWorker.RecoverObjects - 2997 total (1 active), Execution time: mean = 14.156 us, total = 42.425 ms, Queueing time: mean = 92.194 us, max = 20.312 ms, min = -0.000 s, total = 276.305 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 242.806 us, total = 72.842 ms, Queueing time: mean = 83.431 us, max = 541.964 us, min = -0.000 s, total = 25.029 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 1.055 ms, total = 316.448 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 26.073 us, total = 7.822 ms, Queueing time: mean = 42.394 us, max = 916.005 us, min = 8.705 us, total = 12.718 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 74.750 us, total = 4.485 ms, Queueing time: mean = 87.097 us, max = 287.116 us, min = 11.584 us, total = 5.226 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 9.572 us, total = 287.157 us, Queueing time: mean = 111.167 us, max = 1.051 ms, min = 3.844 us, total = 3.335 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 29 total (0 active), Execution time: mean = 388.400 us, total = 11.264 ms, Queueing time: mean = 60.141 us, max = 116.083 us, min = 43.863 us, total = 1.744 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 29 total (0 active), Execution time: mean = 87.773 us, total = 2.545 ms, Queueing time: mean = 41.550 us, max = 102.243 us, min = 18.231 us, total = 1.205 ms
	ActorTaskSubmitter::SubmitTask - 29 total (0 active), Execution time: mean = 255.244 us, total = 7.402 ms, Queueing time: mean = 98.441 us, max = 206.405 us, min = 30.810 us, total = 2.855 ms
	CoreWorkerService.grpc_client.PushTask - 29 total (0 active), Execution time: mean = 3.764 ms, total = 109.156 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 20 total (0 active), Execution time: mean = 499.817 us, total = 9.996 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 20 total (0 active), Execution time: mean = 38.545 us, total = 770.893 us, Queueing time: mean = 148.748 us, max = 789.217 us, min = 15.335 us, total = 2.975 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 23.638 s, total = 236.377 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 221.813 us, total = 1.996 ms, Queueing time: mean = 241.620 us, max = 546.649 us, min = 21.711 us, total = 2.175 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 483.170 us, total = 2.416 ms, Queueing time: mean = 62.069 us, max = 88.382 us, min = 57.389 us, total = 310.344 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 6.154 us, total = 12.307 us, Queueing time: mean = 56.724 us, max = 113.448 us, min = 113.448 us, total = 113.448 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 40.965 us, max = 658.748 us, min = 6.246 us, total = 36.909 ms
Execution time:  mean = 427.074 us, total = 384.794 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 255.478 us, total = 76.643 ms, Queueing time: mean = 85.394 us, max = 456.389 us, min = 6.246 us, total = 25.618 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 32.611 us, total = 9.783 ms, Queueing time: mean = 37.578 us, max = 658.748 us, min = 10.404 us, total = 11.273 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 993.905 us, total = 298.171 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0123186 MiB
	total number of task attempts sent: 63
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:23:12,289 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 5010 total (7 active)
Queueing time: mean = 79.430 us, max = 20.312 ms, min = -0.000 s, total = 397.943 ms
Execution time:  mean = 47.596 ms, total = 238.455 s
Event stats:
	CoreWorker.RecoverObjects - 3597 total (1 active), Execution time: mean = 14.254 us, total = 51.272 ms, Queueing time: mean = 90.225 us, max = 20.312 ms, min = -0.000 s, total = 324.538 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 242.292 us, total = 87.225 ms, Queueing time: mean = 83.891 us, max = 541.964 us, min = -0.000 s, total = 30.201 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 1.029 ms, total = 370.602 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 25.872 us, total = 9.314 ms, Queueing time: mean = 42.001 us, max = 916.005 us, min = 8.705 us, total = 15.120 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 68.255 us, total = 4.914 ms, Queueing time: mean = 85.349 us, max = 287.116 us, min = 11.584 us, total = 6.145 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 9.655 us, total = 347.589 us, Queueing time: mean = 103.802 us, max = 1.051 ms, min = 3.844 us, total = 3.737 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 29 total (0 active), Execution time: mean = 388.400 us, total = 11.264 ms, Queueing time: mean = 60.141 us, max = 116.083 us, min = 43.863 us, total = 1.744 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 29 total (0 active), Execution time: mean = 87.773 us, total = 2.545 ms, Queueing time: mean = 41.550 us, max = 102.243 us, min = 18.231 us, total = 1.205 ms
	ActorTaskSubmitter::SubmitTask - 29 total (0 active), Execution time: mean = 255.244 us, total = 7.402 ms, Queueing time: mean = 98.441 us, max = 206.405 us, min = 30.810 us, total = 2.855 ms
	CoreWorkerService.grpc_client.PushTask - 29 total (0 active), Execution time: mean = 3.764 ms, total = 109.156 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 24 total (0 active), Execution time: mean = 491.115 us, total = 11.787 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 24 total (0 active), Execution time: mean = 37.737 us, total = 905.688 us, Queueing time: mean = 147.094 us, max = 789.217 us, min = 15.335 us, total = 3.530 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 23.638 s, total = 236.377 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 221.813 us, total = 1.996 ms, Queueing time: mean = 241.620 us, max = 546.649 us, min = 21.711 us, total = 2.175 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 510.093 us, total = 3.061 ms, Queueing time: mean = 67.226 us, max = 93.010 us, min = 57.389 us, total = 403.354 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 6.154 us, total = 12.307 us, Queueing time: mean = 56.724 us, max = 113.448 us, min = 113.448 us, total = 113.448 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 40.961 us, max = 658.748 us, min = -0.000 s, total = 44.279 ms
Execution time:  mean = 419.507 us, total = 453.487 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 253.102 us, total = 91.117 ms, Queueing time: mean = 86.594 us, max = 456.389 us, min = -0.000 s, total = 31.174 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 33.216 us, total = 11.958 ms, Queueing time: mean = 36.355 us, max = 658.748 us, min = 10.404 us, total = 13.088 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 972.825 us, total = 350.217 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0123186 MiB
	total number of task attempts sent: 63
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:24:12,289 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 5816 total (7 active)
Queueing time: mean = 77.701 us, max = 20.312 ms, min = -0.000 s, total = 451.909 ms
Execution time:  mean = 41.014 ms, total = 238.536 s
Event stats:
	CoreWorker.RecoverObjects - 4196 total (1 active), Execution time: mean = 14.332 us, total = 60.137 ms, Queueing time: mean = 88.247 us, max = 20.312 ms, min = -0.000 s, total = 370.285 ms
	CoreWorker.InternalHeartbeat - 420 total (1 active), Execution time: mean = 241.901 us, total = 101.598 ms, Queueing time: mean = 82.559 us, max = 541.964 us, min = -0.000 s, total = 34.675 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 420 total (0 active), Execution time: mean = 1.009 ms, total = 423.848 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 420 total (0 active), Execution time: mean = 25.742 us, total = 10.812 ms, Queueing time: mean = 40.843 us, max = 916.005 us, min = 8.705 us, total = 17.154 ms
	CoreWorker.RecordMetrics - 84 total (1 active), Execution time: mean = 63.834 us, total = 5.362 ms, Queueing time: mean = 84.981 us, max = 287.116 us, min = 11.584 us, total = 7.138 ms
	CoreWorker.TryDelPendingObjectRefStreams - 42 total (1 active), Execution time: mean = 9.705 us, total = 407.623 us, Queueing time: mean = 99.876 us, max = 1.051 ms, min = 3.844 us, total = 4.195 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 29 total (0 active), Execution time: mean = 388.400 us, total = 11.264 ms, Queueing time: mean = 60.141 us, max = 116.083 us, min = 43.863 us, total = 1.744 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 29 total (0 active), Execution time: mean = 87.773 us, total = 2.545 ms, Queueing time: mean = 41.550 us, max = 102.243 us, min = 18.231 us, total = 1.205 ms
	ActorTaskSubmitter::SubmitTask - 29 total (0 active), Execution time: mean = 255.244 us, total = 7.402 ms, Queueing time: mean = 98.441 us, max = 206.405 us, min = 30.810 us, total = 2.855 ms
	CoreWorkerService.grpc_client.PushTask - 29 total (0 active), Execution time: mean = 3.764 ms, total = 109.156 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 28 total (0 active), Execution time: mean = 477.036 us, total = 13.357 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 28 total (0 active), Execution time: mean = 36.418 us, total = 1.020 ms, Queueing time: mean = 132.381 us, max = 789.217 us, min = 15.335 us, total = 3.707 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 23.638 s, total = 236.377 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 221.813 us, total = 1.996 ms, Queueing time: mean = 241.620 us, max = 546.649 us, min = 21.711 us, total = 2.175 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	CoreWorker.PrintEventStats - 7 total (1 active, 1 running), Execution time: mean = 521.223 us, total = 3.649 ms, Queueing time: mean = 69.698 us, max = 93.010 us, min = 57.389 us, total = 487.889 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 6.154 us, total = 12.307 us, Queueing time: mean = 56.724 us, max = 113.448 us, min = 113.448 us, total = 113.448 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1261 total (1 active)
Queueing time: mean = 40.295 us, max = 658.748 us, min = -0.000 s, total = 50.811 ms
Execution time:  mean = 412.201 us, total = 519.785 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 420 total (1 active), Execution time: mean = 251.009 us, total = 105.424 ms, Queueing time: mean = 85.381 us, max = 456.389 us, min = -0.000 s, total = 35.860 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 420 total (0 active), Execution time: mean = 32.746 us, total = 13.753 ms, Queueing time: mean = 35.557 us, max = 658.748 us, min = 10.404 us, total = 14.934 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 420 total (0 active), Execution time: mean = 953.363 us, total = 400.413 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0123186 MiB
	total number of task attempts sent: 63
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:25:12,290 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 6623 total (7 active)
Queueing time: mean = 78.220 us, max = 20.312 ms, min = -0.000 s, total = 518.053 ms
Execution time:  mean = 36.029 ms, total = 238.620 s
Event stats:
	CoreWorker.RecoverObjects - 4796 total (1 active), Execution time: mean = 14.346 us, total = 68.802 ms, Queueing time: mean = 88.940 us, max = 20.312 ms, min = -0.000 s, total = 426.558 ms
	CoreWorker.InternalHeartbeat - 480 total (1 active), Execution time: mean = 241.435 us, total = 115.889 ms, Queueing time: mean = 84.166 us, max = 802.072 us, min = -0.000 s, total = 40.400 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 480 total (0 active), Execution time: mean = 1.000 ms, total = 480.057 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 480 total (0 active), Execution time: mean = 25.594 us, total = 12.285 ms, Queueing time: mean = 39.974 us, max = 916.005 us, min = 8.705 us, total = 19.188 ms
	CoreWorker.RecordMetrics - 96 total (1 active), Execution time: mean = 60.353 us, total = 5.794 ms, Queueing time: mean = 83.211 us, max = 287.116 us, min = 11.584 us, total = 7.988 ms
	CoreWorker.TryDelPendingObjectRefStreams - 48 total (1 active), Execution time: mean = 9.771 us, total = 468.996 us, Queueing time: mean = 96.563 us, max = 1.051 ms, min = 3.844 us, total = 4.635 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 32 total (0 active), Execution time: mean = 478.518 us, total = 15.313 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 32 total (0 active), Execution time: mean = 36.721 us, total = 1.175 ms, Queueing time: mean = 139.308 us, max = 789.217 us, min = 15.335 us, total = 4.458 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 29 total (0 active), Execution time: mean = 388.400 us, total = 11.264 ms, Queueing time: mean = 60.141 us, max = 116.083 us, min = 43.863 us, total = 1.744 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 29 total (0 active), Execution time: mean = 87.773 us, total = 2.545 ms, Queueing time: mean = 41.550 us, max = 102.243 us, min = 18.231 us, total = 1.205 ms
	ActorTaskSubmitter::SubmitTask - 29 total (0 active), Execution time: mean = 255.244 us, total = 7.402 ms, Queueing time: mean = 98.441 us, max = 206.405 us, min = 30.810 us, total = 2.855 ms
	CoreWorkerService.grpc_client.PushTask - 29 total (0 active), Execution time: mean = 3.764 ms, total = 109.156 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 23.638 s, total = 236.377 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 221.813 us, total = 1.996 ms, Queueing time: mean = 241.620 us, max = 546.649 us, min = 21.711 us, total = 2.175 ms
	CoreWorker.PrintEventStats - 8 total (1 active, 1 running), Execution time: mean = 554.368 us, total = 4.435 ms, Queueing time: mean = 69.856 us, max = 93.010 us, min = 57.389 us, total = 558.852 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 6.154 us, total = 12.307 us, Queueing time: mean = 56.724 us, max = 113.448 us, min = 113.448 us, total = 113.448 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1441 total (1 active)
Queueing time: mean = 39.909 us, max = 658.748 us, min = -0.000 s, total = 57.508 ms
Execution time:  mean = 412.721 us, total = 594.731 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 480 total (1 active), Execution time: mean = 252.660 us, total = 121.277 ms, Queueing time: mean = 83.990 us, max = 456.389 us, min = -0.000 s, total = 40.315 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 480 total (0 active), Execution time: mean = 32.900 us, total = 15.792 ms, Queueing time: mean = 35.782 us, max = 658.748 us, min = 10.404 us, total = 17.175 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 480 total (0 active), Execution time: mean = 953.054 us, total = 457.466 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0123186 MiB
	total number of task attempts sent: 63
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:26:12,291 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 7429 total (7 active)
Queueing time: mean = 79.543 us, max = 20.312 ms, min = -0.000 s, total = 590.927 ms
Execution time:  mean = 32.133 ms, total = 238.714 s
Event stats:
	CoreWorker.RecoverObjects - 5395 total (1 active), Execution time: mean = 14.369 us, total = 77.519 ms, Queueing time: mean = 90.162 us, max = 20.312 ms, min = -0.000 s, total = 486.426 ms
	CoreWorker.InternalHeartbeat - 540 total (1 active), Execution time: mean = 244.817 us, total = 132.201 ms, Queueing time: mean = 84.874 us, max = 802.072 us, min = -0.000 s, total = 45.832 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 540 total (0 active), Execution time: mean = 1.009 ms, total = 544.861 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 540 total (0 active), Execution time: mean = 25.474 us, total = 13.756 ms, Queueing time: mean = 44.560 us, max = 1.263 ms, min = 8.705 us, total = 24.062 ms
	CoreWorker.RecordMetrics - 108 total (1 active), Execution time: mean = 60.508 us, total = 6.535 ms, Queueing time: mean = 90.955 us, max = 609.546 us, min = 11.584 us, total = 9.823 ms
	CoreWorker.TryDelPendingObjectRefStreams - 54 total (1 active), Execution time: mean = 9.838 us, total = 531.239 us, Queueing time: mean = 97.180 us, max = 1.051 ms, min = 3.844 us, total = 5.248 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 36 total (0 active), Execution time: mean = 460.579 us, total = 16.581 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 36 total (0 active), Execution time: mean = 39.369 us, total = 1.417 ms, Queueing time: mean = 128.570 us, max = 789.217 us, min = 15.335 us, total = 4.629 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 29 total (0 active), Execution time: mean = 388.400 us, total = 11.264 ms, Queueing time: mean = 60.141 us, max = 116.083 us, min = 43.863 us, total = 1.744 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 29 total (0 active), Execution time: mean = 87.773 us, total = 2.545 ms, Queueing time: mean = 41.550 us, max = 102.243 us, min = 18.231 us, total = 1.205 ms
	ActorTaskSubmitter::SubmitTask - 29 total (0 active), Execution time: mean = 255.244 us, total = 7.402 ms, Queueing time: mean = 98.441 us, max = 206.405 us, min = 30.810 us, total = 2.855 ms
	CoreWorkerService.grpc_client.PushTask - 29 total (0 active), Execution time: mean = 3.764 ms, total = 109.156 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 23.638 s, total = 236.377 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 221.813 us, total = 1.996 ms, Queueing time: mean = 241.620 us, max = 546.649 us, min = 21.711 us, total = 2.175 ms
	CoreWorker.PrintEventStats - 9 total (1 active, 1 running), Execution time: mean = 560.311 us, total = 5.043 ms, Queueing time: mean = 71.089 us, max = 93.010 us, min = 57.389 us, total = 639.803 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 6.154 us, total = 12.307 us, Queueing time: mean = 56.724 us, max = 113.448 us, min = 113.448 us, total = 113.448 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1621 total (1 active)
Queueing time: mean = 43.779 us, max = 1.879 ms, min = -0.000 s, total = 70.966 ms
Execution time:  mean = 415.890 us, total = 674.158 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 540 total (1 active), Execution time: mean = 254.674 us, total = 137.524 ms, Queueing time: mean = 93.772 us, max = 1.879 ms, min = -0.000 s, total = 50.637 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 540 total (0 active), Execution time: mean = 32.551 us, total = 17.577 ms, Queueing time: mean = 37.614 us, max = 1.053 ms, min = 10.404 us, total = 20.311 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 540 total (0 active), Execution time: mean = 960.853 us, total = 518.861 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0123186 MiB
	total number of task attempts sent: 63
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:27:12,291 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 8236 total (7 active)
Queueing time: mean = 82.653 us, max = 20.312 ms, min = -0.000 s, total = 680.726 ms
Execution time:  mean = 28.994 ms, total = 238.795 s
Event stats:
	CoreWorker.RecoverObjects - 5994 total (1 active), Execution time: mean = 14.360 us, total = 86.075 ms, Queueing time: mean = 92.473 us, max = 20.312 ms, min = -0.000 s, total = 554.286 ms
	CoreWorker.InternalHeartbeat - 600 total (1 active), Execution time: mean = 240.533 us, total = 144.320 ms, Queueing time: mean = 103.005 us, max = 3.055 ms, min = -0.000 s, total = 61.803 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 600 total (0 active), Execution time: mean = 1.002 ms, total = 601.445 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 600 total (0 active), Execution time: mean = 25.242 us, total = 15.145 ms, Queueing time: mean = 44.284 us, max = 1.263 ms, min = 8.705 us, total = 26.570 ms
	CoreWorker.RecordMetrics - 120 total (1 active), Execution time: mean = 58.043 us, total = 6.965 ms, Queueing time: mean = 99.641 us, max = 641.951 us, min = 11.584 us, total = 11.957 ms
	CoreWorker.TryDelPendingObjectRefStreams - 60 total (1 active), Execution time: mean = 10.048 us, total = 602.854 us, Queueing time: mean = 100.524 us, max = 1.051 ms, min = 3.844 us, total = 6.031 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 40 total (0 active), Execution time: mean = 456.564 us, total = 18.263 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 40 total (0 active), Execution time: mean = 38.572 us, total = 1.543 ms, Queueing time: mean = 125.602 us, max = 789.217 us, min = 15.335 us, total = 5.024 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 29 total (0 active), Execution time: mean = 388.400 us, total = 11.264 ms, Queueing time: mean = 60.141 us, max = 116.083 us, min = 43.863 us, total = 1.744 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 29 total (0 active), Execution time: mean = 87.773 us, total = 2.545 ms, Queueing time: mean = 41.550 us, max = 102.243 us, min = 18.231 us, total = 1.205 ms
	ActorTaskSubmitter::SubmitTask - 29 total (0 active), Execution time: mean = 255.244 us, total = 7.402 ms, Queueing time: mean = 98.441 us, max = 206.405 us, min = 30.810 us, total = 2.855 ms
	CoreWorkerService.grpc_client.PushTask - 29 total (0 active), Execution time: mean = 3.764 ms, total = 109.156 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 10 total (1 active, 1 running), Execution time: mean = 563.431 us, total = 5.634 ms, Queueing time: mean = 71.047 us, max = 93.010 us, min = 57.389 us, total = 710.473 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 23.638 s, total = 236.377 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 221.813 us, total = 1.996 ms, Queueing time: mean = 241.620 us, max = 546.649 us, min = 21.711 us, total = 2.175 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 8.481 us, total = 25.442 us, Queueing time: mean = 63.158 us, max = 113.448 us, min = 76.026 us, total = 189.474 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1801 total (1 active)
Queueing time: mean = 44.797 us, max = 1.879 ms, min = -0.000 s, total = 80.680 ms
Execution time:  mean = 419.329 us, total = 755.212 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 600 total (1 active), Execution time: mean = 258.630 us, total = 155.178 ms, Queueing time: mean = 95.031 us, max = 1.879 ms, min = -0.000 s, total = 57.018 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 600 total (0 active), Execution time: mean = 32.734 us, total = 19.640 ms, Queueing time: mean = 39.406 us, max = 1.053 ms, min = 10.404 us, total = 23.643 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 600 total (0 active), Execution time: mean = 966.997 us, total = 580.198 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0123186 MiB
	total number of task attempts sent: 63
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:28:12,292 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 9069 total (7 active)
Queueing time: mean = 82.437 us, max = 20.312 ms, min = -0.000 s, total = 747.617 ms
Execution time:  mean = 67.378 ms, total = 611.050 s
Event stats:
	CoreWorker.RecoverObjects - 6594 total (1 active), Execution time: mean = 14.232 us, total = 93.849 ms, Queueing time: mean = 92.005 us, max = 20.312 ms, min = -0.000 s, total = 606.680 ms
	CoreWorker.InternalHeartbeat - 660 total (1 active), Execution time: mean = 239.134 us, total = 157.829 ms, Queueing time: mean = 104.857 us, max = 3.055 ms, min = -0.000 s, total = 69.206 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 660 total (0 active), Execution time: mean = 993.094 us, total = 655.442 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 660 total (0 active), Execution time: mean = 24.978 us, total = 16.485 ms, Queueing time: mean = 43.199 us, max = 1.263 ms, min = 8.705 us, total = 28.512 ms
	CoreWorker.RecordMetrics - 132 total (1 active), Execution time: mean = 58.340 us, total = 7.701 ms, Queueing time: mean = 99.313 us, max = 641.951 us, min = 11.584 us, total = 13.109 ms
	CoreWorker.TryDelPendingObjectRefStreams - 66 total (1 active), Execution time: mean = 9.985 us, total = 659.024 us, Queueing time: mean = 100.638 us, max = 1.051 ms, min = 3.844 us, total = 6.642 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 44 total (0 active), Execution time: mean = 448.036 us, total = 19.714 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 44 total (0 active), Execution time: mean = 40.545 us, total = 1.784 ms, Queueing time: mean = 118.534 us, max = 789.217 us, min = 15.335 us, total = 5.215 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 35 total (0 active), Execution time: mean = 378.474 us, total = 13.247 ms, Queueing time: mean = 64.725 us, max = 164.381 us, min = 43.863 us, total = 2.265 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 35 total (0 active), Execution time: mean = 94.086 us, total = 3.293 ms, Queueing time: mean = 47.275 us, max = 184.734 us, min = 18.062 us, total = 1.655 ms
	ActorTaskSubmitter::SubmitTask - 35 total (0 active), Execution time: mean = 253.940 us, total = 8.888 ms, Queueing time: mean = 111.161 us, max = 242.256 us, min = 30.810 us, total = 3.891 ms
	CoreWorkerService.grpc_client.PushTask - 35 total (0 active), Execution time: mean = 3.433 ms, total = 120.164 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 11 total (1 active, 1 running), Execution time: mean = 563.743 us, total = 6.201 ms, Queueing time: mean = 74.474 us, max = 108.744 us, min = 57.389 us, total = 819.217 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 11 total (1 active), Execution time: mean = 55.321 s, total = 608.536 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 10 total (0 active), Execution time: mean = 223.714 us, total = 2.237 ms, Queueing time: mean = 325.833 us, max = 1.084 ms, min = 21.711 us, total = 3.258 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 8.481 us, total = 25.442 us, Queueing time: mean = 63.158 us, max = 113.448 us, min = 76.026 us, total = 189.474 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1981 total (1 active)
Queueing time: mean = 44.418 us, max = 1.879 ms, min = -0.000 s, total = 87.992 ms
Execution time:  mean = 417.675 us, total = 827.415 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 660 total (1 active), Execution time: mean = 259.565 us, total = 171.313 ms, Queueing time: mean = 94.366 us, max = 1.879 ms, min = -0.000 s, total = 62.281 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 660 total (0 active), Execution time: mean = 32.849 us, total = 21.680 ms, Queueing time: mean = 38.929 us, max = 1.053 ms, min = 10.404 us, total = 25.693 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 660 total (0 active), Execution time: mean = 960.948 us, total = 634.226 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0147676 MiB
	total number of task attempts sent: 75
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:29:12,293 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 9875 total (7 active)
Queueing time: mean = 81.193 us, max = 20.312 ms, min = -0.000 s, total = 801.779 ms
Execution time:  mean = 61.887 ms, total = 611.136 s
Event stats:
	CoreWorker.RecoverObjects - 7193 total (1 active), Execution time: mean = 14.204 us, total = 102.172 ms, Queueing time: mean = 90.572 us, max = 20.312 ms, min = -0.000 s, total = 651.482 ms
	CoreWorker.InternalHeartbeat - 720 total (1 active), Execution time: mean = 240.561 us, total = 173.204 ms, Queueing time: mean = 102.764 us, max = 3.055 ms, min = -0.000 s, total = 73.990 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 720 total (0 active), Execution time: mean = 989.854 us, total = 712.695 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 720 total (0 active), Execution time: mean = 24.883 us, total = 17.916 ms, Queueing time: mean = 42.498 us, max = 1.263 ms, min = 8.705 us, total = 30.598 ms
	CoreWorker.RecordMetrics - 144 total (1 active), Execution time: mean = 56.617 us, total = 8.153 ms, Queueing time: mean = 97.331 us, max = 641.951 us, min = 11.584 us, total = 14.016 ms
	CoreWorker.TryDelPendingObjectRefStreams - 72 total (1 active), Execution time: mean = 9.848 us, total = 709.027 us, Queueing time: mean = 99.353 us, max = 1.051 ms, min = 3.844 us, total = 7.153 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 48 total (0 active), Execution time: mean = 447.457 us, total = 21.478 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 48 total (0 active), Execution time: mean = 40.748 us, total = 1.956 ms, Queueing time: mean = 128.128 us, max = 789.217 us, min = 15.335 us, total = 6.150 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 35 total (0 active), Execution time: mean = 378.474 us, total = 13.247 ms, Queueing time: mean = 64.725 us, max = 164.381 us, min = 43.863 us, total = 2.265 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 35 total (0 active), Execution time: mean = 94.086 us, total = 3.293 ms, Queueing time: mean = 47.275 us, max = 184.734 us, min = 18.062 us, total = 1.655 ms
	ActorTaskSubmitter::SubmitTask - 35 total (0 active), Execution time: mean = 253.940 us, total = 8.888 ms, Queueing time: mean = 111.161 us, max = 242.256 us, min = 30.810 us, total = 3.891 ms
	CoreWorkerService.grpc_client.PushTask - 35 total (0 active), Execution time: mean = 3.433 ms, total = 120.164 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 12 total (1 active, 1 running), Execution time: mean = 564.718 us, total = 6.777 ms, Queueing time: mean = 79.618 us, max = 136.196 us, min = 57.389 us, total = 955.413 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 11 total (1 active), Execution time: mean = 55.321 s, total = 608.536 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 10 total (0 active), Execution time: mean = 223.714 us, total = 2.237 ms, Queueing time: mean = 325.833 us, max = 1.084 ms, min = 21.711 us, total = 3.258 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 8.481 us, total = 25.442 us, Queueing time: mean = 63.158 us, max = 113.448 us, min = 76.026 us, total = 189.474 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2161 total (1 active)
Queueing time: mean = 44.889 us, max = 1.879 ms, min = -0.000 s, total = 97.005 ms
Execution time:  mean = 413.542 us, total = 893.664 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 720 total (1 active), Execution time: mean = 257.536 us, total = 185.426 ms, Queueing time: mean = 95.272 us, max = 1.879 ms, min = -0.000 s, total = 68.596 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 720 total (0 active), Execution time: mean = 32.714 us, total = 23.554 ms, Queueing time: mean = 39.432 us, max = 1.053 ms, min = 10.404 us, total = 28.391 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 720 total (0 active), Execution time: mean = 950.678 us, total = 684.488 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0147676 MiB
	total number of task attempts sent: 75
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:30:12,294 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 10682 total (7 active)
Queueing time: mean = 83.203 us, max = 20.312 ms, min = -0.000 s, total = 888.771 ms
Execution time:  mean = 57.220 ms, total = 611.224 s
Event stats:
	CoreWorker.RecoverObjects - 7793 total (1 active), Execution time: mean = 14.265 us, total = 111.169 ms, Queueing time: mean = 92.868 us, max = 20.312 ms, min = -0.000 s, total = 723.723 ms
	CoreWorker.InternalHeartbeat - 780 total (1 active), Execution time: mean = 240.791 us, total = 187.817 ms, Queueing time: mean = 107.599 us, max = 3.536 ms, min = -0.000 s, total = 83.927 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 780 total (0 active), Execution time: mean = 990.497 us, total = 772.587 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 780 total (0 active), Execution time: mean = 24.892 us, total = 19.416 ms, Queueing time: mean = 41.775 us, max = 1.263 ms, min = 8.705 us, total = 32.585 ms
	CoreWorker.RecordMetrics - 156 total (1 active), Execution time: mean = 55.895 us, total = 8.720 ms, Queueing time: mean = 97.044 us, max = 641.951 us, min = 11.584 us, total = 15.139 ms
	CoreWorker.TryDelPendingObjectRefStreams - 78 total (1 active), Execution time: mean = 9.841 us, total = 767.583 us, Queueing time: mean = 99.773 us, max = 1.051 ms, min = 3.844 us, total = 7.782 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 52 total (0 active), Execution time: mean = 445.506 us, total = 23.166 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 52 total (0 active), Execution time: mean = 39.914 us, total = 2.076 ms, Queueing time: mean = 136.405 us, max = 789.217 us, min = 15.335 us, total = 7.093 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 35 total (0 active), Execution time: mean = 378.474 us, total = 13.247 ms, Queueing time: mean = 64.725 us, max = 164.381 us, min = 43.863 us, total = 2.265 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 35 total (0 active), Execution time: mean = 94.086 us, total = 3.293 ms, Queueing time: mean = 47.275 us, max = 184.734 us, min = 18.062 us, total = 1.655 ms
	ActorTaskSubmitter::SubmitTask - 35 total (0 active), Execution time: mean = 253.940 us, total = 8.888 ms, Queueing time: mean = 111.161 us, max = 242.256 us, min = 30.810 us, total = 3.891 ms
	CoreWorkerService.grpc_client.PushTask - 35 total (0 active), Execution time: mean = 3.433 ms, total = 120.164 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 13 total (1 active, 1 running), Execution time: mean = 575.696 us, total = 7.484 ms, Queueing time: mean = 83.707 us, max = 136.196 us, min = 57.389 us, total = 1.088 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 11 total (1 active), Execution time: mean = 55.321 s, total = 608.536 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 10 total (0 active), Execution time: mean = 223.714 us, total = 2.237 ms, Queueing time: mean = 325.833 us, max = 1.084 ms, min = 21.711 us, total = 3.258 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 8.481 us, total = 25.442 us, Queueing time: mean = 63.158 us, max = 113.448 us, min = 76.026 us, total = 189.474 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2341 total (1 active)
Queueing time: mean = 44.614 us, max = 1.879 ms, min = -0.000 s, total = 104.442 ms
Execution time:  mean = 419.487 us, total = 982.019 ms
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 780 total (1 active), Execution time: mean = 268.520 us, total = 209.445 ms, Queueing time: mean = 94.954 us, max = 1.879 ms, min = -0.000 s, total = 74.064 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 780 total (0 active), Execution time: mean = 32.791 us, total = 25.577 ms, Queueing time: mean = 38.923 us, max = 1.053 ms, min = 10.404 us, total = 30.360 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 780 total (0 active), Execution time: mean = 957.436 us, total = 746.800 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0147676 MiB
	total number of task attempts sent: 75
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:31:12,294 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 11488 total (7 active)
Queueing time: mean = 83.146 us, max = 20.312 ms, min = -0.000 s, total = 955.183 ms
Execution time:  mean = 53.213 ms, total = 611.312 s
Event stats:
	CoreWorker.RecoverObjects - 8392 total (1 active), Execution time: mean = 14.291 us, total = 119.933 ms, Queueing time: mean = 93.004 us, max = 20.312 ms, min = -0.000 s, total = 780.486 ms
	CoreWorker.InternalHeartbeat - 840 total (1 active), Execution time: mean = 243.005 us, total = 204.124 ms, Queueing time: mean = 105.690 us, max = 3.536 ms, min = -0.000 s, total = 88.780 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 840 total (0 active), Execution time: mean = 989.203 us, total = 830.931 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 840 total (0 active), Execution time: mean = 25.308 us, total = 21.259 ms, Queueing time: mean = 41.316 us, max = 1.263 ms, min = 8.705 us, total = 34.706 ms
	CoreWorker.RecordMetrics - 168 total (1 active), Execution time: mean = 54.441 us, total = 9.146 ms, Queueing time: mean = 95.036 us, max = 641.951 us, min = 11.584 us, total = 15.966 ms
	CoreWorker.TryDelPendingObjectRefStreams - 84 total (1 active), Execution time: mean = 9.923 us, total = 833.502 us, Queueing time: mean = 101.405 us, max = 1.051 ms, min = 3.844 us, total = 8.518 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 56 total (0 active), Execution time: mean = 451.850 us, total = 25.304 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 56 total (0 active), Execution time: mean = 38.877 us, total = 2.177 ms, Queueing time: mean = 145.829 us, max = 876.703 us, min = 15.335 us, total = 8.166 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 35 total (0 active), Execution time: mean = 378.474 us, total = 13.247 ms, Queueing time: mean = 64.725 us, max = 164.381 us, min = 43.863 us, total = 2.265 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 35 total (0 active), Execution time: mean = 94.086 us, total = 3.293 ms, Queueing time: mean = 47.275 us, max = 184.734 us, min = 18.062 us, total = 1.655 ms
	ActorTaskSubmitter::SubmitTask - 35 total (0 active), Execution time: mean = 253.940 us, total = 8.888 ms, Queueing time: mean = 111.161 us, max = 242.256 us, min = 30.810 us, total = 3.891 ms
	CoreWorkerService.grpc_client.PushTask - 35 total (0 active), Execution time: mean = 3.433 ms, total = 120.164 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 14 total (1 active, 1 running), Execution time: mean = 571.012 us, total = 7.994 ms, Queueing time: mean = 80.508 us, max = 136.196 us, min = 38.928 us, total = 1.127 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 11 total (1 active), Execution time: mean = 55.321 s, total = 608.536 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 10 total (0 active), Execution time: mean = 223.714 us, total = 2.237 ms, Queueing time: mean = 325.833 us, max = 1.084 ms, min = 21.711 us, total = 3.258 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 8.481 us, total = 25.442 us, Queueing time: mean = 63.158 us, max = 113.448 us, min = 76.026 us, total = 189.474 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2521 total (1 active)
Queueing time: mean = 43.909 us, max = 1.879 ms, min = -0.000 s, total = 110.693 ms
Execution time:  mean = 419.991 us, total = 1.059 s
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 840 total (1 active), Execution time: mean = 270.435 us, total = 227.165 ms, Queueing time: mean = 93.176 us, max = 1.879 ms, min = -0.000 s, total = 78.267 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 840 total (0 active), Execution time: mean = 33.416 us, total = 28.069 ms, Queueing time: mean = 38.581 us, max = 1.053 ms, min = 10.404 us, total = 32.408 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 840 total (0 active), Execution time: mean = 956.390 us, total = 803.368 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0147676 MiB
	total number of task attempts sent: 75
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:32:12,295 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 12296 total (7 active)
Queueing time: mean = 82.068 us, max = 20.312 ms, min = -0.000 s, total = 1.009 s
Execution time:  mean = 49.723 ms, total = 611.397 s
Event stats:
	CoreWorker.RecoverObjects - 8992 total (1 active), Execution time: mean = 14.213 us, total = 127.802 ms, Queueing time: mean = 91.766 us, max = 20.312 ms, min = -0.000 s, total = 825.164 ms
	CoreWorker.InternalHeartbeat - 900 total (1 active), Execution time: mean = 243.878 us, total = 219.491 ms, Queueing time: mean = 104.102 us, max = 3.536 ms, min = -0.000 s, total = 93.691 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 900 total (0 active), Execution time: mean = 986.436 us, total = 887.792 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 900 total (0 active), Execution time: mean = 25.565 us, total = 23.009 ms, Queueing time: mean = 41.155 us, max = 1.263 ms, min = 8.705 us, total = 37.039 ms
	CoreWorker.RecordMetrics - 180 total (1 active), Execution time: mean = 54.221 us, total = 9.760 ms, Queueing time: mean = 94.474 us, max = 641.951 us, min = 11.584 us, total = 17.005 ms
	CoreWorker.TryDelPendingObjectRefStreams - 90 total (1 active), Execution time: mean = 9.872 us, total = 888.498 us, Queueing time: mean = 99.036 us, max = 1.051 ms, min = 3.844 us, total = 8.913 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 60 total (0 active), Execution time: mean = 450.584 us, total = 27.035 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 60 total (0 active), Execution time: mean = 38.548 us, total = 2.313 ms, Queueing time: mean = 142.956 us, max = 876.703 us, min = 15.335 us, total = 8.577 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 35 total (0 active), Execution time: mean = 378.474 us, total = 13.247 ms, Queueing time: mean = 64.725 us, max = 164.381 us, min = 43.863 us, total = 2.265 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 35 total (0 active), Execution time: mean = 94.086 us, total = 3.293 ms, Queueing time: mean = 47.275 us, max = 184.734 us, min = 18.062 us, total = 1.655 ms
	ActorTaskSubmitter::SubmitTask - 35 total (0 active), Execution time: mean = 253.940 us, total = 8.888 ms, Queueing time: mean = 111.161 us, max = 242.256 us, min = 30.810 us, total = 3.891 ms
	CoreWorkerService.grpc_client.PushTask - 35 total (0 active), Execution time: mean = 3.433 ms, total = 120.164 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 15 total (1 active, 1 running), Execution time: mean = 563.754 us, total = 8.456 ms, Queueing time: mean = 79.494 us, max = 136.196 us, min = 38.928 us, total = 1.192 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 11 total (1 active), Execution time: mean = 55.321 s, total = 608.536 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 10 total (0 active), Execution time: mean = 223.714 us, total = 2.237 ms, Queueing time: mean = 325.833 us, max = 1.084 ms, min = 21.711 us, total = 3.258 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 9.515 us, total = 38.059 us, Queueing time: mean = 70.268 us, max = 113.448 us, min = 76.026 us, total = 281.072 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2701 total (1 active)
Queueing time: mean = 44.103 us, max = 2.152 ms, min = -0.000 s, total = 119.122 ms
Execution time:  mean = 420.136 us, total = 1.135 s
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 900 total (1 active), Execution time: mean = 272.090 us, total = 244.881 ms, Queueing time: mean = 94.113 us, max = 2.152 ms, min = -0.000 s, total = 84.702 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 900 total (0 active), Execution time: mean = 33.714 us, total = 30.343 ms, Queueing time: mean = 38.225 us, max = 1.053 ms, min = 10.404 us, total = 34.402 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 900 total (0 active), Execution time: mean = 954.854 us, total = 859.368 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0147676 MiB
	total number of task attempts sent: 75
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:33:12,295 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 13127 total (7 active)
Queueing time: mean = 91.331 us, max = 131.856 ms, min = -0.000 s, total = 1.199 s
Execution time:  mean = 46.605 ms, total = 611.785 s
Event stats:
	CoreWorker.RecoverObjects - 9590 total (1 active), Execution time: mean = 14.189 us, total = 136.071 ms, Queueing time: mean = 104.762 us, max = 131.856 ms, min = -0.000 s, total = 1.005 s
	CoreWorker.InternalHeartbeat - 960 total (1 active), Execution time: mean = 246.034 us, total = 236.193 ms, Queueing time: mean = 102.740 us, max = 3.536 ms, min = -0.000 s, total = 98.630 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 960 total (0 active), Execution time: mean = 985.185 us, total = 945.777 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 960 total (0 active), Execution time: mean = 25.799 us, total = 24.767 ms, Queueing time: mean = 40.900 us, max = 1.263 ms, min = 8.705 us, total = 39.264 ms
	CoreWorker.RecordMetrics - 192 total (1 active), Execution time: mean = 54.076 us, total = 10.383 ms, Queueing time: mean = 93.849 us, max = 641.951 us, min = 11.584 us, total = 18.019 ms
	CoreWorker.TryDelPendingObjectRefStreams - 96 total (1 active), Execution time: mean = 9.848 us, total = 945.419 us, Queueing time: mean = 98.188 us, max = 1.051 ms, min = 3.844 us, total = 9.426 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 64 total (0 active), Execution time: mean = 444.270 us, total = 28.433 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 64 total (0 active), Execution time: mean = 38.573 us, total = 2.469 ms, Queueing time: mean = 138.547 us, max = 876.703 us, min = 15.335 us, total = 8.867 ms
	CoreWorkerService.grpc_client.PushTask - 41 total (0 active), Execution time: mean = 3.236 ms, total = 132.696 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 41 total (0 active), Execution time: mean = 95.924 us, total = 3.933 ms, Queueing time: mean = 45.666 us, max = 184.734 us, min = 16.294 us, total = 1.872 ms
	ActorTaskSubmitter::SubmitTask - 41 total (0 active), Execution time: mean = 254.636 us, total = 10.440 ms, Queueing time: mean = 108.133 us, max = 242.256 us, min = 30.810 us, total = 4.433 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 41 total (0 active), Execution time: mean = 380.498 us, total = 15.600 ms, Queueing time: mean = 64.612 us, max = 164.381 us, min = 43.863 us, total = 2.649 ms
	CoreWorker.PrintEventStats - 16 total (1 active, 1 running), Execution time: mean = 559.824 us, total = 8.957 ms, Queueing time: mean = 76.581 us, max = 136.196 us, min = 32.894 us, total = 1.225 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 11 total (1 active), Execution time: mean = 55.321 s, total = 608.536 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 10 total (0 active), Execution time: mean = 223.714 us, total = 2.237 ms, Queueing time: mean = 325.833 us, max = 1.084 ms, min = 21.711 us, total = 3.258 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 9.515 us, total = 38.059 us, Queueing time: mean = 70.268 us, max = 113.448 us, min = 76.026 us, total = 281.072 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	CoreWorkerService.grpc_server.LocalGC.HandleRequestImpl - 1 total (0 active), Execution time: mean = 141.356 ms, total = 141.356 ms, Queueing time: mean = 137.497 us, max = 137.497 us, min = 137.497 us, total = 137.497 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	CoreWorkerService.grpc_server.LocalGC - 1 total (0 active), Execution time: mean = 141.861 ms, total = 141.861 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2881 total (1 active)
Queueing time: mean = 43.757 us, max = 2.152 ms, min = -0.000 s, total = 126.064 ms
Execution time:  mean = 421.442 us, total = 1.214 s
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 960 total (1 active), Execution time: mean = 274.614 us, total = 263.630 ms, Queueing time: mean = 93.592 us, max = 2.152 ms, min = -0.000 s, total = 89.848 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 960 total (0 active), Execution time: mean = 34.324 us, total = 32.951 ms, Queueing time: mean = 37.706 us, max = 1.053 ms, min = 10.404 us, total = 36.198 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 960 total (0 active), Execution time: mean = 955.623 us, total = 917.399 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0172167 MiB
	total number of task attempts sent: 87
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:34:12,296 I 112 701599] core_worker.cc:902: Event stats:


Global stats: 13937 total (7 active)
Queueing time: mean = 90.344 us, max = 131.856 ms, min = -0.000 s, total = 1.259 s
Execution time:  mean = 43.903 ms, total = 611.876 s
Event stats:
	CoreWorker.RecoverObjects - 10189 total (1 active), Execution time: mean = 14.248 us, total = 145.177 ms, Queueing time: mean = 103.492 us, max = 131.856 ms, min = -0.000 s, total = 1.054 s
	CoreWorker.InternalHeartbeat - 1020 total (1 active), Execution time: mean = 247.286 us, total = 252.232 ms, Queueing time: mean = 102.373 us, max = 3.536 ms, min = -0.000 s, total = 104.420 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1020 total (0 active), Execution time: mean = 984.150 us, total = 1.004 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1020 total (0 active), Execution time: mean = 25.995 us, total = 26.515 ms, Queueing time: mean = 40.475 us, max = 1.263 ms, min = 8.705 us, total = 41.285 ms
	CoreWorker.RecordMetrics - 204 total (1 active), Execution time: mean = 53.445 us, total = 10.903 ms, Queueing time: mean = 93.932 us, max = 641.951 us, min = 11.584 us, total = 19.162 ms
	CoreWorker.TryDelPendingObjectRefStreams - 102 total (1 active), Execution time: mean = 9.821 us, total = 1.002 ms, Queueing time: mean = 99.025 us, max = 1.051 ms, min = 3.844 us, total = 10.101 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 68 total (0 active), Execution time: mean = 443.737 us, total = 30.174 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 68 total (0 active), Execution time: mean = 38.385 us, total = 2.610 ms, Queueing time: mean = 136.485 us, max = 876.703 us, min = 15.335 us, total = 9.281 ms
	CoreWorkerService.grpc_client.PushTask - 42 total (0 active), Execution time: mean = 3.201 ms, total = 134.447 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 42 total (0 active), Execution time: mean = 97.439 us, total = 4.092 ms, Queueing time: mean = 46.522 us, max = 184.734 us, min = 16.294 us, total = 1.954 ms
	ActorTaskSubmitter::SubmitTask - 42 total (0 active), Execution time: mean = 255.566 us, total = 10.734 ms, Queueing time: mean = 106.919 us, max = 242.256 us, min = 30.810 us, total = 4.491 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 42 total (0 active), Execution time: mean = 381.170 us, total = 16.009 ms, Queueing time: mean = 66.028 us, max = 164.381 us, min = 43.863 us, total = 2.773 ms
	CoreWorker.PrintEventStats - 17 total (1 active, 1 running), Execution time: mean = 568.565 us, total = 9.666 ms, Queueing time: mean = 77.911 us, max = 136.196 us, min = 32.894 us, total = 1.324 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 11 total (1 active), Execution time: mean = 55.321 s, total = 608.536 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 10 total (0 active), Execution time: mean = 223.714 us, total = 2.237 ms, Queueing time: mean = 325.833 us, max = 1.084 ms, min = 21.711 us, total = 3.258 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 75.945 us, total = 607.563 us, Queueing time: mean = 208.003 us, max = 432.060 us, min = 134.145 us, total = 1.664 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 170.048 us, total = 1.020 ms, Queueing time: mean = 494.230 us, max = 988.627 us, min = 30.531 us, total = 2.965 ms
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 9.515 us, total = 38.059 us, Queueing time: mean = 70.268 us, max = 113.448 us, min = 76.026 us, total = 281.072 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 2 total (0 active), Execution time: mean = 381.834 us, total = 763.668 us, Queueing time: mean = 42.760 us, max = 68.925 us, min = 16.595 us, total = 85.520 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 2 total (0 active), Execution time: mean = 33.042 us, total = 66.085 us, Queueing time: mean = 24.668 us, max = 25.422 us, min = 23.914 us, total = 49.336 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 2 total (0 active), Execution time: mean = 665.601 us, total = 1.331 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 2 total (0 active), Execution time: mean = 944.116 us, total = 1.888 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 451.941 us, total = 451.941 us, Queueing time: mean = 781.857 us, max = 781.857 us, min = 781.857 us, total = 781.857 us
	Subscriber.HandlePublishedMessage_GCS_ACTOR_CHANNEL - 1 total (0 active), Execution time: mean = 504.293 us, total = 504.293 us, Queueing time: mean = 203.565 us, max = 203.565 us, min = 203.565 us, total = 203.565 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 681.289 us, total = 681.289 us, Queueing time: mean = 13.424 us, max = 13.424 us, min = 13.424 us, total = 13.424 us
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor - 1 total (0 active), Execution time: mean = 1.423 ms, total = 1.423 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 51.508 us, total = 51.508 us, Queueing time: mean = 306.797 us, max = 306.797 us, min = 306.797 us, total = 306.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetActorInfo - 1 total (0 active), Execution time: mean = 560.371 us, total = 560.371 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.RegisterActor.OnReplyReceived - 1 total (0 active), Execution time: mean = 65.263 us, total = 65.263 us, Queueing time: mean = 26.405 us, max = 26.405 us, min = 26.405 us, total = 26.405 us
	CoreWorkerService.grpc_server.LocalGC.HandleRequestImpl - 1 total (0 active), Execution time: mean = 141.356 ms, total = 141.356 ms, Queueing time: mean = 137.497 us, max = 137.497 us, min = 137.497 us, total = 137.497 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 1.202 ms, total = 1.202 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 47.372 us, total = 47.372 us, Queueing time: mean = 52.957 us, max = 52.957 us, min = 52.957 us, total = 52.957 us
	CoreWorkerService.grpc_server.LocalGC - 1 total (0 active), Execution time: mean = 141.861 ms, total = 141.861 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 955.174 us, total = 955.174 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.CreateActor - 1 total (0 active), Execution time: mean = 1.395 s, total = 1.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 1 total (0 active), Execution time: mean = 217.522 us, total = 217.522 us, Queueing time: mean = 26.371 us, max = 26.371 us, min = 26.371 us, total = 26.371 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3061 total (1 active)
Queueing time: mean = 43.392 us, max = 2.152 ms, min = -0.000 s, total = 132.822 ms
Execution time:  mean = 423.909 us, total = 1.298 s
Event stats:
	CoreWorker.deadline_timer.flush_task_events - 1020 total (1 active), Execution time: mean = 277.113 us, total = 282.655 ms, Queueing time: mean = 92.631 us, max = 2.152 ms, min = -0.000 s, total = 94.484 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1020 total (0 active), Execution time: mean = 34.652 us, total = 35.345 ms, Queueing time: mean = 37.569 us, max = 1.053 ms, min = 10.404 us, total = 38.320 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1020 total (0 active), Execution time: mean = 960.184 us, total = 979.388 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 195.925 us, total = 195.925 us, Queueing time: mean = 17.665 us, max = 17.665 us, min = 17.665 us, total = 17.665 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0176249 MiB
	total number of task attempts sent: 89
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


