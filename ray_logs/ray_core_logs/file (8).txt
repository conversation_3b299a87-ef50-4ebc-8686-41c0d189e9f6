[2025-06-26 01:17:13,974 I 701612 701612] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 701612
[2025-06-26 01:17:13,977 I 701612 701612] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-06-26 01:17:13,981 I 701612 701612] grpc_server.cc:141: worker server started, listening on port 10006.
[2025-06-26 01:17:13,983 I 701612 701612] core_worker.cc:542: Initializing worker at address: ************:10006 worker_id=b52478d950e8d9c1f73e090a2abd4f2a5feafb005feb31be89ace1a9 node_id=11d1fceddfd4a7d70539f452d9f1c4f91705b1be5f541b4b015ed1f5
[2025-06-26 01:17:13,985 I 701612 701612] task_event_buffer.cc:287: Reporting task events to <PERSON><PERSON> every 1000ms.
[2025-06-26 01:17:13,987 I 701612 701612] core_worker.cc:967: Adjusted worker niceness to 15
[2025-06-26 01:17:13,987 I 701612 701612] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-06-26 01:17:13,987 I 701612 701612] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-06-26 01:17:13,987 I 701612 701612] event.cc:331: Set ray event level to warning
[2025-06-26 01:17:13,988 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 15 total (9 active)
Queueing time: mean = 40.003 us, max = 488.877 us, min = 13.302 us, total = 600.042 us
Execution time:  mean = 150.808 us, total = 2.262 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 7 total (5 active, 1 running), Execution time: mean = 5.988 us, total = 41.916 us, Queueing time: mean = 77.231 us, max = 488.877 us, min = 51.739 us, total = 540.616 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.ExitIfParentRayletDies - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 37.913 us, max = 129.300 us, min = 22.351 us, total = 151.651 us
Execution time:  mean = 245.616 us, total = 982.465 us
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 818.105 us, total = 818.105 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 18.752 us, total = 18.752 us, Queueing time: mean = 129.300 us, max = 129.300 us, min = 129.300 us, total = 129.300 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=11d1fceddfd4a7d70539f452d9f1c4f91705b1be5f541b4b015ed1f5
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:5107: Number of alive nodes:1
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=374025dd58ceacb2d89386dc3a38408dc32b1456276f0fcd84952fd1
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=374025dd58ceacb2d89386dc3a38408dc32b1456276f0fcd84952fd1
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=107d8554c9ce898888fbcca7095612438976ed120c786ea7a3b82940
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=107d8554c9ce898888fbcca7095612438976ed120c786ea7a3b82940
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=6985e19ea27e856c07dc8ab917ef47ca7ac856fdd8daa6e3682941a9
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=6985e19ea27e856c07dc8ab917ef47ca7ac856fdd8daa6e3682941a9
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=e90de5051af7eab045a5db8c90b1509053a14387b123a1d3416ab7b2
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=e90de5051af7eab045a5db8c90b1509053a14387b123a1d3416ab7b2
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=aeb88ee76014cf968e47e7c5541354741a624a0d7183bbd80e7cc5f8
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=aeb88ee76014cf968e47e7c5541354741a624a0d7183bbd80e7cc5f8
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=8c4d80205327b4f323d879c99025b7243851b812f3c5de83998be538
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=8c4d80205327b4f323d879c99025b7243851b812f3c5de83998be538
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=6d647dce398845972a47ce100b2b655a442fcea0c1e67051f484861f
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=6d647dce398845972a47ce100b2b655a442fcea0c1e67051f484861f
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=c60c555fc603cb22a64b905e730341251113eb722cf57f73c495e3fb
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=c60c555fc603cb22a64b905e730341251113eb722cf57f73c495e3fb
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=77967f0e6d34a5fa26747c3c66d5a8d7b80570e71ff0bfb0320af5c8
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=77967f0e6d34a5fa26747c3c66d5a8d7b80570e71ff0bfb0320af5c8
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=71f196cf3e35c23d0086046ebee4d8b57afb729e123bf51e32afab43
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=71f196cf3e35c23d0086046ebee4d8b57afb729e123bf51e32afab43
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=45dcdd8fc5701ea50b0cef58c35281c586bc903f5a425e0f3c9ed66e
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=45dcdd8fc5701ea50b0cef58c35281c586bc903f5a425e0f3c9ed66e
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=22dcf4c94eb49e5029d861bbd4193e4e05721c90987ce6702bef89a1
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=22dcf4c94eb49e5029d861bbd4193e4e05721c90987ce6702bef89a1
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=5ee3f7e3f9d46150aadf44e4c4321a8a15c86ea8af08e6b6653774a2
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=5ee3f7e3f9d46150aadf44e4c4321a8a15c86ea8af08e6b6653774a2
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=f91843d4faa36d81732f614a02a8f8db1a8afd5e9f428b1d4271e85a
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=f91843d4faa36d81732f614a02a8f8db1a8afd5e9f428b1d4271e85a
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,989 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=cc56480aa1d01d213e5567748e50a120eecea0f10356a110b2b49300
[2025-06-26 01:17:13,989 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=cc56480aa1d01d213e5567748e50a120eecea0f10356a110b2b49300
[2025-06-26 01:17:13,989 W 701612 701645] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:13,991 I 701612 701612] actor_task_submitter.cc:73: Set actor max pending calls to -1 actor_id=95868628bee24beed493ac7204000000
[2025-06-26 01:17:13,991 I 701612 701612] core_worker.cc:3370: Creating actor actor_id=95868628bee24beed493ac7204000000
[2025-06-26 01:17:14,715 I 701612 701612] task_receiver.cc:175: Actor creation task finished, task_id: ffffffffffffffff95868628bee24beed493ac7204000000, actor_id: 95868628bee24beed493ac7204000000, actor_repr_name: 
[2025-06-26 01:18:13,989 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 911 total (8 active)
Queueing time: mean = 82.129 us, max = 1.730 ms, min = -0.000 s, total = 74.819 ms
Execution time:  mean = 969.417 us, total = 883.139 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 13.322 us, total = 7.993 ms, Queueing time: mean = 84.137 us, max = 1.618 ms, min = -0.000 s, total = 50.482 ms
	CoreWorker.ExitIfParentRayletDies - 61 total (1 active), Execution time: mean = 21.967 us, total = 1.340 ms, Queueing time: mean = 68.265 us, max = 141.070 us, min = 15.763 us, total = 4.164 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 22.950 us, total = 1.377 ms, Queueing time: mean = 43.457 us, max = 393.764 us, min = 12.239 us, total = 2.607 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 862.790 us, total = 51.767 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 197.344 us, total = 11.841 ms, Queueing time: mean = 122.301 us, max = 1.489 ms, min = 26.416 us, total = 7.338 ms
	CoreWorkerService.grpc_server.PushTask - 14 total (0 active), Execution time: mean = 56.989 ms, total = 797.847 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 14 total (0 active), Execution time: mean = 40.622 us, total = 568.704 us, Queueing time: mean = 49.149 us, max = 218.400 us, min = 9.210 us, total = 688.088 us
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 192.731 us, total = 2.313 ms, Queueing time: mean = 60.961 us, max = 111.179 us, min = -0.000 s, total = 731.534 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 8.223 us, total = 49.340 us, Queueing time: mean = 95.470 us, max = 193.521 us, min = 64.446 us, total = 572.818 us
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 4 total (0 active), Execution time: mean = 524.246 us, total = 2.097 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 4 total (0 active), Execution time: mean = 43.117 us, total = 172.468 us, Queueing time: mean = 160.300 us, max = 209.180 us, min = 74.649 us, total = 641.201 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us

-----------------
Task execution event stats:

Global stats: 5870 total (1 active)
Queueing time: mean = 223.230 us, max = 725.033 ms, min = -0.000 s, total = 1.310 s
Execution time:  mean = 164.519 us, total = 965.724 ms
Event stats:
	CoreWorker.CheckSignal - 5855 total (1 active), Execution time: mean = 29.650 us, total = 173.603 ms, Queueing time: mean = 99.795 us, max = 49.184 ms, min = -0.000 s, total = 584.303 ms
	CoreWorker.HandlePushTaskActor - 13 total (0 active), Execution time: mean = 5.160 ms, total = 67.082 ms, Queueing time: mean = 48.897 us, max = 150.582 us, min = 9.308 us, total = 635.657 us
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 40.886 us, max = 674.695 us, min = 12.238 us, total = 7.400 ms
Execution time:  mean = 439.411 us, total = 79.533 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 1.005 ms, total = 60.292 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 284.458 us, total = 17.067 ms, Queueing time: mean = 91.839 us, max = 674.695 us, min = 21.051 us, total = 5.510 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 33.812 us, total = 2.029 ms, Queueing time: mean = 31.129 us, max = 129.300 us, min = 12.238 us, total = 1.868 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.00544643 MiB
	total number of task attempts sent: 14
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:18:34,283 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:18:34,283 I 701612 701645] core_worker.cc:5107: Number of alive nodes:1
[2025-06-26 01:18:35,716 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:18:36,626 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:19:13,989 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 1805 total (8 active)
Queueing time: mean = 77.415 us, max = 1.730 ms, min = -0.000 s, total = 139.735 ms
Execution time:  mean = 46.326 ms, total = 83.618 s
Event stats:
	CoreWorker.RecoverObjects - 1199 total (1 active), Execution time: mean = 13.205 us, total = 15.832 ms, Queueing time: mean = 82.492 us, max = 1.618 ms, min = -0.000 s, total = 98.908 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 23.365 us, total = 2.804 ms, Queueing time: mean = 41.296 us, max = 393.764 us, min = 12.239 us, total = 4.956 ms
	CoreWorker.ExitIfParentRayletDies - 120 total (1 active), Execution time: mean = 21.123 us, total = 2.535 ms, Queueing time: mean = 79.082 us, max = 1.454 ms, min = 12.002 us, total = 9.490 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 879.865 us, total = 105.584 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 212.514 us, total = 25.502 ms, Queueing time: mean = 102.029 us, max = 1.489 ms, min = 26.416 us, total = 12.244 ms
	CoreWorkerService.grpc_server.PushTask - 24 total (0 active), Execution time: mean = 33.737 ms, total = 809.691 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 148.282 us, total = 3.559 ms, Queueing time: mean = 61.917 us, max = 150.276 us, min = -0.000 s, total = 1.486 ms
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 24 total (0 active), Execution time: mean = 40.706 us, total = 976.941 us, Queueing time: mean = 52.981 us, max = 218.400 us, min = 9.210 us, total = 1.272 ms
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 7.410 us, total = 88.917 us, Queueing time: mean = 69.076 us, max = 193.521 us, min = 14.251 us, total = 828.911 us
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 8 total (0 active), Execution time: mean = 581.349 us, total = 4.651 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 8 total (0 active), Execution time: mean = 43.637 us, total = 349.099 us, Queueing time: mean = 146.148 us, max = 353.734 us, min = 38.020 us, total = 1.169 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 4 total (1 active), Execution time: mean = 20.660 s, total = 82.639 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 3 total (0 active), Execution time: mean = 233.699 us, total = 701.096 us, Queueing time: mean = 403.076 us, max = 1.146 ms, min = 14.275 us, total = 1.209 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 3 total (0 active), Execution time: mean = 60.350 us, total = 181.051 us, Queueing time: mean = 167.359 us, max = 189.656 us, min = 126.713 us, total = 502.077 us
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 268.505 us, total = 537.011 us, Queueing time: mean = 38.283 us, max = 76.566 us, min = 76.566 us, total = 76.566 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 11813 total (1 active)
Queueing time: mean = 154.194 us, max = 725.033 ms, min = -0.001 s, total = 1.821 s
Execution time:  mean = 98.147 us, total = 1.159 s
Event stats:
	CoreWorker.CheckSignal - 11788 total (1 active), Execution time: mean = 30.469 us, total = 359.172 ms, Queueing time: mean = 92.887 us, max = 49.184 ms, min = -0.001 s, total = 1.095 s
	CoreWorker.HandlePushTaskActor - 23 total (0 active), Execution time: mean = 3.269 ms, total = 75.194 ms, Queueing time: mean = 49.055 us, max = 164.236 us, min = 9.308 us, total = 1.128 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 38.029 us, max = 674.695 us, min = 7.338 us, total = 13.728 ms
Execution time:  mean = 417.700 us, total = 150.790 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 960.842 us, total = 115.301 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 262.046 us, total = 31.446 ms, Queueing time: mean = 84.526 us, max = 674.695 us, min = 7.338 us, total = 10.143 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 32.478 us, total = 3.897 ms, Queueing time: mean = 29.690 us, max = 129.300 us, min = 11.832 us, total = 3.563 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.00933743 MiB
	total number of task attempts sent: 24
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:20:13,990 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 2684 total (8 active)
Queueing time: mean = 80.666 us, max = 5.321 ms, min = -0.000 s, total = 216.508 ms
Execution time:  mean = 31.189 ms, total = 83.710 s
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 13.118 us, total = 23.600 ms, Queueing time: mean = 89.721 us, max = 5.321 ms, min = -0.000 s, total = 161.409 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 23.047 us, total = 4.148 ms, Queueing time: mean = 41.800 us, max = 393.764 us, min = 12.239 us, total = 7.524 ms
	CoreWorker.ExitIfParentRayletDies - 180 total (1 active), Execution time: mean = 21.467 us, total = 3.864 ms, Queueing time: mean = 81.151 us, max = 1.454 ms, min = 12.002 us, total = 14.607 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 890.092 us, total = 160.217 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 224.039 us, total = 40.327 ms, Queueing time: mean = 93.119 us, max = 1.489 ms, min = 24.486 us, total = 16.761 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 126.599 us, total = 4.558 ms, Queueing time: mean = 63.269 us, max = 199.461 us, min = -0.000 s, total = 2.278 ms
	CoreWorkerService.grpc_server.PushTask - 30 total (0 active), Execution time: mean = 27.258 ms, total = 817.753 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 30 total (0 active), Execution time: mean = 42.339 us, total = 1.270 ms, Queueing time: mean = 48.425 us, max = 218.400 us, min = 9.210 us, total = 1.453 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 7.855 us, total = 141.397 us, Queueing time: mean = 65.274 us, max = 193.521 us, min = 14.251 us, total = 1.175 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 12 total (0 active), Execution time: mean = 594.690 us, total = 7.136 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 12 total (0 active), Execution time: mean = 57.397 us, total = 688.764 us, Queueing time: mean = 154.177 us, max = 456.428 us, min = 38.020 us, total = 1.850 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 4 total (1 active), Execution time: mean = 20.660 s, total = 82.639 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 3 total (0 active), Execution time: mean = 233.699 us, total = 701.096 us, Queueing time: mean = 403.076 us, max = 1.146 ms, min = 14.275 us, total = 1.209 ms
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 339.228 us, total = 1.018 ms, Queueing time: mean = 48.588 us, max = 76.566 us, min = 69.197 us, total = 145.763 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 3 total (0 active), Execution time: mean = 60.350 us, total = 181.051 us, Queueing time: mean = 167.359 us, max = 189.656 us, min = 126.713 us, total = 502.077 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 17739 total (1 active)
Queueing time: mean = 139.093 us, max = 725.033 ms, min = -0.001 s, total = 2.467 s
Execution time:  mean = 75.979 us, total = 1.348 s
Event stats:
	CoreWorker.CheckSignal - 17708 total (1 active), Execution time: mean = 30.572 us, total = 541.365 ms, Queueing time: mean = 98.287 us, max = 49.184 ms, min = -0.001 s, total = 1.740 s
	CoreWorker.HandlePushTaskActor - 29 total (0 active), Execution time: mean = 2.806 ms, total = 81.380 ms, Queueing time: mean = 51.173 us, max = 164.236 us, min = 9.308 us, total = 1.484 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 37.994 us, max = 674.695 us, min = 7.338 us, total = 20.555 ms
Execution time:  mean = 424.742 us, total = 229.785 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 982.229 us, total = 176.801 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 261.352 us, total = 47.043 ms, Queueing time: mean = 79.260 us, max = 674.695 us, min = 7.338 us, total = 14.267 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 32.195 us, total = 5.795 ms, Queueing time: mean = 34.808 us, max = 303.956 us, min = 11.832 us, total = 6.265 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.011672 MiB
	total number of task attempts sent: 30
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:20:38,141 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:20:38,141 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:20:38,141 I 701612 701645] core_worker.cc:5107: Number of alive nodes:2
[2025-06-26 01:20:43,258 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:20:43,258 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:20:43,262 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:20:43,262 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:21:05,529 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=1debeedd775f559e844e3f300a626c3ae54f84472517a3fe5853f7f0
[2025-06-26 01:21:08,662 I 701612 701645] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:21:08,662 I 701612 701645] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:21:13,990 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 3565 total (8 active)
Queueing time: mean = 78.769 us, max = 5.321 ms, min = -0.000 s, total = 280.811 ms
Execution time:  mean = 66.153 ms, total = 235.834 s
Event stats:
	CoreWorker.RecoverObjects - 2398 total (1 active), Execution time: mean = 13.274 us, total = 31.831 ms, Queueing time: mean = 87.156 us, max = 5.321 ms, min = -0.000 s, total = 208.999 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 23.128 us, total = 5.551 ms, Queueing time: mean = 43.073 us, max = 393.764 us, min = 12.239 us, total = 10.338 ms
	CoreWorker.ExitIfParentRayletDies - 240 total (1 active), Execution time: mean = 21.583 us, total = 5.180 ms, Queueing time: mean = 82.394 us, max = 1.454 ms, min = 12.002 us, total = 19.775 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 915.703 us, total = 219.769 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 230.061 us, total = 55.215 ms, Queueing time: mean = 87.460 us, max = 1.489 ms, min = 21.522 us, total = 20.990 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 114.037 us, total = 5.474 ms, Queueing time: mean = 66.375 us, max = 199.461 us, min = -0.000 s, total = 3.186 ms
	CoreWorkerService.grpc_server.PushTask - 30 total (0 active), Execution time: mean = 27.258 ms, total = 817.753 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 30 total (0 active), Execution time: mean = 42.339 us, total = 1.270 ms, Queueing time: mean = 48.425 us, max = 218.400 us, min = 9.210 us, total = 1.453 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 7.898 us, total = 189.554 us, Queueing time: mean = 64.051 us, max = 193.521 us, min = 14.251 us, total = 1.537 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 16 total (0 active), Execution time: mean = 546.989 us, total = 8.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 16 total (0 active), Execution time: mean = 51.744 us, total = 827.905 us, Queueing time: mean = 142.884 us, max = 456.428 us, min = 24.371 us, total = 2.286 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.075 s, total = 234.672 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 215.843 us, total = 1.727 ms, Queueing time: mean = 370.873 us, max = 1.146 ms, min = 14.275 us, total = 2.967 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 398.032 us, total = 1.592 ms, Queueing time: mean = 53.369 us, max = 76.566 us, min = 67.713 us, total = 213.476 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 23668 total (1 active)
Queueing time: mean = 128.120 us, max = 725.033 ms, min = -0.001 s, total = 3.032 s
Execution time:  mean = 64.596 us, total = 1.529 s
Event stats:
	CoreWorker.CheckSignal - 23637 total (1 active), Execution time: mean = 30.564 us, total = 722.434 ms, Queueing time: mean = 97.535 us, max = 49.184 ms, min = -0.001 s, total = 2.305 s
	CoreWorker.HandlePushTaskActor - 29 total (0 active), Execution time: mean = 2.806 ms, total = 81.380 ms, Queueing time: mean = 51.173 us, max = 164.236 us, min = 9.308 us, total = 1.484 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 38.664 us, max = 674.695 us, min = 7.338 us, total = 27.877 ms
Execution time:  mean = 415.451 us, total = 299.541 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 961.148 us, total = 230.676 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 253.867 us, total = 60.928 ms, Queueing time: mean = 80.377 us, max = 674.695 us, min = 7.338 us, total = 19.290 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 32.464 us, total = 7.791 ms, Queueing time: mean = 35.682 us, max = 303.956 us, min = 11.832 us, total = 8.564 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.011672 MiB
	total number of task attempts sent: 30
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:22:13,991 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 4433 total (8 active)
Queueing time: mean = 78.067 us, max = 5.321 ms, min = -0.000 s, total = 346.073 ms
Execution time:  mean = 53.218 ms, total = 235.915 s
Event stats:
	CoreWorker.RecoverObjects - 2998 total (1 active), Execution time: mean = 13.161 us, total = 39.458 ms, Queueing time: mean = 86.181 us, max = 5.321 ms, min = -0.000 s, total = 258.370 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 23.141 us, total = 6.942 ms, Queueing time: mean = 42.388 us, max = 393.764 us, min = 12.239 us, total = 12.716 ms
	CoreWorker.ExitIfParentRayletDies - 300 total (1 active), Execution time: mean = 21.643 us, total = 6.493 ms, Queueing time: mean = 84.295 us, max = 1.454 ms, min = 12.002 us, total = 25.289 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 913.382 us, total = 274.015 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 227.083 us, total = 68.125 ms, Queueing time: mean = 90.244 us, max = 1.489 ms, min = 21.522 us, total = 27.073 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 104.835 us, total = 6.290 ms, Queueing time: mean = 68.629 us, max = 199.461 us, min = -0.000 s, total = 4.118 ms
	CoreWorkerService.grpc_server.PushTask - 30 total (0 active), Execution time: mean = 27.258 ms, total = 817.753 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 7.996 us, total = 239.886 us, Queueing time: mean = 71.283 us, max = 237.077 us, min = 14.251 us, total = 2.138 ms
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 30 total (0 active), Execution time: mean = 42.339 us, total = 1.270 ms, Queueing time: mean = 48.425 us, max = 218.400 us, min = 9.210 us, total = 1.453 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 20 total (0 active), Execution time: mean = 516.395 us, total = 10.328 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 20 total (0 active), Execution time: mean = 49.148 us, total = 982.962 us, Queueing time: mean = 127.688 us, max = 456.428 us, min = 18.234 us, total = 2.554 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.075 s, total = 234.672 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 215.843 us, total = 1.727 ms, Queueing time: mean = 370.873 us, max = 1.146 ms, min = 14.275 us, total = 2.967 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 426.042 us, total = 2.130 ms, Queueing time: mean = 57.473 us, max = 76.566 us, min = 67.713 us, total = 287.363 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.833 us, total = 9.667 us, Queueing time: mean = 20.790 us, max = 41.579 us, min = 41.579 us, total = 41.579 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 29603 total (1 active)
Queueing time: mean = 119.143 us, max = 725.033 ms, min = -0.001 s, total = 3.527 s
Execution time:  mean = 57.595 us, total = 1.705 s
Event stats:
	CoreWorker.CheckSignal - 29572 total (1 active), Execution time: mean = 30.386 us, total = 898.564 ms, Queueing time: mean = 94.687 us, max = 49.184 ms, min = -0.001 s, total = 2.800 s
	CoreWorker.HandlePushTaskActor - 29 total (0 active), Execution time: mean = 2.806 ms, total = 81.380 ms, Queueing time: mean = 51.173 us, max = 164.236 us, min = 9.308 us, total = 1.484 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 38.904 us, max = 674.695 us, min = 7.338 us, total = 35.052 ms
Execution time:  mean = 409.855 us, total = 369.279 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 946.880 us, total = 284.064 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 251.411 us, total = 75.423 ms, Queueing time: mean = 81.982 us, max = 674.695 us, min = 7.338 us, total = 24.595 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 32.155 us, total = 9.646 ms, Queueing time: mean = 34.785 us, max = 303.956 us, min = 11.120 us, total = 10.435 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.011672 MiB
	total number of task attempts sent: 30
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:23:13,991 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 5299 total (8 active)
Queueing time: mean = 77.734 us, max = 5.321 ms, min = -0.000 s, total = 411.911 ms
Execution time:  mean = 44.536 ms, total = 235.997 s
Event stats:
	CoreWorker.RecoverObjects - 3597 total (1 active), Execution time: mean = 13.127 us, total = 47.217 ms, Queueing time: mean = 86.351 us, max = 5.321 ms, min = -0.000 s, total = 310.603 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 23.092 us, total = 8.313 ms, Queueing time: mean = 40.971 us, max = 393.764 us, min = 12.239 us, total = 14.750 ms
	CoreWorker.ExitIfParentRayletDies - 360 total (1 active), Execution time: mean = 21.572 us, total = 7.766 ms, Queueing time: mean = 82.616 us, max = 1.454 ms, min = 12.002 us, total = 29.742 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 910.995 us, total = 327.958 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 227.887 us, total = 82.039 ms, Queueing time: mean = 89.663 us, max = 1.489 ms, min = 21.522 us, total = 32.279 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 98.065 us, total = 7.061 ms, Queueing time: mean = 69.500 us, max = 199.461 us, min = -0.000 s, total = 5.004 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 7.995 us, total = 287.826 us, Queueing time: mean = 71.443 us, max = 237.077 us, min = 14.251 us, total = 2.572 ms
	CoreWorkerService.grpc_server.PushTask - 30 total (0 active), Execution time: mean = 27.258 ms, total = 817.753 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 30 total (0 active), Execution time: mean = 42.339 us, total = 1.270 ms, Queueing time: mean = 48.425 us, max = 218.400 us, min = 9.210 us, total = 1.453 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 24 total (0 active), Execution time: mean = 533.952 us, total = 12.815 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 24 total (0 active), Execution time: mean = 46.511 us, total = 1.116 ms, Queueing time: mean = 129.726 us, max = 456.428 us, min = 18.234 us, total = 3.113 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.075 s, total = 234.672 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 215.843 us, total = 1.727 ms, Queueing time: mean = 370.873 us, max = 1.146 ms, min = 14.275 us, total = 2.967 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 427.846 us, total = 2.567 ms, Queueing time: mean = 53.544 us, max = 76.566 us, min = 33.898 us, total = 321.261 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.833 us, total = 9.667 us, Queueing time: mean = 20.790 us, max = 41.579 us, min = 41.579 us, total = 41.579 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 35539 total (1 active)
Queueing time: mean = 113.076 us, max = 725.033 ms, min = -0.001 s, total = 4.019 s
Execution time:  mean = 52.946 us, total = 1.882 s
Event stats:
	CoreWorker.CheckSignal - 35508 total (1 active), Execution time: mean = 30.282 us, total = 1.075 s, Queueing time: mean = 92.703 us, max = 49.184 ms, min = -0.001 s, total = 3.292 s
	CoreWorker.HandlePushTaskActor - 29 total (0 active), Execution time: mean = 2.806 ms, total = 81.380 ms, Queueing time: mean = 51.173 us, max = 164.236 us, min = 9.308 us, total = 1.484 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 39.060 us, max = 674.695 us, min = 7.338 us, total = 42.224 ms
Execution time:  mean = 406.826 us, total = 439.779 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 938.122 us, total = 337.724 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 250.537 us, total = 90.193 ms, Queueing time: mean = 81.192 us, max = 674.695 us, min = 7.338 us, total = 29.229 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 32.545 us, total = 11.716 ms, Queueing time: mean = 36.034 us, max = 599.113 us, min = 11.120 us, total = 12.972 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.011672 MiB
	total number of task attempts sent: 30
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:24:13,992 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 6166 total (8 active)
Queueing time: mean = 76.992 us, max = 5.321 ms, min = -0.000 s, total = 474.735 ms
Execution time:  mean = 38.287 ms, total = 236.078 s
Event stats:
	CoreWorker.RecoverObjects - 4197 total (1 active), Execution time: mean = 13.119 us, total = 55.062 ms, Queueing time: mean = 86.012 us, max = 5.321 ms, min = -0.000 s, total = 360.991 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 420 total (0 active), Execution time: mean = 23.151 us, total = 9.723 ms, Queueing time: mean = 39.970 us, max = 393.764 us, min = 12.239 us, total = 16.787 ms
	CoreWorker.ExitIfParentRayletDies - 420 total (1 active), Execution time: mean = 21.750 us, total = 9.135 ms, Queueing time: mean = 80.582 us, max = 1.454 ms, min = 12.002 us, total = 33.844 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 420 total (0 active), Execution time: mean = 905.207 us, total = 380.187 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 420 total (1 active), Execution time: mean = 230.110 us, total = 96.646 ms, Queueing time: mean = 87.099 us, max = 1.489 ms, min = -0.000 s, total = 36.581 ms
	CoreWorker.RecordMetrics - 84 total (1 active), Execution time: mean = 94.664 us, total = 7.952 ms, Queueing time: mean = 71.593 us, max = 199.461 us, min = -0.000 s, total = 6.014 ms
	CoreWorker.TryDelPendingObjectRefStreams - 42 total (1 active), Execution time: mean = 8.734 us, total = 366.811 us, Queueing time: mean = 77.947 us, max = 237.077 us, min = 14.251 us, total = 3.274 ms
	CoreWorkerService.grpc_server.PushTask - 30 total (0 active), Execution time: mean = 27.258 ms, total = 817.753 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 30 total (0 active), Execution time: mean = 42.339 us, total = 1.270 ms, Queueing time: mean = 48.425 us, max = 218.400 us, min = 9.210 us, total = 1.453 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 28 total (0 active), Execution time: mean = 513.366 us, total = 14.374 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 28 total (0 active), Execution time: mean = 44.556 us, total = 1.248 ms, Queueing time: mean = 119.853 us, max = 456.428 us, min = 18.234 us, total = 3.356 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.075 s, total = 234.672 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 215.843 us, total = 1.727 ms, Queueing time: mean = 370.873 us, max = 1.146 ms, min = 14.275 us, total = 2.967 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	CoreWorker.PrintEventStats - 7 total (1 active, 1 running), Execution time: mean = 452.403 us, total = 3.167 ms, Queueing time: mean = 51.361 us, max = 76.566 us, min = 33.898 us, total = 359.524 us
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.833 us, total = 9.667 us, Queueing time: mean = 20.790 us, max = 41.579 us, min = 41.579 us, total = 41.579 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 41477 total (1 active)
Queueing time: mean = 108.491 us, max = 725.033 ms, min = -0.001 s, total = 4.500 s
Execution time:  mean = 49.582 us, total = 2.056 s
Event stats:
	CoreWorker.CheckSignal - 41446 total (1 active), Execution time: mean = 30.162 us, total = 1.250 s, Queueing time: mean = 91.033 us, max = 49.184 ms, min = -0.001 s, total = 3.773 s
	CoreWorker.HandlePushTaskActor - 29 total (0 active), Execution time: mean = 2.806 ms, total = 81.380 ms, Queueing time: mean = 51.173 us, max = 164.236 us, min = 9.308 us, total = 1.484 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1261 total (1 active)
Queueing time: mean = 38.999 us, max = 674.695 us, min = 7.338 us, total = 49.178 ms
Execution time:  mean = 403.553 us, total = 508.880 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 420 total (0 active), Execution time: mean = 929.587 us, total = 390.426 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 420 total (1 active), Execution time: mean = 249.125 us, total = 104.632 ms, Queueing time: mean = 81.181 us, max = 674.695 us, min = 7.338 us, total = 34.096 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 420 total (0 active), Execution time: mean = 32.561 us, total = 13.676 ms, Queueing time: mean = 35.856 us, max = 599.113 us, min = 10.530 us, total = 15.060 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.011672 MiB
	total number of task attempts sent: 30
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:25:13,993 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 7032 total (8 active)
Queueing time: mean = 80.281 us, max = 5.835 ms, min = -0.000 s, total = 564.539 ms
Execution time:  mean = 33.583 ms, total = 236.159 s
Event stats:
	CoreWorker.RecoverObjects - 4796 total (1 active), Execution time: mean = 13.117 us, total = 62.907 ms, Queueing time: mean = 89.205 us, max = 5.835 ms, min = -0.000 s, total = 427.827 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 480 total (0 active), Execution time: mean = 23.272 us, total = 11.171 ms, Queueing time: mean = 39.102 us, max = 393.764 us, min = 12.239 us, total = 18.769 ms
	CoreWorker.ExitIfParentRayletDies - 480 total (1 active), Execution time: mean = 21.608 us, total = 10.372 ms, Queueing time: mean = 86.690 us, max = 2.507 ms, min = 12.002 us, total = 41.611 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 480 total (0 active), Execution time: mean = 902.759 us, total = 433.324 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 480 total (1 active), Execution time: mean = 229.894 us, total = 110.349 ms, Queueing time: mean = 97.725 us, max = 5.615 ms, min = -0.000 s, total = 46.908 ms
	CoreWorker.RecordMetrics - 96 total (1 active), Execution time: mean = 91.483 us, total = 8.782 ms, Queueing time: mean = 71.804 us, max = 199.461 us, min = -0.000 s, total = 6.893 ms
	CoreWorker.TryDelPendingObjectRefStreams - 48 total (1 active), Execution time: mean = 8.710 us, total = 418.067 us, Queueing time: mean = 80.954 us, max = 237.077 us, min = 14.251 us, total = 3.886 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 32 total (0 active), Execution time: mean = 516.670 us, total = 16.533 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 32 total (0 active), Execution time: mean = 43.874 us, total = 1.404 ms, Queueing time: mean = 146.546 us, max = 1.092 ms, min = 18.234 us, total = 4.689 ms
	CoreWorkerService.grpc_server.PushTask - 30 total (0 active), Execution time: mean = 27.258 ms, total = 817.753 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 30 total (0 active), Execution time: mean = 42.339 us, total = 1.270 ms, Queueing time: mean = 48.425 us, max = 218.400 us, min = 9.210 us, total = 1.453 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.075 s, total = 234.672 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 215.843 us, total = 1.727 ms, Queueing time: mean = 370.873 us, max = 1.146 ms, min = 14.275 us, total = 2.967 ms
	CoreWorker.PrintEventStats - 8 total (1 active, 1 running), Execution time: mean = 466.518 us, total = 3.732 ms, Queueing time: mean = 53.480 us, max = 76.566 us, min = 33.898 us, total = 427.839 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.833 us, total = 9.667 us, Queueing time: mean = 20.790 us, max = 41.579 us, min = 41.579 us, total = 41.579 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 47399 total (1 active)
Queueing time: mean = 108.063 us, max = 725.033 ms, min = -0.001 s, total = 5.122 s
Execution time:  mean = 47.170 us, total = 2.236 s
Event stats:
	CoreWorker.CheckSignal - 47368 total (1 active), Execution time: mean = 30.176 us, total = 1.429 s, Queueing time: mean = 92.788 us, max = 49.184 ms, min = -0.001 s, total = 4.395 s
	CoreWorker.HandlePushTaskActor - 29 total (0 active), Execution time: mean = 2.806 ms, total = 81.380 ms, Queueing time: mean = 51.173 us, max = 164.236 us, min = 9.308 us, total = 1.484 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1441 total (1 active)
Queueing time: mean = 42.381 us, max = 5.182 ms, min = 7.338 us, total = 61.071 ms
Execution time:  mean = 402.688 us, total = 580.274 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 480 total (0 active), Execution time: mean = 928.480 us, total = 445.670 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 480 total (1 active), Execution time: mean = 247.651 us, total = 118.872 ms, Queueing time: mean = 92.072 us, max = 5.182 ms, min = 7.338 us, total = 44.195 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 480 total (0 active), Execution time: mean = 32.470 us, total = 15.586 ms, Queueing time: mean = 35.113 us, max = 599.113 us, min = 10.094 us, total = 16.854 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.011672 MiB
	total number of task attempts sent: 30
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:26:13,993 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 7898 total (8 active)
Queueing time: mean = 81.300 us, max = 5.835 ms, min = -0.000 s, total = 642.104 ms
Execution time:  mean = 29.913 ms, total = 236.250 s
Event stats:
	CoreWorker.RecoverObjects - 5395 total (1 active), Execution time: mean = 13.251 us, total = 71.487 ms, Queueing time: mean = 90.357 us, max = 5.835 ms, min = -0.000 s, total = 487.475 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 540 total (0 active), Execution time: mean = 23.128 us, total = 12.489 ms, Queueing time: mean = 40.979 us, max = 410.863 us, min = 12.239 us, total = 22.129 ms
	CoreWorker.ExitIfParentRayletDies - 540 total (1 active), Execution time: mean = 21.570 us, total = 11.648 ms, Queueing time: mean = 91.602 us, max = 2.507 ms, min = 12.002 us, total = 49.465 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 540 total (0 active), Execution time: mean = 918.932 us, total = 496.223 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 540 total (1 active), Execution time: mean = 231.528 us, total = 125.025 ms, Queueing time: mean = 96.504 us, max = 5.615 ms, min = -0.000 s, total = 52.112 ms
	CoreWorker.RecordMetrics - 108 total (1 active), Execution time: mean = 88.428 us, total = 9.550 ms, Queueing time: mean = 72.620 us, max = 199.461 us, min = -0.000 s, total = 7.843 ms
	CoreWorker.TryDelPendingObjectRefStreams - 54 total (1 active), Execution time: mean = 9.132 us, total = 493.122 us, Queueing time: mean = 77.095 us, max = 237.077 us, min = 11.322 us, total = 4.163 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 36 total (0 active), Execution time: mean = 502.847 us, total = 18.102 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 36 total (0 active), Execution time: mean = 42.826 us, total = 1.542 ms, Queueing time: mean = 136.255 us, max = 1.092 ms, min = 18.234 us, total = 4.905 ms
	CoreWorkerService.grpc_server.PushTask - 30 total (0 active), Execution time: mean = 27.258 ms, total = 817.753 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 30 total (0 active), Execution time: mean = 42.339 us, total = 1.270 ms, Queueing time: mean = 48.425 us, max = 218.400 us, min = 9.210 us, total = 1.453 ms
	CoreWorker.PrintEventStats - 9 total (1 active, 1 running), Execution time: mean = 470.809 us, total = 4.237 ms, Queueing time: mean = 53.846 us, max = 76.566 us, min = 33.898 us, total = 484.610 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.075 s, total = 234.672 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 215.843 us, total = 1.727 ms, Queueing time: mean = 370.873 us, max = 1.146 ms, min = 14.275 us, total = 2.967 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.833 us, total = 9.667 us, Queueing time: mean = 20.790 us, max = 41.579 us, min = 41.579 us, total = 41.579 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 53308 total (1 active)
Queueing time: mean = 110.294 us, max = 725.033 ms, min = -0.001 s, total = 5.880 s
Execution time:  mean = 45.586 us, total = 2.430 s
Event stats:
	CoreWorker.CheckSignal - 53277 total (1 active), Execution time: mean = 30.476 us, total = 1.624 s, Queueing time: mean = 96.714 us, max = 49.184 ms, min = -0.001 s, total = 5.153 s
	CoreWorker.HandlePushTaskActor - 29 total (0 active), Execution time: mean = 2.806 ms, total = 81.380 ms, Queueing time: mean = 51.173 us, max = 164.236 us, min = 9.308 us, total = 1.484 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1621 total (1 active)
Queueing time: mean = 41.919 us, max = 5.182 ms, min = 7.338 us, total = 67.951 ms
Execution time:  mean = 408.684 us, total = 662.477 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 540 total (0 active), Execution time: mean = 945.694 us, total = 510.675 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 540 total (1 active), Execution time: mean = 248.578 us, total = 134.232 ms, Queueing time: mean = 90.990 us, max = 5.182 ms, min = 7.338 us, total = 49.135 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 540 total (0 active), Execution time: mean = 32.268 us, total = 17.425 ms, Queueing time: mean = 34.805 us, max = 599.113 us, min = 10.094 us, total = 18.795 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.011672 MiB
	total number of task attempts sent: 30
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:27:13,994 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 8765 total (8 active)
Queueing time: mean = 84.546 us, max = 5.835 ms, min = -0.000 s, total = 741.047 ms
Execution time:  mean = 26.964 ms, total = 236.339 s
Event stats:
	CoreWorker.RecoverObjects - 5994 total (1 active), Execution time: mean = 13.217 us, total = 79.220 ms, Queueing time: mean = 94.570 us, max = 5.835 ms, min = -0.000 s, total = 566.855 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 600 total (0 active), Execution time: mean = 23.562 us, total = 14.137 ms, Queueing time: mean = 41.886 us, max = 540.335 us, min = 12.239 us, total = 25.132 ms
	CoreWorker.ExitIfParentRayletDies - 600 total (1 active), Execution time: mean = 21.773 us, total = 13.064 ms, Queueing time: mean = 91.183 us, max = 2.507 ms, min = 12.002 us, total = 54.710 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 600 total (0 active), Execution time: mean = 926.212 us, total = 555.727 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 600 total (1 active), Execution time: mean = 233.182 us, total = 139.909 ms, Queueing time: mean = 100.511 us, max = 5.615 ms, min = -0.000 s, total = 60.307 ms
	CoreWorker.RecordMetrics - 120 total (1 active), Execution time: mean = 86.748 us, total = 10.410 ms, Queueing time: mean = 81.443 us, max = 922.193 us, min = -0.000 s, total = 9.773 ms
	CoreWorker.TryDelPendingObjectRefStreams - 60 total (1 active), Execution time: mean = 9.093 us, total = 545.597 us, Queueing time: mean = 77.271 us, max = 237.077 us, min = 11.322 us, total = 4.636 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 40 total (0 active), Execution time: mean = 491.979 us, total = 19.679 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 40 total (0 active), Execution time: mean = 42.204 us, total = 1.688 ms, Queueing time: mean = 137.583 us, max = 1.092 ms, min = 18.234 us, total = 5.503 ms
	CoreWorkerService.grpc_server.PushTask - 30 total (0 active), Execution time: mean = 27.258 ms, total = 817.753 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 30 total (0 active), Execution time: mean = 42.339 us, total = 1.270 ms, Queueing time: mean = 48.425 us, max = 218.400 us, min = 9.210 us, total = 1.453 ms
	CoreWorker.PrintEventStats - 10 total (1 active, 1 running), Execution time: mean = 468.666 us, total = 4.687 ms, Queueing time: mean = 53.533 us, max = 76.566 us, min = 33.898 us, total = 535.331 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.075 s, total = 234.672 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 215.843 us, total = 1.727 ms, Queueing time: mean = 370.873 us, max = 1.146 ms, min = 14.275 us, total = 2.967 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 6.938 us, total = 20.814 us, Queueing time: mean = 36.472 us, max = 67.837 us, min = 41.579 us, total = 109.416 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 59218 total (1 active)
Queueing time: mean = 111.632 us, max = 725.033 ms, min = -0.001 s, total = 6.611 s
Execution time:  mean = 44.416 us, total = 2.630 s
Event stats:
	CoreWorker.CheckSignal - 59187 total (1 active), Execution time: mean = 30.814 us, total = 1.824 s, Queueing time: mean = 99.409 us, max = 49.184 ms, min = -0.001 s, total = 5.884 s
	CoreWorker.HandlePushTaskActor - 29 total (0 active), Execution time: mean = 2.806 ms, total = 81.380 ms, Queueing time: mean = 51.173 us, max = 164.236 us, min = 9.308 us, total = 1.484 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1801 total (1 active)
Queueing time: mean = 44.782 us, max = 5.182 ms, min = 7.338 us, total = 80.652 ms
Execution time:  mean = 409.353 us, total = 737.246 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 600 total (0 active), Execution time: mean = 946.056 us, total = 567.634 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 600 total (1 active), Execution time: mean = 249.940 us, total = 149.964 ms, Queueing time: mean = 94.703 us, max = 5.182 ms, min = 7.338 us, total = 56.822 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 600 total (0 active), Execution time: mean = 32.504 us, total = 19.502 ms, Queueing time: mean = 39.680 us, max = 2.619 ms, min = 10.094 us, total = 23.808 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.011672 MiB
	total number of task attempts sent: 30
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:28:13,994 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 9646 total (8 active)
Queueing time: mean = 83.891 us, max = 5.835 ms, min = -0.000 s, total = 809.216 ms
Execution time:  mean = 63.093 ms, total = 608.598 s
Event stats:
	CoreWorker.RecoverObjects - 6594 total (1 active), Execution time: mean = 13.166 us, total = 86.814 ms, Queueing time: mean = 93.868 us, max = 5.835 ms, min = -0.000 s, total = 618.965 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 660 total (0 active), Execution time: mean = 23.314 us, total = 15.387 ms, Queueing time: mean = 41.648 us, max = 540.335 us, min = 12.239 us, total = 27.488 ms
	CoreWorker.ExitIfParentRayletDies - 660 total (1 active), Execution time: mean = 22.090 us, total = 14.580 ms, Queueing time: mean = 90.347 us, max = 2.507 ms, min = 12.002 us, total = 59.629 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 660 total (0 active), Execution time: mean = 931.422 us, total = 614.739 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 660 total (1 active), Execution time: mean = 233.836 us, total = 154.332 ms, Queueing time: mean = 99.676 us, max = 5.615 ms, min = -0.000 s, total = 65.786 ms
	CoreWorker.RecordMetrics - 132 total (1 active), Execution time: mean = 86.556 us, total = 11.425 ms, Queueing time: mean = 81.498 us, max = 922.193 us, min = -0.000 s, total = 10.758 ms
	CoreWorker.TryDelPendingObjectRefStreams - 66 total (1 active), Execution time: mean = 9.223 us, total = 608.724 us, Queueing time: mean = 79.930 us, max = 244.661 us, min = 11.322 us, total = 5.275 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 44 total (0 active), Execution time: mean = 500.241 us, total = 22.011 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 44 total (0 active), Execution time: mean = 41.212 us, total = 1.813 ms, Queueing time: mean = 144.143 us, max = 1.092 ms, min = 18.234 us, total = 6.342 ms
	CoreWorkerService.grpc_server.PushTask - 36 total (0 active), Execution time: mean = 23.036 ms, total = 829.282 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 36 total (0 active), Execution time: mean = 41.316 us, total = 1.487 ms, Queueing time: mean = 56.029 us, max = 218.400 us, min = 9.210 us, total = 2.017 ms
	CoreWorker.PrintEventStats - 11 total (1 active, 1 running), Execution time: mean = 476.570 us, total = 5.242 ms, Queueing time: mean = 53.343 us, max = 76.566 us, min = 33.898 us, total = 586.772 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 60.683 s, total = 606.832 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.519 us, total = 1.913 ms, Queueing time: mean = 354.792 us, max = 1.146 ms, min = 14.275 us, total = 3.193 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 6.938 us, total = 20.814 us, Queueing time: mean = 36.472 us, max = 67.837 us, min = 41.579 us, total = 109.416 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 65156 total (1 active)
Queueing time: mean = 109.537 us, max = 725.033 ms, min = -0.001 s, total = 7.137 s
Execution time:  mean = 43.249 us, total = 2.818 s
Event stats:
	CoreWorker.CheckSignal - 65119 total (1 active), Execution time: mean = 30.808 us, total = 2.006 s, Queueing time: mean = 98.428 us, max = 49.184 ms, min = -0.001 s, total = 6.410 s
	CoreWorker.HandlePushTaskActor - 35 total (0 active), Execution time: mean = 2.477 ms, total = 86.679 ms, Queueing time: mean = 56.819 us, max = 179.485 us, min = 9.308 us, total = 1.989 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1981 total (1 active)
Queueing time: mean = 45.363 us, max = 5.182 ms, min = 7.338 us, total = 89.863 ms
Execution time:  mean = 409.844 us, total = 811.900 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 660 total (0 active), Execution time: mean = 948.884 us, total = 626.264 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 660 total (1 active), Execution time: mean = 248.510 us, total = 164.016 ms, Queueing time: mean = 96.748 us, max = 5.182 ms, min = 7.338 us, total = 63.854 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 660 total (0 active), Execution time: mean = 32.537 us, total = 21.475 ms, Queueing time: mean = 39.375 us, max = 2.619 ms, min = 10.094 us, total = 25.987 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0140066 MiB
	total number of task attempts sent: 36
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:29:13,995 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 10512 total (8 active)
Queueing time: mean = 82.794 us, max = 5.835 ms, min = -0.000 s, total = 870.335 ms
Execution time:  mean = 57.904 ms, total = 608.684 s
Event stats:
	CoreWorker.RecoverObjects - 7193 total (1 active), Execution time: mean = 13.143 us, total = 94.536 ms, Queueing time: mean = 92.100 us, max = 5.835 ms, min = -0.000 s, total = 662.477 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 720 total (0 active), Execution time: mean = 23.149 us, total = 16.668 ms, Queueing time: mean = 44.893 us, max = 2.394 ms, min = 12.239 us, total = 32.323 ms
	CoreWorker.ExitIfParentRayletDies - 720 total (1 active), Execution time: mean = 22.791 us, total = 16.409 ms, Queueing time: mean = 87.421 us, max = 2.507 ms, min = -0.000 s, total = 62.943 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 720 total (0 active), Execution time: mean = 931.723 us, total = 670.840 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 720 total (1 active), Execution time: mean = 233.641 us, total = 168.221 ms, Queueing time: mean = 100.896 us, max = 5.615 ms, min = -0.000 s, total = 72.645 ms
	CoreWorker.RecordMetrics - 144 total (1 active), Execution time: mean = 85.796 us, total = 12.355 ms, Queueing time: mean = 82.156 us, max = 922.193 us, min = -0.000 s, total = 11.830 ms
	CoreWorker.TryDelPendingObjectRefStreams - 72 total (1 active), Execution time: mean = 9.188 us, total = 661.508 us, Queueing time: mean = 79.138 us, max = 244.661 us, min = 11.322 us, total = 5.698 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 48 total (0 active), Execution time: mean = 516.973 us, total = 24.815 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 48 total (0 active), Execution time: mean = 40.726 us, total = 1.955 ms, Queueing time: mean = 153.399 us, max = 1.092 ms, min = 18.234 us, total = 7.363 ms
	CoreWorkerService.grpc_server.PushTask - 36 total (0 active), Execution time: mean = 23.036 ms, total = 829.282 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 36 total (0 active), Execution time: mean = 41.316 us, total = 1.487 ms, Queueing time: mean = 56.029 us, max = 218.400 us, min = 9.210 us, total = 2.017 ms
	CoreWorker.PrintEventStats - 12 total (1 active, 1 running), Execution time: mean = 474.566 us, total = 5.695 ms, Queueing time: mean = 55.786 us, max = 82.662 us, min = 33.898 us, total = 669.434 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 60.683 s, total = 606.832 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.519 us, total = 1.913 ms, Queueing time: mean = 354.792 us, max = 1.146 ms, min = 14.275 us, total = 3.193 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 6.938 us, total = 20.814 us, Queueing time: mean = 36.472 us, max = 67.837 us, min = 41.579 us, total = 109.416 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 71090 total (1 active)
Queueing time: mean = 107.531 us, max = 725.033 ms, min = -0.001 s, total = 7.644 s
Execution time:  mean = 42.123 us, total = 2.995 s
Event stats:
	CoreWorker.CheckSignal - 71053 total (1 active), Execution time: mean = 30.721 us, total = 2.183 s, Queueing time: mean = 97.350 us, max = 49.184 ms, min = -0.001 s, total = 6.917 s
	CoreWorker.HandlePushTaskActor - 35 total (0 active), Execution time: mean = 2.477 ms, total = 86.679 ms, Queueing time: mean = 56.819 us, max = 179.485 us, min = 9.308 us, total = 1.989 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2161 total (1 active)
Queueing time: mean = 45.389 us, max = 5.182 ms, min = 7.338 us, total = 98.087 ms
Execution time:  mean = 407.825 us, total = 881.311 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 720 total (0 active), Execution time: mean = 943.886 us, total = 679.598 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 720 total (1 active), Execution time: mean = 247.450 us, total = 178.164 ms, Queueing time: mean = 96.915 us, max = 5.182 ms, min = 7.338 us, total = 69.779 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 720 total (0 active), Execution time: mean = 32.504 us, total = 23.403 ms, Queueing time: mean = 39.285 us, max = 2.619 ms, min = 10.094 us, total = 28.285 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0140066 MiB
	total number of task attempts sent: 36
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:30:13,995 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 11379 total (8 active)
Queueing time: mean = 84.269 us, max = 9.153 ms, min = -0.000 s, total = 958.894 ms
Execution time:  mean = 53.500 ms, total = 608.781 s
Event stats:
	CoreWorker.RecoverObjects - 7793 total (1 active), Execution time: mean = 13.120 us, total = 102.243 ms, Queueing time: mean = 93.688 us, max = 9.153 ms, min = -0.000 s, total = 730.108 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 780 total (0 active), Execution time: mean = 23.181 us, total = 18.081 ms, Queueing time: mean = 43.997 us, max = 2.394 ms, min = 12.239 us, total = 34.317 ms
	CoreWorker.ExitIfParentRayletDies - 780 total (1 active), Execution time: mean = 22.606 us, total = 17.633 ms, Queueing time: mean = 88.574 us, max = 2.507 ms, min = -0.000 s, total = 69.088 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 780 total (0 active), Execution time: mean = 948.167 us, total = 739.570 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 780 total (1 active), Execution time: mean = 234.052 us, total = 182.560 ms, Queueing time: mean = 106.820 us, max = 5.615 ms, min = -0.000 s, total = 83.320 ms
	CoreWorker.RecordMetrics - 156 total (1 active), Execution time: mean = 84.938 us, total = 13.250 ms, Queueing time: mean = 82.000 us, max = 922.193 us, min = -0.000 s, total = 12.792 ms
	CoreWorker.TryDelPendingObjectRefStreams - 78 total (1 active), Execution time: mean = 9.163 us, total = 714.724 us, Queueing time: mean = 83.110 us, max = 297.926 us, min = 11.322 us, total = 6.483 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 52 total (0 active), Execution time: mean = 523.132 us, total = 27.203 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 52 total (0 active), Execution time: mean = 39.961 us, total = 2.078 ms, Queueing time: mean = 147.277 us, max = 1.092 ms, min = 15.904 us, total = 7.658 ms
	CoreWorkerService.grpc_server.PushTask - 36 total (0 active), Execution time: mean = 23.036 ms, total = 829.282 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 36 total (0 active), Execution time: mean = 41.316 us, total = 1.487 ms, Queueing time: mean = 56.029 us, max = 218.400 us, min = 9.210 us, total = 2.017 ms
	CoreWorker.PrintEventStats - 13 total (1 active, 1 running), Execution time: mean = 475.638 us, total = 6.183 ms, Queueing time: mean = 57.041 us, max = 82.662 us, min = 33.898 us, total = 741.535 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 60.683 s, total = 606.832 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.519 us, total = 1.913 ms, Queueing time: mean = 354.792 us, max = 1.146 ms, min = 14.275 us, total = 3.193 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 6.938 us, total = 20.814 us, Queueing time: mean = 36.472 us, max = 67.837 us, min = 41.579 us, total = 109.416 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 77007 total (1 active)
Queueing time: mean = 108.136 us, max = 725.033 ms, min = -0.001 s, total = 8.327 s
Execution time:  mean = 41.219 us, total = 3.174 s
Event stats:
	CoreWorker.CheckSignal - 76970 total (1 active), Execution time: mean = 30.693 us, total = 2.362 s, Queueing time: mean = 98.738 us, max = 49.184 ms, min = -0.001 s, total = 7.600 s
	CoreWorker.HandlePushTaskActor - 35 total (0 active), Execution time: mean = 2.477 ms, total = 86.679 ms, Queueing time: mean = 56.819 us, max = 179.485 us, min = 9.308 us, total = 1.989 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2341 total (1 active)
Queueing time: mean = 45.017 us, max = 5.182 ms, min = 7.338 us, total = 105.386 ms
Execution time:  mean = 410.167 us, total = 960.201 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 780 total (0 active), Execution time: mean = 947.245 us, total = 738.851 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 780 total (1 active), Execution time: mean = 250.502 us, total = 195.391 ms, Queueing time: mean = 95.615 us, max = 5.182 ms, min = 7.338 us, total = 74.580 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 780 total (0 active), Execution time: mean = 33.094 us, total = 25.813 ms, Queueing time: mean = 39.466 us, max = 2.619 ms, min = 10.094 us, total = 30.784 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0140066 MiB
	total number of task attempts sent: 36
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:31:13,996 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 12245 total (8 active)
Queueing time: mean = 84.056 us, max = 9.153 ms, min = -0.000 s, total = 1.029 s
Execution time:  mean = 49.724 ms, total = 608.868 s
Event stats:
	CoreWorker.RecoverObjects - 8392 total (1 active), Execution time: mean = 13.172 us, total = 110.539 ms, Queueing time: mean = 93.471 us, max = 9.153 ms, min = -0.000 s, total = 784.409 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 840 total (0 active), Execution time: mean = 23.316 us, total = 19.586 ms, Queueing time: mean = 43.284 us, max = 2.394 ms, min = 12.239 us, total = 36.358 ms
	CoreWorker.ExitIfParentRayletDies - 840 total (1 active), Execution time: mean = 22.508 us, total = 18.906 ms, Queueing time: mean = 87.870 us, max = 2.507 ms, min = -0.000 s, total = 73.810 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 840 total (0 active), Execution time: mean = 950.174 us, total = 798.147 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 840 total (1 active), Execution time: mean = 232.828 us, total = 195.575 ms, Queueing time: mean = 107.057 us, max = 5.615 ms, min = -0.000 s, total = 89.928 ms
	CoreWorker.RecordMetrics - 168 total (1 active), Execution time: mean = 84.461 us, total = 14.189 ms, Queueing time: mean = 81.775 us, max = 922.193 us, min = -0.000 s, total = 13.738 ms
	CoreWorker.TryDelPendingObjectRefStreams - 84 total (1 active), Execution time: mean = 9.134 us, total = 767.295 us, Queueing time: mean = 83.055 us, max = 297.926 us, min = 11.322 us, total = 6.977 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 56 total (0 active), Execution time: mean = 534.618 us, total = 29.939 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 56 total (0 active), Execution time: mean = 42.106 us, total = 2.358 ms, Queueing time: mean = 156.643 us, max = 1.092 ms, min = 15.904 us, total = 8.772 ms
	CoreWorkerService.grpc_server.PushTask - 36 total (0 active), Execution time: mean = 23.036 ms, total = 829.282 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 36 total (0 active), Execution time: mean = 41.316 us, total = 1.487 ms, Queueing time: mean = 56.029 us, max = 218.400 us, min = 9.210 us, total = 2.017 ms
	CoreWorker.PrintEventStats - 14 total (1 active, 1 running), Execution time: mean = 480.542 us, total = 6.728 ms, Queueing time: mean = 63.013 us, max = 140.653 us, min = 33.898 us, total = 882.188 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 60.683 s, total = 606.832 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.519 us, total = 1.913 ms, Queueing time: mean = 354.792 us, max = 1.146 ms, min = 14.275 us, total = 3.193 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 6.938 us, total = 20.814 us, Queueing time: mean = 36.472 us, max = 67.837 us, min = 41.579 us, total = 109.416 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 82936 total (1 active)
Queueing time: mean = 107.119 us, max = 725.033 ms, min = -0.001 s, total = 8.884 s
Execution time:  mean = 40.488 us, total = 3.358 s
Event stats:
	CoreWorker.CheckSignal - 82899 total (1 active), Execution time: mean = 30.715 us, total = 2.546 s, Queueing time: mean = 98.392 us, max = 49.184 ms, min = -0.001 s, total = 8.157 s
	CoreWorker.HandlePushTaskActor - 35 total (0 active), Execution time: mean = 2.477 ms, total = 86.679 ms, Queueing time: mean = 56.819 us, max = 179.485 us, min = 9.308 us, total = 1.989 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2521 total (1 active)
Queueing time: mean = 45.152 us, max = 5.182 ms, min = 7.338 us, total = 113.828 ms
Execution time:  mean = 412.365 us, total = 1.040 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 840 total (0 active), Execution time: mean = 950.285 us, total = 798.239 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 840 total (1 active), Execution time: mean = 253.466 us, total = 212.912 ms, Queueing time: mean = 95.682 us, max = 5.182 ms, min = 7.338 us, total = 80.373 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 840 total (0 active), Execution time: mean = 33.662 us, total = 28.276 ms, Queueing time: mean = 39.800 us, max = 2.619 ms, min = 10.094 us, total = 33.432 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0140066 MiB
	total number of task attempts sent: 36
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:32:13,997 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 13113 total (8 active)
Queueing time: mean = 83.164 us, max = 9.153 ms, min = -0.000 s, total = 1.091 s
Execution time:  mean = 46.439 ms, total = 608.953 s
Event stats:
	CoreWorker.RecoverObjects - 8992 total (1 active), Execution time: mean = 13.024 us, total = 117.113 ms, Queueing time: mean = 92.257 us, max = 9.153 ms, min = -0.000 s, total = 829.577 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 900 total (0 active), Execution time: mean = 23.417 us, total = 21.076 ms, Queueing time: mean = 42.728 us, max = 2.394 ms, min = 12.239 us, total = 38.455 ms
	CoreWorker.ExitIfParentRayletDies - 900 total (1 active), Execution time: mean = 22.481 us, total = 20.233 ms, Queueing time: mean = 87.928 us, max = 2.507 ms, min = -0.000 s, total = 79.135 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 900 total (0 active), Execution time: mean = 949.786 us, total = 854.808 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 900 total (1 active), Execution time: mean = 233.803 us, total = 210.423 ms, Queueing time: mean = 107.122 us, max = 5.615 ms, min = -0.000 s, total = 96.410 ms
	CoreWorker.RecordMetrics - 180 total (1 active), Execution time: mean = 83.705 us, total = 15.067 ms, Queueing time: mean = 82.082 us, max = 922.193 us, min = -0.000 s, total = 14.775 ms
	CoreWorker.TryDelPendingObjectRefStreams - 90 total (1 active), Execution time: mean = 9.079 us, total = 817.113 us, Queueing time: mean = 84.111 us, max = 319.222 us, min = 11.322 us, total = 7.570 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 60 total (0 active), Execution time: mean = 541.124 us, total = 32.467 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 60 total (0 active), Execution time: mean = 41.706 us, total = 2.502 ms, Queueing time: mean = 153.075 us, max = 1.092 ms, min = 15.904 us, total = 9.184 ms
	CoreWorkerService.grpc_server.PushTask - 36 total (0 active), Execution time: mean = 23.036 ms, total = 829.282 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 36 total (0 active), Execution time: mean = 41.316 us, total = 1.487 ms, Queueing time: mean = 56.029 us, max = 218.400 us, min = 9.210 us, total = 2.017 ms
	CoreWorker.PrintEventStats - 15 total (1 active, 1 running), Execution time: mean = 494.311 us, total = 7.415 ms, Queueing time: mean = 67.999 us, max = 140.653 us, min = 33.898 us, total = 1.020 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 60.683 s, total = 606.832 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.519 us, total = 1.913 ms, Queueing time: mean = 354.792 us, max = 1.146 ms, min = 14.275 us, total = 3.193 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 6.126 us, total = 24.504 us, Queueing time: mean = 31.989 us, max = 67.837 us, min = 18.542 us, total = 127.958 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 88871 total (1 active)
Queueing time: mean = 105.647 us, max = 725.033 ms, min = -0.001 s, total = 9.389 s
Execution time:  mean = 39.772 us, total = 3.535 s
Event stats:
	CoreWorker.CheckSignal - 88834 total (1 active), Execution time: mean = 30.651 us, total = 2.723 s, Queueing time: mean = 97.503 us, max = 49.184 ms, min = -0.001 s, total = 8.662 s
	CoreWorker.HandlePushTaskActor - 35 total (0 active), Execution time: mean = 2.477 ms, total = 86.679 ms, Queueing time: mean = 56.819 us, max = 179.485 us, min = 9.308 us, total = 1.989 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2701 total (1 active)
Queueing time: mean = 45.561 us, max = 5.182 ms, min = 7.338 us, total = 123.060 ms
Execution time:  mean = 410.918 us, total = 1.110 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 900 total (0 active), Execution time: mean = 946.764 us, total = 852.088 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 900 total (1 active), Execution time: mean = 252.399 us, total = 227.159 ms, Queueing time: mean = 97.478 us, max = 5.182 ms, min = 7.338 us, total = 87.730 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 900 total (0 active), Execution time: mean = 33.884 us, total = 30.496 ms, Queueing time: mean = 39.230 us, max = 2.619 ms, min = 10.094 us, total = 35.307 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0140066 MiB
	total number of task attempts sent: 36
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:33:13,997 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 13992 total (8 active)
Queueing time: mean = 99.678 us, max = 125.435 ms, min = -0.000 s, total = 1.395 s
Execution time:  mean = 43.547 ms, total = 609.309 s
Event stats:
	CoreWorker.RecoverObjects - 9590 total (1 active), Execution time: mean = 13.080 us, total = 125.440 ms, Queueing time: mean = 103.687 us, max = 114.118 ms, min = -0.000 s, total = 994.360 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 960 total (0 active), Execution time: mean = 23.699 us, total = 22.751 ms, Queueing time: mean = 42.297 us, max = 2.394 ms, min = 12.239 us, total = 40.605 ms
	CoreWorker.ExitIfParentRayletDies - 960 total (1 active), Execution time: mean = 22.550 us, total = 21.648 ms, Queueing time: mean = 87.670 us, max = 2.507 ms, min = -0.000 s, total = 84.164 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 960 total (0 active), Execution time: mean = 951.304 us, total = 913.252 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 960 total (1 active), Execution time: mean = 235.873 us, total = 226.438 ms, Queueing time: mean = 235.646 us, max = 125.435 ms, min = -0.000 s, total = 226.220 ms
	CoreWorker.RecordMetrics - 192 total (1 active), Execution time: mean = 84.251 us, total = 16.176 ms, Queueing time: mean = 82.498 us, max = 922.193 us, min = -0.000 s, total = 15.840 ms
	CoreWorker.TryDelPendingObjectRefStreams - 96 total (1 active), Execution time: mean = 9.025 us, total = 866.364 us, Queueing time: mean = 84.209 us, max = 319.222 us, min = 11.322 us, total = 8.084 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 64 total (0 active), Execution time: mean = 535.499 us, total = 34.272 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 64 total (0 active), Execution time: mean = 42.738 us, total = 2.735 ms, Queueing time: mean = 148.908 us, max = 1.092 ms, min = 15.904 us, total = 9.530 ms
	CoreWorkerService.grpc_server.PushTask - 42 total (0 active), Execution time: mean = 19.989 ms, total = 839.559 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 42 total (0 active), Execution time: mean = 42.022 us, total = 1.765 ms, Queueing time: mean = 54.007 us, max = 218.400 us, min = 9.210 us, total = 2.268 ms
	CoreWorker.PrintEventStats - 16 total (1 active, 1 running), Execution time: mean = 489.200 us, total = 7.827 ms, Queueing time: mean = 68.126 us, max = 140.653 us, min = 33.898 us, total = 1.090 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 60.683 s, total = 606.832 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.519 us, total = 1.913 ms, Queueing time: mean = 354.792 us, max = 1.146 ms, min = 14.275 us, total = 3.193 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 6.126 us, total = 24.504 us, Queueing time: mean = 31.989 us, max = 67.837 us, min = 18.542 us, total = 127.958 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	CoreWorkerService.grpc_server.LocalGC.HandleRequestImpl - 1 total (0 active), Execution time: mean = 127.409 ms, total = 127.409 ms, Queueing time: mean = 139.937 us, max = 139.937 us, min = 139.937 us, total = 139.937 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.LocalGC - 1 total (0 active), Execution time: mean = 128.455 ms, total = 128.455 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 94797 total (1 active)
Queueing time: mean = 104.608 us, max = 725.033 ms, min = -0.001 s, total = 9.917 s
Execution time:  mean = 40.505 us, total = 3.840 s
Event stats:
	CoreWorker.CheckSignal - 94754 total (1 active), Execution time: mean = 31.878 us, total = 3.021 s, Queueing time: mean = 96.975 us, max = 49.184 ms, min = -0.001 s, total = 9.189 s
	CoreWorker.HandlePushTaskActor - 41 total (0 active), Execution time: mean = 2.297 ms, total = 94.191 ms, Queueing time: mean = 58.505 us, max = 179.485 us, min = 9.308 us, total = 2.399 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2881 total (1 active)
Queueing time: mean = 47.249 us, max = 6.374 ms, min = 7.338 us, total = 136.125 ms
Execution time:  mean = 412.600 us, total = 1.189 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 960 total (0 active), Execution time: mean = 948.552 us, total = 910.609 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 960 total (1 active), Execution time: mean = 255.461 us, total = 245.243 ms, Queueing time: mean = 102.965 us, max = 6.374 ms, min = 7.338 us, total = 98.846 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 960 total (0 active), Execution time: mean = 34.065 us, total = 32.703 ms, Queueing time: mean = 38.809 us, max = 2.619 ms, min = 9.701 us, total = 37.257 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0163412 MiB
	total number of task attempts sent: 42
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:34:13,998 I 701612 701645] core_worker.cc:902: Event stats:


Global stats: 14860 total (8 active)
Queueing time: mean = 98.386 us, max = 125.435 ms, min = -0.000 s, total = 1.462 s
Execution time:  mean = 41.009 ms, total = 609.401 s
Event stats:
	CoreWorker.RecoverObjects - 10189 total (1 active), Execution time: mean = 13.107 us, total = 133.543 ms, Queueing time: mean = 102.744 us, max = 114.118 ms, min = -0.000 s, total = 1.047 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1020 total (0 active), Execution time: mean = 24.025 us, total = 24.506 ms, Queueing time: mean = 42.030 us, max = 2.394 ms, min = 12.239 us, total = 42.871 ms
	CoreWorker.ExitIfParentRayletDies - 1020 total (1 active), Execution time: mean = 22.484 us, total = 22.934 ms, Queueing time: mean = 87.662 us, max = 2.507 ms, min = -0.000 s, total = 89.415 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1020 total (0 active), Execution time: mean = 953.271 us, total = 972.336 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 1020 total (1 active), Execution time: mean = 237.998 us, total = 242.758 ms, Queueing time: mean = 226.838 us, max = 125.435 ms, min = -0.000 s, total = 231.375 ms
	CoreWorker.RecordMetrics - 204 total (1 active), Execution time: mean = 84.256 us, total = 17.188 ms, Queueing time: mean = 81.874 us, max = 922.193 us, min = -0.000 s, total = 16.702 ms
	CoreWorker.TryDelPendingObjectRefStreams - 102 total (1 active), Execution time: mean = 9.046 us, total = 922.677 us, Queueing time: mean = 84.688 us, max = 319.222 us, min = 11.322 us, total = 8.638 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 68 total (0 active), Execution time: mean = 533.326 us, total = 36.266 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 68 total (0 active), Execution time: mean = 42.118 us, total = 2.864 ms, Queueing time: mean = 149.705 us, max = 1.092 ms, min = 15.904 us, total = 10.180 ms
	CoreWorkerService.grpc_server.PushTask - 43 total (0 active), Execution time: mean = 19.551 ms, total = 840.693 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PushTask.HandleRequestImpl - 43 total (0 active), Execution time: mean = 42.120 us, total = 1.811 ms, Queueing time: mean = 54.050 us, max = 218.400 us, min = 9.210 us, total = 2.324 ms
	CoreWorker.PrintEventStats - 17 total (1 active, 1 running), Execution time: mean = 486.143 us, total = 8.264 ms, Queueing time: mean = 66.467 us, max = 140.653 us, min = 33.898 us, total = 1.130 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 60.683 s, total = 606.832 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.519 us, total = 1.913 ms, Queueing time: mean = 354.792 us, max = 1.146 ms, min = 14.275 us, total = 3.193 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 81.921 us, total = 655.364 us, Queueing time: mean = 184.034 us, max = 255.657 us, min = 126.713 us, total = 1.472 ms
	PeriodicalRunner.RunFnPeriodically - 7 total (0 active), Execution time: mean = 107.865 us, total = 755.056 us, Queueing time: mean = 1.074 ms, max = 1.730 ms, min = 51.739 us, total = 7.520 ms
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 6.126 us, total = 24.504 us, Queueing time: mean = 31.989 us, max = 67.837 us, min = 18.542 us, total = 127.958 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 168.138 us, total = 168.138 us, Queueing time: mean = 13.302 us, max = 13.302 us, min = 13.302 us, total = 13.302 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 457.431 us, total = 457.431 us, Queueing time: mean = 14.814 us, max = 14.814 us, min = 14.814 us, total = 14.814 us
	CoreWorkerService.grpc_server.LocalGC.HandleRequestImpl - 1 total (0 active), Execution time: mean = 127.409 ms, total = 127.409 ms, Queueing time: mean = 139.937 us, max = 139.937 us, min = 139.937 us, total = 139.937 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.033 ms, total = 1.033 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 2.341 ms, total = 2.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.905 us, total = 38.905 us, Queueing time: mean = 46.124 us, max = 46.124 us, min = 46.124 us, total = 46.124 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 980.147 us, total = 980.147 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.LocalGC - 1 total (0 active), Execution time: mean = 128.455 ms, total = 128.455 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 100730 total (1 active)
Queueing time: mean = 103.656 us, max = 725.033 ms, min = -0.001 s, total = 10.441 s
Execution time:  mean = 39.942 us, total = 4.023 s
Event stats:
	CoreWorker.CheckSignal - 100686 total (1 active), Execution time: mean = 31.816 us, total = 3.203 s, Queueing time: mean = 96.473 us, max = 49.184 ms, min = -0.001 s, total = 9.713 s
	CoreWorker.HandlePushTaskActor - 42 total (0 active), Execution time: mean = 2.260 ms, total = 94.935 ms, Queueing time: mean = 57.741 us, max = 179.485 us, min = 9.308 us, total = 2.425 ms
	CoreWorker.HandlePushTask - 1 total (0 active), Execution time: mean = 725.024 ms, total = 725.024 ms, Queueing time: mean = 388.084 us, max = 388.084 us, min = 388.084 us, total = 388.084 us
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 15.802 us, total = 15.802 us, Queueing time: mean = 725.033 ms, max = 725.033 ms, min = 725.033 ms, total = 725.033 ms

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3061 total (1 active)
Queueing time: mean = 46.602 us, max = 6.374 ms, min = 7.338 us, total = 142.650 ms
Execution time:  mean = 414.435 us, total = 1.269 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1020 total (0 active), Execution time: mean = 951.510 us, total = 970.541 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 1020 total (1 active), Execution time: mean = 257.798 us, total = 262.954 ms, Queueing time: mean = 101.289 us, max = 6.374 ms, min = 7.338 us, total = 103.315 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1020 total (0 active), Execution time: mean = 34.260 us, total = 34.945 ms, Queueing time: mean = 38.542 us, max = 2.619 ms, min = 9.701 us, total = 39.312 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 145.608 us, total = 145.608 us, Queueing time: mean = 22.351 us, max = 22.351 us, min = 22.351 us, total = 22.351 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0167303 MiB
	total number of task attempts sent: 43
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


