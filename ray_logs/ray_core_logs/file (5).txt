[2025-06-26 00:48:20,779 I 693801 693852] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 693801
[2025-06-26 00:48:20,783 I 693801 693852] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-06-26 00:48:20,786 I 693801 693852] grpc_server.cc:141: driver server started, listening on port 10003.
[2025-06-26 00:48:20,789 I 693801 693852] core_worker.cc:542: Initializing worker at address: ************:10003 worker_id=02000000ffffffffffffffffffffffffffffffffffffffffffffffff node_id=11d1fceddfd4a7d70539f452d9f1c4f91705b1be5f541b4b015ed1f5
[2025-06-26 00:48:20,792 I 693801 693852] task_event_buffer.cc:287: Reporting task events to G<PERSON> every 1000ms.
[2025-06-26 00:48:20,794 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=e90de5051af7eab045a5db8c90b1509053a14387b123a1d3416ab7b2
[2025-06-26 00:48:20,794 I 693801 693872] core_worker.cc:5107: Number of alive nodes:1
[2025-06-26 00:48:20,794 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=11d1fceddfd4a7d70539f452d9f1c4f91705b1be5f541b4b015ed1f5
[2025-06-26 00:48:20,794 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 14 total (7 active)
Queueing time: mean = 7.583 us, max = 44.684 us, min = 17.301 us, total = 106.168 us
Execution time:  mean = 244.260 us, total = 3.420 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 6 total (5 active, 1 running), Execution time: mean = 5.333 us, total = 31.997 us, Queueing time: mean = 7.447 us, max = 44.684 us, min = 44.684 us, total = 44.684 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 10.941 us, max = 24.631 us, min = 19.133 us, total = 43.764 us
Execution time:  mean = 507.841 us, total = 2.031 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 298.884 us, total = 298.884 us, Queueing time: mean = 19.133 us, max = 19.133 us, min = 19.133 us, total = 19.133 us
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 1.405 ms, total = 1.405 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 1
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:48:20,797 I 693801 693852] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-06-26 00:48:20,798 I 693801 693852] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-06-26 00:48:20,798 I 693801 693852] event.cc:331: Set ray event level to warning
[2025-06-26 00:48:24,101 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet e90de5051af7eab045a5db8c90b1509053a14387b123a1d3416ab7b2
[2025-06-26 00:48:54,552 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=aeb88ee76014cf968e47e7c5541354741a624a0d7183bbd80e7cc5f8
[2025-06-26 00:48:54,554 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet aeb88ee76014cf968e47e7c5541354741a624a0d7183bbd80e7cc5f8
[2025-06-26 00:49:20,795 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 3407 total (8 active)
Queueing time: mean = 157.934 us, max = 8.073 ms, min = -0.000 s, total = 538.082 ms
Execution time:  mean = 163.919 ms, total = 558.471 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 1222 total (0 active), Execution time: mean = 198.987 us, total = 243.162 ms, Queueing time: mean = 369.444 us, max = 3.026 ms, min = 4.545 us, total = 451.461 ms
	NodeManagerService.grpc_client.RequestWorkerLease - 1222 total (0 active), Execution time: mean = 312.485 ms, total = 381.856 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 15.858 us, total = 9.515 ms, Queueing time: mean = 113.863 us, max = 8.073 ms, min = -0.000 s, total = 68.318 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 68 total (0 active), Execution time: mean = 952.436 us, total = 64.766 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 68 total (0 active), Execution time: mean = 21.018 us, total = 1.429 ms, Queueing time: mean = 68.078 us, max = 628.739 us, min = 10.200 us, total = 4.629 ms
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 291.808 us, total = 17.508 ms, Queueing time: mean = 67.285 us, max = 240.421 us, min = 22.508 us, total = 4.037 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 19 total (0 active), Execution time: mean = 131.632 ns, total = 2.501 us, Queueing time: mean = 82.208 us, max = 187.116 us, min = 36.158 us, total = 1.562 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 18 total (0 active), Execution time: mean = 384.129 us, total = 6.914 ms, Queueing time: mean = 40.772 us, max = 72.006 us, min = 21.561 us, total = 733.888 us
	CoreWorker.SubmitTask - 18 total (0 active), Execution time: mean = 162.404 us, total = 2.923 ms, Queueing time: mean = 51.767 us, max = 230.580 us, min = 14.752 us, total = 931.810 us
	NodeManagerService.grpc_client.ReturnWorker - 18 total (0 active), Execution time: mean = 1.718 ms, total = 30.925 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 18 total (0 active), Execution time: mean = 23.846 us, total = 429.230 us, Queueing time: mean = 38.333 us, max = 94.623 us, min = 17.996 us, total = 689.993 us
	CoreWorkerService.grpc_client.PushTask - 18 total (0 active), Execution time: mean = 7.914 s, total = 142.454 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 58.860 us, total = 706.325 us, Queueing time: mean = 73.081 us, max = 236.166 us, min = 13.207 us, total = 876.970 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 9.094 us, total = 54.561 us, Queueing time: mean = 192.040 us, max = 823.031 us, min = 39.103 us, total = 1.152 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 4 total (0 active), Execution time: mean = 297.323 us, total = 1.189 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 4 total (0 active), Execution time: mean = 45.294 us, total = 181.176 us, Queueing time: mean = 55.804 us, max = 101.216 us, min = 29.933 us, total = 223.218 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 2 total (1 active), Execution time: mean = 5.843 ms, total = 11.686 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 16.880 s, total = 33.760 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 2 total (0 active), Execution time: mean = 15.216 us, total = 30.431 us, Queueing time: mean = 44.868 us, max = 53.306 us, min = 36.430 us, total = 89.736 us
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 1 total (0 active), Execution time: mean = 79.861 us, total = 79.861 us, Queueing time: mean = 218.445 us, max = 218.445 us, min = 218.445 us, total = 218.445 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 422.276 us, total = 422.276 us, Queueing time: mean = 30.708 us, max = 30.708 us, min = 30.708 us, total = 30.708 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 39.232 us, max = 240.626 us, min = 12.961 us, total = 7.101 ms
Execution time:  mean = 461.233 us, total = 83.483 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 44.138 us, total = 2.648 ms, Queueing time: mean = 34.098 us, max = 240.626 us, min = 12.961 us, total = 2.046 ms
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 322.528 us, total = 19.352 ms, Queueing time: mean = 83.841 us, max = 180.243 us, min = 27.774 us, total = 5.030 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 1.019 ms, total = 61.155 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 1
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.00703144 MiB
	total number of task attempts sent: 53
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:50:20,796 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 9415 total (13 active)
Queueing time: mean = 209.796 us, max = 8.073 ms, min = -0.000 s, total = 1.975 s
Execution time:  mean = 91.924 ms, total = 865.468 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease - 3772 total (1 active), Execution time: mean = 156.592 ms, total = 590.665 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 3771 total (0 active), Execution time: mean = 174.738 us, total = 658.937 ms, Queueing time: mean = 472.117 us, max = 3.848 ms, min = 3.304 us, total = 1.780 s
	CoreWorker.RecoverObjects - 1199 total (1 active), Execution time: mean = 15.839 us, total = 18.990 ms, Queueing time: mean = 109.885 us, max = 8.073 ms, min = -0.000 s, total = 131.752 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 136 total (0 active), Execution time: mean = 933.700 us, total = 126.983 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 136 total (0 active), Execution time: mean = 22.737 us, total = 3.092 ms, Queueing time: mean = 137.511 us, max = 3.551 ms, min = 10.200 us, total = 18.702 ms
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 282.579 us, total = 33.909 ms, Queueing time: mean = 95.822 us, max = 1.013 ms, min = -0.000 s, total = 11.499 ms
	CoreWorker.SubmitTask - 36 total (0 active), Execution time: mean = 194.952 us, total = 7.018 ms, Queueing time: mean = 462.776 us, max = 3.347 ms, min = 14.752 us, total = 16.660 ms
	CoreWorkerService.grpc_client.PushTask - 35 total (4 active), Execution time: mean = 6.860 s, total = 240.099 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerMemoryStore.Put.get_async_callbacks - 32 total (0 active), Execution time: mean = 128.094 ns, total = 4.099 us, Queueing time: mean = 90.818 us, max = 200.031 us, min = 36.158 us, total = 2.906 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 31 total (0 active), Execution time: mean = 376.251 us, total = 11.664 ms, Queueing time: mean = 40.487 us, max = 72.006 us, min = 21.524 us, total = 1.255 ms
	NodeManagerService.grpc_client.ReturnWorker - 31 total (0 active), Execution time: mean = 1.818 ms, total = 56.365 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 31 total (0 active), Execution time: mean = 25.269 us, total = 783.340 us, Queueing time: mean = 37.856 us, max = 94.623 us, min = 17.996 us, total = 1.174 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 53.386 us, total = 1.281 ms, Queueing time: mean = 147.881 us, max = 1.781 ms, min = 13.207 us, total = 3.549 ms
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 10.237 us, total = 122.844 us, Queueing time: mean = 282.064 us, max = 1.806 ms, min = 35.296 us, total = 3.385 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 8 total (0 active), Execution time: mean = 43.451 us, total = 347.609 us, Queueing time: mean = 59.367 us, max = 113.262 us, min = 29.933 us, total = 474.934 us
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 8 total (0 active), Execution time: mean = 325.853 us, total = 2.607 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling - 2 total (1 active), Execution time: mean = 5.843 ms, total = 11.686 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 520.893 us, total = 1.042 ms, Queueing time: mean = 24.015 us, max = 48.030 us, min = 48.030 us, total = 48.030 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 16.880 s, total = 33.760 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 2 total (0 active), Execution time: mean = 15.216 us, total = 30.431 us, Queueing time: mean = 44.868 us, max = 53.306 us, min = 36.430 us, total = 89.736 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 1 total (0 active), Execution time: mean = 79.861 us, total = 79.861 us, Queueing time: mean = 218.445 us, max = 218.445 us, min = 218.445 us, total = 218.445 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 422.276 us, total = 422.276 us, Queueing time: mean = 30.708 us, max = 30.708 us, min = 30.708 us, total = 30.708 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 40.928 us, max = 389.721 us, min = 12.961 us, total = 14.775 ms
Execution time:  mean = 461.550 us, total = 166.620 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 43.421 us, total = 5.210 ms, Queueing time: mean = 42.633 us, max = 389.721 us, min = 12.961 us, total = 5.116 ms
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 316.683 us, total = 38.002 ms, Queueing time: mean = 80.287 us, max = 189.320 us, min = 20.382 us, total = 9.634 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 1.026 ms, total = 123.079 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0136366 MiB
	total number of task attempts sent: 99
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:51:20,797 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 10243 total (8 active)
Queueing time: mean = 199.381 us, max = 8.073 ms, min = -0.000 s, total = 2.042 s
Execution time:  mean = 88.486 ms, total = 906.365 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 3772 total (0 active), Execution time: mean = 174.761 us, total = 659.199 ms, Queueing time: mean = 472.119 us, max = 3.848 ms, min = 3.304 us, total = 1.781 s
	NodeManagerService.grpc_client.RequestWorkerLease - 3772 total (0 active), Execution time: mean = 157.078 ms, total = 592.500 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 1798 total (1 active), Execution time: mean = 15.646 us, total = 28.132 ms, Queueing time: mean = 103.440 us, max = 8.073 ms, min = -0.000 s, total = 185.986 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 196 total (0 active), Execution time: mean = 923.123 us, total = 180.932 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 196 total (0 active), Execution time: mean = 22.868 us, total = 4.482 ms, Queueing time: mean = 106.768 us, max = 3.551 ms, min = 10.200 us, total = 20.926 ms
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 261.833 us, total = 47.130 ms, Queueing time: mean = 97.815 us, max = 1.013 ms, min = -0.000 s, total = 17.607 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 37 total (0 active), Execution time: mean = 127.216 ns, total = 4.707 us, Queueing time: mean = 92.079 us, max = 200.031 us, min = 36.158 us, total = 3.407 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 36 total (0 active), Execution time: mean = 375.816 us, total = 13.529 ms, Queueing time: mean = 45.781 us, max = 250.489 us, min = 20.296 us, total = 1.648 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 55.137 us, total = 1.985 ms, Queueing time: mean = 127.168 us, max = 1.781 ms, min = 13.207 us, total = 4.578 ms
	CoreWorker.SubmitTask - 36 total (0 active), Execution time: mean = 194.952 us, total = 7.018 ms, Queueing time: mean = 462.776 us, max = 3.347 ms, min = 14.752 us, total = 16.660 ms
	NodeManagerService.grpc_client.ReturnWorker - 36 total (0 active), Execution time: mean = 1.741 ms, total = 62.665 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_client.PushTask - 36 total (0 active), Execution time: mean = 7.752 s, total = 279.071 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 36 total (0 active), Execution time: mean = 25.450 us, total = 916.216 us, Queueing time: mean = 59.585 us, max = 475.434 us, min = 16.566 us, total = 2.145 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 11.796 us, total = 212.324 us, Queueing time: mean = 216.023 us, max = 1.806 ms, min = 35.296 us, total = 3.888 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 12 total (0 active), Execution time: mean = 41.677 us, total = 500.129 us, Queueing time: mean = 79.679 us, max = 297.198 us, min = 23.411 us, total = 956.147 us
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 12 total (0 active), Execution time: mean = 356.610 us, total = 4.279 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 530.794 us, total = 1.592 ms, Queueing time: mean = 52.239 us, max = 108.687 us, min = 48.030 us, total = 156.717 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 2 total (1 active), Execution time: mean = 5.843 ms, total = 11.686 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 16.880 s, total = 33.760 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 2 total (0 active), Execution time: mean = 15.216 us, total = 30.431 us, Queueing time: mean = 44.868 us, max = 53.306 us, min = 36.430 us, total = 89.736 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 1 total (0 active), Execution time: mean = 79.861 us, total = 79.861 us, Queueing time: mean = 218.445 us, max = 218.445 us, min = 218.445 us, total = 218.445 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 422.276 us, total = 422.276 us, Queueing time: mean = 30.708 us, max = 30.708 us, min = 30.708 us, total = 30.708 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 39.440 us, max = 389.721 us, min = 9.918 us, total = 21.337 ms
Execution time:  mean = 450.839 us, total = 243.904 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 40.590 us, total = 7.306 ms, Queueing time: mean = 42.268 us, max = 389.721 us, min = 9.918 us, total = 7.608 ms
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 307.230 us, total = 55.301 ms, Queueing time: mean = 76.132 us, max = 225.282 us, min = 13.368 us, total = 13.704 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 1.005 ms, total = 180.968 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0139799 MiB
	total number of task attempts sent: 105
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:52:20,797 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 15942 total (8 active)
Queueing time: mean = 236.870 us, max = 10.309 ms, min = -0.000 s, total = 3.776 s
Execution time:  mean = 80.637 ms, total = 1285.523 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 6156 total (0 active), Execution time: mean = 173.862 us, total = 1.070 s, Queueing time: mean = 558.509 us, max = 10.309 ms, min = 2.673 us, total = 3.438 s
	NodeManagerService.grpc_client.RequestWorkerLease - 6156 total (0 active), Execution time: mean = 133.059 ms, total = 819.113 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 2398 total (1 active), Execution time: mean = 15.683 us, total = 37.607 ms, Queueing time: mean = 100.885 us, max = 8.073 ms, min = -0.000 s, total = 241.922 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 264 total (0 active), Execution time: mean = 926.833 us, total = 244.684 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 264 total (0 active), Execution time: mean = 23.246 us, total = 6.137 ms, Queueing time: mean = 104.166 us, max = 3.551 ms, min = 10.200 us, total = 27.500 ms
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 269.874 us, total = 64.770 ms, Queueing time: mean = 94.610 us, max = 1.013 ms, min = -0.000 s, total = 22.706 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 55 total (0 active), Execution time: mean = 122.364 ns, total = 6.730 us, Queueing time: mean = 89.007 us, max = 200.031 us, min = 36.158 us, total = 4.895 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 54 total (0 active), Execution time: mean = 366.825 us, total = 19.809 ms, Queueing time: mean = 47.289 us, max = 250.489 us, min = 20.296 us, total = 2.554 ms
	CoreWorker.SubmitTask - 54 total (0 active), Execution time: mean = 191.377 us, total = 10.334 ms, Queueing time: mean = 375.370 us, max = 3.347 ms, min = 13.805 us, total = 20.270 ms
	NodeManagerService.grpc_client.ReturnWorker - 54 total (0 active), Execution time: mean = 1.626 ms, total = 87.823 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 54 total (0 active), Execution time: mean = 24.849 us, total = 1.342 ms, Queueing time: mean = 52.102 us, max = 475.434 us, min = 15.471 us, total = 2.814 ms
	CoreWorkerService.grpc_client.PushTask - 54 total (0 active), Execution time: mean = 7.983 s, total = 431.075 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 59.346 us, total = 2.849 ms, Queueing time: mean = 123.713 us, max = 1.781 ms, min = 13.207 us, total = 5.938 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 11.509 us, total = 276.226 us, Queueing time: mean = 184.919 us, max = 1.806 ms, min = 15.855 us, total = 4.438 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 16 total (0 active), Execution time: mean = 351.204 us, total = 5.619 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 16 total (0 active), Execution time: mean = 42.350 us, total = 677.607 us, Queueing time: mean = 80.829 us, max = 297.198 us, min = 21.995 us, total = 1.293 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 534.917 us, total = 2.140 ms, Queueing time: mean = 52.620 us, max = 108.687 us, min = 48.030 us, total = 210.482 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 2 total (1 active), Execution time: mean = 5.843 ms, total = 11.686 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 2 total (0 active), Execution time: mean = 15.216 us, total = 30.431 us, Queueing time: mean = 44.868 us, max = 53.306 us, min = 36.430 us, total = 89.736 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 2 total (1 active), Execution time: mean = 16.880 s, total = 33.760 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 1 total (0 active), Execution time: mean = 79.861 us, total = 79.861 us, Queueing time: mean = 218.445 us, max = 218.445 us, min = 218.445 us, total = 218.445 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 1 total (0 active), Execution time: mean = 422.276 us, total = 422.276 us, Queueing time: mean = 30.708 us, max = 30.708 us, min = 30.708 us, total = 30.708 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 38.393 us, max = 389.721 us, min = 9.918 us, total = 27.682 ms
Execution time:  mean = 447.958 us, total = 322.978 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 40.204 us, total = 9.649 ms, Queueing time: mean = 40.010 us, max = 389.721 us, min = 9.918 us, total = 9.602 ms
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 308.576 us, total = 74.058 ms, Queueing time: mean = 75.227 us, max = 225.282 us, min = 13.368 us, total = 18.055 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 995.595 us, total = 238.943 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0208807 MiB
	total number of task attempts sent: 156
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:52:53,870 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=5ee3f7e3f9d46150aadf44e4c4321a8a15c86ea8af08e6b6653774a2
[2025-06-26 00:52:53,870 I 693801 693872] core_worker.cc:5107: Number of alive nodes:4
[2025-06-26 00:52:54,653 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=c60c555fc603cb22a64b905e730341251113eb722cf57f73c495e3fb
[2025-06-26 00:53:02,523 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet c60c555fc603cb22a64b905e730341251113eb722cf57f73c495e3fb
[2025-06-26 00:53:02,532 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 5ee3f7e3f9d46150aadf44e4c4321a8a15c86ea8af08e6b6653774a2
[2025-06-26 00:53:20,798 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 22315 total (15 active)
Queueing time: mean = 238.990 us, max = 10.309 ms, min = -0.000 s, total = 5.333 s
Execution time:  mean = 76.820 ms, total = 1714.232 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 8888 total (0 active), Execution time: mean = 176.052 us, total = 1.565 s, Queueing time: mean = 553.054 us, max = 10.309 ms, min = 2.673 us, total = 4.916 s
	NodeManagerService.grpc_client.RequestWorkerLease - 8888 total (0 active), Execution time: mean = 102.733 ms, total = 913.094 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 2997 total (1 active), Execution time: mean = 15.728 us, total = 47.138 ms, Queueing time: mean = 99.915 us, max = 8.073 ms, min = -0.000 s, total = 299.445 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 332 total (0 active), Execution time: mean = 959.007 us, total = 318.390 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 332 total (0 active), Execution time: mean = 23.809 us, total = 7.905 ms, Queueing time: mean = 105.377 us, max = 3.551 ms, min = 10.200 us, total = 34.985 ms
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 271.469 us, total = 81.441 ms, Queueing time: mean = 94.103 us, max = 1.013 ms, min = -0.000 s, total = 28.231 ms
	CoreWorkerService.grpc_client.PushTask - 72 total (7 active), Execution time: mean = 7.293 s, total = 525.075 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.SubmitTask - 72 total (0 active), Execution time: mean = 180.186 us, total = 12.973 ms, Queueing time: mean = 311.798 us, max = 3.347 ms, min = 13.805 us, total = 22.449 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 66 total (0 active), Execution time: mean = 131.621 ns, total = 8.687 us, Queueing time: mean = 104.784 us, max = 1.093 ms, min = 36.158 us, total = 6.916 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 65 total (0 active), Execution time: mean = 364.363 us, total = 23.684 ms, Queueing time: mean = 51.309 us, max = 381.394 us, min = 17.763 us, total = 3.335 ms
	NodeManagerService.grpc_client.ReturnWorker - 65 total (0 active), Execution time: mean = 1.665 ms, total = 108.232 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 65 total (0 active), Execution time: mean = 24.203 us, total = 1.573 ms, Queueing time: mean = 62.655 us, max = 856.342 us, min = 13.090 us, total = 4.073 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 57.922 us, total = 3.475 ms, Queueing time: mean = 115.354 us, max = 1.781 ms, min = 13.207 us, total = 6.921 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 11.671 us, total = 350.138 us, Queueing time: mean = 170.386 us, max = 1.806 ms, min = 15.855 us, total = 5.112 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 20 total (0 active), Execution time: mean = 353.888 us, total = 7.078 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 20 total (0 active), Execution time: mean = 41.814 us, total = 836.275 us, Queueing time: mean = 73.726 us, max = 297.198 us, min = 21.569 us, total = 1.475 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 538.880 us, total = 2.694 ms, Queueing time: mean = 58.625 us, max = 108.687 us, min = 48.030 us, total = 293.124 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 4 total (1 active), Execution time: mean = 68.465 s, total = 273.860 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 3 total (0 active), Execution time: mean = 322.059 us, total = 966.177 us, Queueing time: mean = 82.018 us, max = 136.880 us, min = 30.708 us, total = 246.054 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 3 total (0 active), Execution time: mean = 80.546 us, total = 241.638 us, Queueing time: mean = 244.712 us, max = 260.398 us, min = 218.445 us, total = 734.137 us
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 61.936 us, total = 123.872 us, Queueing time: mean = 46.781 us, max = 93.561 us, min = 93.561 us, total = 93.561 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 2 total (1 active), Execution time: mean = 5.843 ms, total = 11.686 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 2 total (0 active), Execution time: mean = 15.216 us, total = 30.431 us, Queueing time: mean = 44.868 us, max = 53.306 us, min = 36.430 us, total = 89.736 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 38.111 us, max = 613.807 us, min = 9.918 us, total = 34.338 ms
Execution time:  mean = 450.653 us, total = 406.038 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 39.042 us, total = 11.713 ms, Queueing time: mean = 38.475 us, max = 389.721 us, min = 9.918 us, total = 11.542 ms
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 310.227 us, total = 93.068 ms, Queueing time: mean = 75.902 us, max = 613.807 us, min = 13.368 us, total = 22.771 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 1.003 ms, total = 300.930 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0274477 MiB
	total number of task attempts sent: 200
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:54:20,799 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 23150 total (8 active)
Queueing time: mean = 233.083 us, max = 10.309 ms, min = -0.000 s, total = 5.396 s
Execution time:  mean = 76.961 ms, total = 1781.656 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 8888 total (0 active), Execution time: mean = 176.052 us, total = 1.565 s, Queueing time: mean = 553.054 us, max = 10.309 ms, min = 2.673 us, total = 4.916 s
	NodeManagerService.grpc_client.RequestWorkerLease - 8888 total (0 active), Execution time: mean = 102.733 ms, total = 913.094 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 3597 total (1 active), Execution time: mean = 15.679 us, total = 56.398 ms, Queueing time: mean = 97.051 us, max = 8.073 ms, min = -0.000 s, total = 349.092 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 392 total (0 active), Execution time: mean = 953.696 us, total = 373.849 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 392 total (0 active), Execution time: mean = 24.121 us, total = 9.456 ms, Queueing time: mean = 96.781 us, max = 3.551 ms, min = 10.200 us, total = 37.938 ms
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 269.241 us, total = 96.927 ms, Queueing time: mean = 97.533 us, max = 1.013 ms, min = -0.000 s, total = 35.112 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 73 total (0 active), Execution time: mean = 135.466 ns, total = 9.889 us, Queueing time: mean = 102.882 us, max = 1.093 ms, min = 36.158 us, total = 7.510 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 72 total (0 active), Execution time: mean = 363.749 us, total = 26.190 ms, Queueing time: mean = 49.967 us, max = 381.394 us, min = 17.763 us, total = 3.598 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 56.093 us, total = 4.039 ms, Queueing time: mean = 110.332 us, max = 1.781 ms, min = 13.207 us, total = 7.944 ms
	CoreWorker.SubmitTask - 72 total (0 active), Execution time: mean = 180.186 us, total = 12.973 ms, Queueing time: mean = 311.798 us, max = 3.347 ms, min = 13.805 us, total = 22.449 ms
	NodeManagerService.grpc_client.ReturnWorker - 72 total (0 active), Execution time: mean = 1.617 ms, total = 116.417 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 72 total (0 active), Execution time: mean = 24.170 us, total = 1.740 ms, Queueing time: mean = 60.684 us, max = 856.342 us, min = 13.090 us, total = 4.369 ms
	CoreWorkerService.grpc_client.PushTask - 72 total (0 active), Execution time: mean = 8.228 s, total = 592.403 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 11.868 us, total = 427.235 us, Queueing time: mean = 156.204 us, max = 1.806 ms, min = 15.855 us, total = 5.623 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 24 total (0 active), Execution time: mean = 364.489 us, total = 8.748 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 24 total (0 active), Execution time: mean = 42.626 us, total = 1.023 ms, Queueing time: mean = 72.727 us, max = 297.198 us, min = 21.569 us, total = 1.745 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 547.591 us, total = 3.286 ms, Queueing time: mean = 106.966 us, max = 348.672 us, min = 48.030 us, total = 641.796 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 4 total (1 active), Execution time: mean = 68.465 s, total = 273.860 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 3 total (0 active), Execution time: mean = 322.059 us, total = 966.177 us, Queueing time: mean = 82.018 us, max = 136.880 us, min = 30.708 us, total = 246.054 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 3 total (0 active), Execution time: mean = 80.546 us, total = 241.638 us, Queueing time: mean = 244.712 us, max = 260.398 us, min = 218.445 us, total = 734.137 us
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 61.936 us, total = 123.872 us, Queueing time: mean = 46.781 us, max = 93.561 us, min = 93.561 us, total = 93.561 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 2 total (1 active), Execution time: mean = 5.843 ms, total = 11.686 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 2 total (0 active), Execution time: mean = 15.216 us, total = 30.431 us, Queueing time: mean = 44.868 us, max = 53.306 us, min = 36.430 us, total = 89.736 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 37.692 us, max = 613.807 us, min = 9.918 us, total = 40.745 ms
Execution time:  mean = 445.459 us, total = 481.542 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 38.212 us, total = 13.756 ms, Queueing time: mean = 37.331 us, max = 389.721 us, min = 9.918 us, total = 13.439 ms
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 305.933 us, total = 110.136 ms, Queueing time: mean = 75.782 us, max = 613.807 us, min = 13.368 us, total = 27.282 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 992.559 us, total = 357.321 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0277815 MiB
	total number of task attempts sent: 207
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:54:24,612 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=5ee3f7e3f9d46150aadf44e4c4321a8a15c86ea8af08e6b6653774a2
[2025-06-26 00:54:24,612 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=5ee3f7e3f9d46150aadf44e4c4321a8a15c86ea8af08e6b6653774a2
[2025-06-26 00:54:24,612 I 693801 693872] core_worker.cc:5107: Number of alive nodes:4
[2025-06-26 00:54:24,614 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=c60c555fc603cb22a64b905e730341251113eb722cf57f73c495e3fb
[2025-06-26 00:54:24,614 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=c60c555fc603cb22a64b905e730341251113eb722cf57f73c495e3fb
[2025-06-26 00:54:24,614 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=aeb88ee76014cf968e47e7c5541354741a624a0d7183bbd80e7cc5f8
[2025-06-26 00:54:24,614 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=aeb88ee76014cf968e47e7c5541354741a624a0d7183bbd80e7cc5f8
[2025-06-26 00:54:54,153 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=107d8554c9ce898888fbcca7095612438976ed120c786ea7a3b82940
[2025-06-26 00:54:55,058 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=e90de5051af7eab045a5db8c90b1509053a14387b123a1d3416ab7b2
[2025-06-26 00:54:55,058 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=e90de5051af7eab045a5db8c90b1509053a14387b123a1d3416ab7b2
[2025-06-26 00:55:20,800 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 23969 total (8 active)
Queueing time: mean = 229.229 us, max = 10.309 ms, min = -0.000 s, total = 5.494 s
Execution time:  mean = 79.359 ms, total = 1902.147 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 8888 total (0 active), Execution time: mean = 176.052 us, total = 1.565 s, Queueing time: mean = 553.054 us, max = 10.309 ms, min = 2.673 us, total = 4.916 s
	NodeManagerService.grpc_client.RequestWorkerLease - 8888 total (0 active), Execution time: mean = 102.733 ms, total = 913.094 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 4196 total (1 active), Execution time: mean = 15.687 us, total = 65.822 ms, Queueing time: mean = 99.782 us, max = 8.073 ms, min = -0.000 s, total = 418.684 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 452 total (0 active), Execution time: mean = 949.982 us, total = 429.392 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 452 total (0 active), Execution time: mean = 24.121 us, total = 10.903 ms, Queueing time: mean = 93.393 us, max = 3.551 ms, min = 9.776 us, total = 42.214 ms
	CoreWorker.InternalHeartbeat - 420 total (1 active), Execution time: mean = 267.989 us, total = 112.556 ms, Queueing time: mean = 134.268 us, max = 6.556 ms, min = -0.000 s, total = 56.393 ms
	CoreWorker.RecordMetrics - 84 total (1 active), Execution time: mean = 55.096 us, total = 4.628 ms, Queueing time: mean = 107.555 us, max = 1.781 ms, min = 13.207 us, total = 9.035 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 73 total (0 active), Execution time: mean = 135.466 ns, total = 9.889 us, Queueing time: mean = 102.882 us, max = 1.093 ms, min = 36.158 us, total = 7.510 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 72 total (0 active), Execution time: mean = 363.749 us, total = 26.190 ms, Queueing time: mean = 49.967 us, max = 381.394 us, min = 17.763 us, total = 3.598 ms
	CoreWorker.SubmitTask - 72 total (0 active), Execution time: mean = 180.186 us, total = 12.973 ms, Queueing time: mean = 311.798 us, max = 3.347 ms, min = 13.805 us, total = 22.449 ms
	NodeManagerService.grpc_client.ReturnWorker - 72 total (0 active), Execution time: mean = 1.617 ms, total = 116.417 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 72 total (0 active), Execution time: mean = 24.170 us, total = 1.740 ms, Queueing time: mean = 60.684 us, max = 856.342 us, min = 13.090 us, total = 4.369 ms
	CoreWorkerService.grpc_client.PushTask - 72 total (0 active), Execution time: mean = 8.228 s, total = 592.403 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 42 total (1 active), Execution time: mean = 11.821 us, total = 496.481 us, Queueing time: mean = 145.828 us, max = 1.806 ms, min = 15.855 us, total = 6.125 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 28 total (0 active), Execution time: mean = 367.470 us, total = 10.289 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 28 total (0 active), Execution time: mean = 45.861 us, total = 1.284 ms, Queueing time: mean = 74.799 us, max = 297.198 us, min = 21.569 us, total = 2.094 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 8 total (1 active), Execution time: mean = 49.283 s, total = 394.265 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 106.094 us, total = 848.751 us, Queueing time: mean = 229.004 us, max = 323.159 us, min = 150.171 us, total = 1.832 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 7 total (0 active), Execution time: mean = 255.843 us, total = 1.791 ms, Queueing time: mean = 70.633 us, max = 136.880 us, min = 29.121 us, total = 494.433 us
	CoreWorker.PrintEventStats - 7 total (1 active, 1 running), Execution time: mean = 561.099 us, total = 3.928 ms, Queueing time: mean = 106.670 us, max = 348.672 us, min = 48.030 us, total = 746.691 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 61.936 us, total = 123.872 us, Queueing time: mean = 46.781 us, max = 93.561 us, min = 93.561 us, total = 93.561 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 2 total (1 active), Execution time: mean = 5.843 ms, total = 11.686 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 2 total (0 active), Execution time: mean = 15.216 us, total = 30.431 us, Queueing time: mean = 44.868 us, max = 53.306 us, min = 36.430 us, total = 89.736 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1261 total (1 active)
Queueing time: mean = 44.066 us, max = 6.908 ms, min = -0.000 s, total = 55.567 ms
Execution time:  mean = 454.765 us, total = 573.459 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 420 total (0 active), Execution time: mean = 37.713 us, total = 15.839 ms, Queueing time: mean = 36.560 us, max = 389.721 us, min = 9.918 us, total = 15.355 ms
	CoreWorker.deadline_timer.flush_task_events - 420 total (1 active), Execution time: mean = 319.758 us, total = 134.298 ms, Queueing time: mean = 95.684 us, max = 6.908 ms, min = -0.000 s, total = 40.187 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 420 total (0 active), Execution time: mean = 1.007 ms, total = 422.993 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0277815 MiB
	total number of task attempts sent: 207
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:56:20,800 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 24775 total (8 active)
Queueing time: mean = 224.340 us, max = 10.309 ms, min = -0.000 s, total = 5.558 s
Execution time:  mean = 76.780 ms, total = 1902.235 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 8888 total (0 active), Execution time: mean = 176.052 us, total = 1.565 s, Queueing time: mean = 553.054 us, max = 10.309 ms, min = 2.673 us, total = 4.916 s
	NodeManagerService.grpc_client.RequestWorkerLease - 8888 total (0 active), Execution time: mean = 102.733 ms, total = 913.094 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 4795 total (1 active), Execution time: mean = 15.712 us, total = 75.340 ms, Queueing time: mean = 98.698 us, max = 8.073 ms, min = -0.000 s, total = 473.259 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 512 total (0 active), Execution time: mean = 949.868 us, total = 486.332 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 512 total (0 active), Execution time: mean = 24.440 us, total = 12.513 ms, Queueing time: mean = 86.799 us, max = 3.551 ms, min = 9.776 us, total = 44.441 ms
	CoreWorker.InternalHeartbeat - 480 total (1 active), Execution time: mean = 267.634 us, total = 128.464 ms, Queueing time: mean = 128.240 us, max = 6.556 ms, min = -0.000 s, total = 61.555 ms
	CoreWorker.RecordMetrics - 96 total (1 active), Execution time: mean = 53.480 us, total = 5.134 ms, Queueing time: mean = 105.236 us, max = 1.781 ms, min = 13.207 us, total = 10.103 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 73 total (0 active), Execution time: mean = 135.466 ns, total = 9.889 us, Queueing time: mean = 102.882 us, max = 1.093 ms, min = 36.158 us, total = 7.510 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 72 total (0 active), Execution time: mean = 363.749 us, total = 26.190 ms, Queueing time: mean = 49.967 us, max = 381.394 us, min = 17.763 us, total = 3.598 ms
	CoreWorker.SubmitTask - 72 total (0 active), Execution time: mean = 180.186 us, total = 12.973 ms, Queueing time: mean = 311.798 us, max = 3.347 ms, min = 13.805 us, total = 22.449 ms
	NodeManagerService.grpc_client.ReturnWorker - 72 total (0 active), Execution time: mean = 1.617 ms, total = 116.417 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 72 total (0 active), Execution time: mean = 24.170 us, total = 1.740 ms, Queueing time: mean = 60.684 us, max = 856.342 us, min = 13.090 us, total = 4.369 ms
	CoreWorkerService.grpc_client.PushTask - 72 total (0 active), Execution time: mean = 8.228 s, total = 592.403 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 48 total (1 active), Execution time: mean = 14.068 us, total = 675.254 us, Queueing time: mean = 135.341 us, max = 1.806 ms, min = 15.855 us, total = 6.496 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 32 total (0 active), Execution time: mean = 389.138 us, total = 12.452 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 32 total (0 active), Execution time: mean = 45.638 us, total = 1.460 ms, Queueing time: mean = 70.489 us, max = 297.198 us, min = 21.569 us, total = 2.256 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 8 total (1 active), Execution time: mean = 49.283 s, total = 394.265 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 106.094 us, total = 848.751 us, Queueing time: mean = 229.004 us, max = 323.159 us, min = 150.171 us, total = 1.832 ms
	CoreWorker.PrintEventStats - 8 total (1 active, 1 running), Execution time: mean = 564.369 us, total = 4.515 ms, Queueing time: mean = 101.736 us, max = 348.672 us, min = 48.030 us, total = 813.887 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 7 total (0 active), Execution time: mean = 255.843 us, total = 1.791 ms, Queueing time: mean = 70.633 us, max = 136.880 us, min = 29.121 us, total = 494.433 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 61.936 us, total = 123.872 us, Queueing time: mean = 46.781 us, max = 93.561 us, min = 93.561 us, total = 93.561 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 2 total (1 active), Execution time: mean = 5.843 ms, total = 11.686 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 2 total (0 active), Execution time: mean = 15.216 us, total = 30.431 us, Queueing time: mean = 44.868 us, max = 53.306 us, min = 36.430 us, total = 89.736 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1441 total (1 active)
Queueing time: mean = 44.207 us, max = 6.908 ms, min = -0.000 s, total = 63.702 ms
Execution time:  mean = 449.264 us, total = 647.389 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 480 total (0 active), Execution time: mean = 37.497 us, total = 17.999 ms, Queueing time: mean = 36.575 us, max = 389.721 us, min = 9.918 us, total = 17.556 ms
	CoreWorker.deadline_timer.flush_task_events - 480 total (1 active), Execution time: mean = 312.006 us, total = 149.763 ms, Queueing time: mean = 96.087 us, max = 6.908 ms, min = -0.000 s, total = 46.122 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 480 total (0 active), Execution time: mean = 998.541 us, total = 479.300 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0277815 MiB
	total number of task attempts sent: 207
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:57:20,801 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 25582 total (8 active)
Queueing time: mean = 219.568 us, max = 10.309 ms, min = -0.000 s, total = 5.617 s
Execution time:  mean = 74.362 ms, total = 1902.317 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 8888 total (0 active), Execution time: mean = 176.052 us, total = 1.565 s, Queueing time: mean = 553.054 us, max = 10.309 ms, min = 2.673 us, total = 4.916 s
	NodeManagerService.grpc_client.RequestWorkerLease - 8888 total (0 active), Execution time: mean = 102.733 ms, total = 913.094 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 5395 total (1 active), Execution time: mean = 15.684 us, total = 84.614 ms, Queueing time: mean = 96.425 us, max = 8.073 ms, min = -0.000 s, total = 520.214 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 572 total (0 active), Execution time: mean = 945.071 us, total = 540.581 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 572 total (0 active), Execution time: mean = 24.342 us, total = 13.924 ms, Queueing time: mean = 81.995 us, max = 3.551 ms, min = 9.776 us, total = 46.901 ms
	CoreWorker.InternalHeartbeat - 540 total (1 active), Execution time: mean = 263.825 us, total = 142.465 ms, Queueing time: mean = 128.541 us, max = 6.556 ms, min = -0.000 s, total = 69.412 ms
	CoreWorker.RecordMetrics - 108 total (1 active), Execution time: mean = 52.032 us, total = 5.619 ms, Queueing time: mean = 103.758 us, max = 1.781 ms, min = 13.207 us, total = 11.206 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 73 total (0 active), Execution time: mean = 135.466 ns, total = 9.889 us, Queueing time: mean = 102.882 us, max = 1.093 ms, min = 36.158 us, total = 7.510 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 72 total (0 active), Execution time: mean = 363.749 us, total = 26.190 ms, Queueing time: mean = 49.967 us, max = 381.394 us, min = 17.763 us, total = 3.598 ms
	CoreWorker.SubmitTask - 72 total (0 active), Execution time: mean = 180.186 us, total = 12.973 ms, Queueing time: mean = 311.798 us, max = 3.347 ms, min = 13.805 us, total = 22.449 ms
	NodeManagerService.grpc_client.ReturnWorker - 72 total (0 active), Execution time: mean = 1.617 ms, total = 116.417 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 72 total (0 active), Execution time: mean = 24.170 us, total = 1.740 ms, Queueing time: mean = 60.684 us, max = 856.342 us, min = 13.090 us, total = 4.369 ms
	CoreWorkerService.grpc_client.PushTask - 72 total (0 active), Execution time: mean = 8.228 s, total = 592.403 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 54 total (1 active), Execution time: mean = 13.898 us, total = 750.511 us, Queueing time: mean = 128.120 us, max = 1.806 ms, min = -0.000 s, total = 6.918 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 36 total (0 active), Execution time: mean = 379.302 us, total = 13.655 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 36 total (0 active), Execution time: mean = 47.473 us, total = 1.709 ms, Queueing time: mean = 66.261 us, max = 297.198 us, min = 21.569 us, total = 2.385 ms
	CoreWorker.PrintEventStats - 9 total (1 active, 1 running), Execution time: mean = 579.776 us, total = 5.218 ms, Queueing time: mean = 94.920 us, max = 348.672 us, min = 40.395 us, total = 854.282 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 8 total (1 active), Execution time: mean = 49.283 s, total = 394.265 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 106.094 us, total = 848.751 us, Queueing time: mean = 229.004 us, max = 323.159 us, min = 150.171 us, total = 1.832 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 7 total (0 active), Execution time: mean = 255.843 us, total = 1.791 ms, Queueing time: mean = 70.633 us, max = 136.880 us, min = 29.121 us, total = 494.433 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 61.936 us, total = 123.872 us, Queueing time: mean = 46.781 us, max = 93.561 us, min = 93.561 us, total = 93.561 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 2 total (1 active), Execution time: mean = 5.843 ms, total = 11.686 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 2 total (0 active), Execution time: mean = 15.216 us, total = 30.431 us, Queueing time: mean = 44.868 us, max = 53.306 us, min = 36.430 us, total = 89.736 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1621 total (1 active)
Queueing time: mean = 43.923 us, max = 6.908 ms, min = -0.000 s, total = 71.199 ms
Execution time:  mean = 445.715 us, total = 722.504 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 540 total (0 active), Execution time: mean = 37.557 us, total = 20.281 ms, Queueing time: mean = 36.768 us, max = 389.721 us, min = 9.918 us, total = 19.855 ms
	CoreWorker.deadline_timer.flush_task_events - 540 total (1 active), Execution time: mean = 308.686 us, total = 166.690 ms, Queueing time: mean = 95.036 us, max = 6.908 ms, min = -0.000 s, total = 51.320 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 540 total (0 active), Execution time: mean = 991.119 us, total = 535.204 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0277815 MiB
	total number of task attempts sent: 207
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:58:07,051 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 107d8554c9ce898888fbcca7095612438976ed120c786ea7a3b82940
[2025-06-26 00:58:20,802 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 26477 total (20 active)
Queueing time: mean = 214.649 us, max = 10.309 ms, min = -0.000 s, total = 5.683 s
Execution time:  mean = 96.072 ms, total = 2543.696 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease - 8916 total (10 active), Execution time: mean = 105.036 ms, total = 936.498 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 8906 total (0 active), Execution time: mean = 176.664 us, total = 1.573 s, Queueing time: mean = 552.198 us, max = 10.309 ms, min = 2.673 us, total = 4.918 s
	CoreWorker.RecoverObjects - 5994 total (1 active), Execution time: mean = 15.819 us, total = 94.818 ms, Queueing time: mean = 95.220 us, max = 8.073 ms, min = -0.000 s, total = 570.748 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 637 total (0 active), Execution time: mean = 943.412 us, total = 600.954 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 637 total (0 active), Execution time: mean = 24.263 us, total = 15.455 ms, Queueing time: mean = 77.212 us, max = 3.551 ms, min = 9.776 us, total = 49.184 ms
	CoreWorker.InternalHeartbeat - 600 total (1 active), Execution time: mean = 264.358 us, total = 158.615 ms, Queueing time: mean = 125.932 us, max = 6.556 ms, min = -0.000 s, total = 75.559 ms
	CoreWorker.RecordMetrics - 120 total (1 active), Execution time: mean = 50.961 us, total = 6.115 ms, Queueing time: mean = 102.447 us, max = 1.781 ms, min = 13.207 us, total = 12.294 ms
	CoreWorker.SubmitTask - 90 total (0 active), Execution time: mean = 179.163 us, total = 16.125 ms, Queueing time: mean = 276.300 us, max = 3.347 ms, min = 13.805 us, total = 24.867 ms
	CoreWorkerService.grpc_client.PushTask - 76 total (2 active), Execution time: mean = 8.073 s, total = 613.538 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerMemoryStore.Put.get_async_callbacks - 75 total (0 active), Execution time: mean = 135.200 ns, total = 10.140 us, Queueing time: mean = 102.454 us, max = 1.093 ms, min = 36.158 us, total = 7.684 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 74 total (0 active), Execution time: mean = 363.552 us, total = 26.903 ms, Queueing time: mean = 49.459 us, max = 381.394 us, min = 17.763 us, total = 3.660 ms
	NodeManagerService.grpc_client.ReturnWorker - 74 total (0 active), Execution time: mean = 1.662 ms, total = 122.973 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 74 total (0 active), Execution time: mean = 24.481 us, total = 1.812 ms, Queueing time: mean = 66.165 us, max = 856.342 us, min = 13.090 us, total = 4.896 ms
	CoreWorker.TryDelPendingObjectRefStreams - 60 total (1 active), Execution time: mean = 13.651 us, total = 819.050 us, Queueing time: mean = 123.169 us, max = 1.806 ms, min = -0.000 s, total = 7.390 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 40 total (0 active), Execution time: mean = 46.101 us, total = 1.844 ms, Queueing time: mean = 61.934 us, max = 297.198 us, min = 16.887 us, total = 2.477 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 40 total (0 active), Execution time: mean = 375.547 us, total = 15.022 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 10 total (1 active, 1 running), Execution time: mean = 598.059 us, total = 5.981 ms, Queueing time: mean = 92.147 us, max = 348.672 us, min = 40.395 us, total = 921.472 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 106.094 us, total = 848.751 us, Queueing time: mean = 229.004 us, max = 323.159 us, min = 150.171 us, total = 1.832 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 8 total (1 active), Execution time: mean = 49.283 s, total = 394.265 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 7 total (0 active), Execution time: mean = 255.843 us, total = 1.791 ms, Queueing time: mean = 70.633 us, max = 136.880 us, min = 29.121 us, total = 494.433 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 3 total (1 active), Execution time: mean = 198.914 s, total = 596.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 53.915 us, total = 161.745 us, Queueing time: mean = 36.838 us, max = 93.561 us, min = 16.954 us, total = 110.515 us
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 3 total (0 active), Execution time: mean = 16.414 us, total = 49.242 us, Queueing time: mean = 48.201 us, max = 54.867 us, min = 36.430 us, total = 144.603 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1801 total (1 active)
Queueing time: mean = 43.608 us, max = 6.908 ms, min = -0.000 s, total = 78.539 ms
Execution time:  mean = 442.777 us, total = 797.441 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 600 total (0 active), Execution time: mean = 37.415 us, total = 22.449 ms, Queueing time: mean = 36.376 us, max = 389.721 us, min = 9.715 us, total = 21.826 ms
	CoreWorker.deadline_timer.flush_task_events - 600 total (1 active), Execution time: mean = 306.364 us, total = 183.818 ms, Queueing time: mean = 94.481 us, max = 6.908 ms, min = -0.000 s, total = 56.689 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 600 total (0 active), Execution time: mean = 984.743 us, total = 590.846 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0325193 MiB
	total number of task attempts sent: 230
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:58:32,372 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=6d647dce398845972a47ce100b2b655a442fcea0c1e67051f484861f
[2025-06-26 00:58:32,372 I 693801 693872] core_worker.cc:5107: Number of alive nodes:3
[2025-06-26 00:58:32,375 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 6d647dce398845972a47ce100b2b655a442fcea0c1e67051f484861f
[2025-06-26 00:58:35,670 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=6985e19ea27e856c07dc8ab917ef47ca7ac856fdd8daa6e3682941a9
[2025-06-26 00:58:35,673 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 6985e19ea27e856c07dc8ab917ef47ca7ac856fdd8daa6e3682941a9
[2025-06-26 00:58:37,214 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=f91843d4faa36d81732f614a02a8f8db1a8afd5e9f428b1d4271e85a
[2025-06-26 00:58:37,218 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet f91843d4faa36d81732f614a02a8f8db1a8afd5e9f428b1d4271e85a
[2025-06-26 00:59:20,803 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 29197 total (8 active)
Queueing time: mean = 207.526 us, max = 10.309 ms, min = -0.000 s, total = 6.059 s
Execution time:  mean = 109.663 ms, total = 3201.820 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 9820 total (0 active), Execution time: mean = 176.625 us, total = 1.734 s, Queueing time: mean = 531.282 us, max = 10.309 ms, min = 2.673 us, total = 5.217 s
	NodeManagerService.grpc_client.RequestWorkerLease - 9820 total (0 active), Execution time: mean = 125.320 ms, total = 1230.639 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 6594 total (1 active), Execution time: mean = 15.787 us, total = 104.101 ms, Queueing time: mean = 95.926 us, max = 8.073 ms, min = -0.000 s, total = 632.534 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 701 total (0 active), Execution time: mean = 941.560 us, total = 660.034 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 701 total (0 active), Execution time: mean = 24.259 us, total = 17.005 ms, Queueing time: mean = 74.225 us, max = 3.551 ms, min = 9.776 us, total = 52.032 ms
	CoreWorker.InternalHeartbeat - 660 total (1 active), Execution time: mean = 265.874 us, total = 175.477 ms, Queueing time: mean = 122.914 us, max = 6.556 ms, min = -0.000 s, total = 81.123 ms
	CoreWorker.RecordMetrics - 132 total (1 active), Execution time: mean = 51.970 us, total = 6.860 ms, Queueing time: mean = 101.132 us, max = 1.781 ms, min = 13.207 us, total = 13.349 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 91 total (0 active), Execution time: mean = 127.868 ns, total = 11.636 us, Queueing time: mean = 102.117 us, max = 1.093 ms, min = 36.158 us, total = 9.293 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 90 total (0 active), Execution time: mean = 362.633 us, total = 32.637 ms, Queueing time: mean = 53.895 us, max = 578.210 us, min = 17.763 us, total = 4.851 ms
	CoreWorker.SubmitTask - 90 total (0 active), Execution time: mean = 179.163 us, total = 16.125 ms, Queueing time: mean = 276.300 us, max = 3.347 ms, min = 13.805 us, total = 24.867 ms
	NodeManagerService.grpc_client.ReturnWorker - 90 total (0 active), Execution time: mean = 1.607 ms, total = 144.637 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 90 total (0 active), Execution time: mean = 25.382 us, total = 2.284 ms, Queueing time: mean = 60.783 us, max = 856.342 us, min = 13.090 us, total = 5.470 ms
	CoreWorkerService.grpc_client.PushTask - 90 total (0 active), Execution time: mean = 8.390 s, total = 755.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 66 total (1 active), Execution time: mean = 13.518 us, total = 892.172 us, Queueing time: mean = 119.458 us, max = 1.806 ms, min = -0.000 s, total = 7.884 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 44 total (0 active), Execution time: mean = 376.970 us, total = 16.587 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 44 total (0 active), Execution time: mean = 45.136 us, total = 1.986 ms, Queueing time: mean = 64.736 us, max = 297.198 us, min = 16.887 us, total = 2.848 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 11 total (1 active), Execution time: mean = 56.038 s, total = 616.421 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 11 total (0 active), Execution time: mean = 95.676 us, total = 1.052 ms, Queueing time: mean = 227.724 us, max = 323.159 us, min = 150.171 us, total = 2.505 ms
	CoreWorker.PrintEventStats - 11 total (1 active, 1 running), Execution time: mean = 593.525 us, total = 6.529 ms, Queueing time: mean = 96.697 us, max = 348.672 us, min = 40.395 us, total = 1.064 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 10 total (0 active), Execution time: mean = 250.068 us, total = 2.501 ms, Queueing time: mean = 73.194 us, max = 157.093 us, min = 21.940 us, total = 731.936 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 53.915 us, total = 161.745 us, Queueing time: mean = 36.838 us, max = 93.561 us, min = 16.954 us, total = 110.515 us
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling - 3 total (1 active), Execution time: mean = 198.914 s, total = 596.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 3 total (0 active), Execution time: mean = 16.414 us, total = 49.242 us, Queueing time: mean = 48.201 us, max = 54.867 us, min = 36.430 us, total = 144.603 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1981 total (1 active)
Queueing time: mean = 43.329 us, max = 6.908 ms, min = -0.000 s, total = 85.835 ms
Execution time:  mean = 441.449 us, total = 874.510 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 660 total (0 active), Execution time: mean = 37.397 us, total = 24.682 ms, Queueing time: mean = 35.986 us, max = 389.721 us, min = 9.715 us, total = 23.751 ms
	CoreWorker.deadline_timer.flush_task_events - 660 total (1 active), Execution time: mean = 305.120 us, total = 201.379 ms, Queueing time: mean = 94.030 us, max = 6.908 ms, min = -0.000 s, total = 62.060 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 660 total (0 active), Execution time: mean = 982.001 us, total = 648.120 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0347509 MiB
	total number of task attempts sent: 260
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 00:59:49,309 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=6d647dce398845972a47ce100b2b655a442fcea0c1e67051f484861f
[2025-06-26 00:59:49,309 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=6d647dce398845972a47ce100b2b655a442fcea0c1e67051f484861f
[2025-06-26 00:59:49,309 I 693801 693872] core_worker.cc:5107: Number of alive nodes:4
[2025-06-26 00:59:54,438 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=f91843d4faa36d81732f614a02a8f8db1a8afd5e9f428b1d4271e85a
[2025-06-26 00:59:54,438 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=f91843d4faa36d81732f614a02a8f8db1a8afd5e9f428b1d4271e85a
[2025-06-26 00:59:54,441 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=6985e19ea27e856c07dc8ab917ef47ca7ac856fdd8daa6e3682941a9
[2025-06-26 00:59:54,441 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=6985e19ea27e856c07dc8ab917ef47ca7ac856fdd8daa6e3682941a9
[2025-06-26 01:00:20,601 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=71f196cf3e35c23d0086046ebee4d8b57afb729e123bf51e32afab43
[2025-06-26 01:00:20,803 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 30014 total (8 active)
Queueing time: mean = 206.655 us, max = 10.340 ms, min = -0.000 s, total = 6.203 s
Execution time:  mean = 110.127 ms, total = 3305.360 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 9820 total (0 active), Execution time: mean = 176.625 us, total = 1.734 s, Queueing time: mean = 531.282 us, max = 10.309 ms, min = 2.673 us, total = 5.217 s
	NodeManagerService.grpc_client.RequestWorkerLease - 9820 total (0 active), Execution time: mean = 125.320 ms, total = 1230.639 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 7192 total (1 active), Execution time: mean = 15.798 us, total = 113.619 ms, Queueing time: mean = 104.413 us, max = 10.340 ms, min = -0.000 s, total = 750.936 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 761 total (0 active), Execution time: mean = 1.025 ms, total = 780.146 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 761 total (0 active), Execution time: mean = 24.575 us, total = 18.702 ms, Queueing time: mean = 73.790 us, max = 3.551 ms, min = 9.776 us, total = 56.154 ms
	CoreWorker.InternalHeartbeat - 720 total (1 active), Execution time: mean = 267.186 us, total = 192.374 ms, Queueing time: mean = 136.809 us, max = 7.858 ms, min = -0.000 s, total = 98.502 ms
	CoreWorker.RecordMetrics - 144 total (1 active), Execution time: mean = 50.997 us, total = 7.344 ms, Queueing time: mean = 100.823 us, max = 1.781 ms, min = 13.207 us, total = 14.519 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 91 total (0 active), Execution time: mean = 127.868 ns, total = 11.636 us, Queueing time: mean = 102.117 us, max = 1.093 ms, min = 36.158 us, total = 9.293 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 90 total (0 active), Execution time: mean = 362.633 us, total = 32.637 ms, Queueing time: mean = 53.895 us, max = 578.210 us, min = 17.763 us, total = 4.851 ms
	CoreWorker.SubmitTask - 90 total (0 active), Execution time: mean = 179.163 us, total = 16.125 ms, Queueing time: mean = 276.300 us, max = 3.347 ms, min = 13.805 us, total = 24.867 ms
	NodeManagerService.grpc_client.ReturnWorker - 90 total (0 active), Execution time: mean = 1.607 ms, total = 144.637 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 90 total (0 active), Execution time: mean = 25.382 us, total = 2.284 ms, Queueing time: mean = 60.783 us, max = 856.342 us, min = 13.090 us, total = 5.470 ms
	CoreWorkerService.grpc_client.PushTask - 90 total (0 active), Execution time: mean = 8.390 s, total = 755.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 72 total (1 active), Execution time: mean = 13.328 us, total = 959.607 us, Queueing time: mean = 115.997 us, max = 1.806 ms, min = -0.000 s, total = 8.352 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 48 total (0 active), Execution time: mean = 424.101 us, total = 20.357 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 48 total (0 active), Execution time: mean = 44.516 us, total = 2.137 ms, Queueing time: mean = 62.227 us, max = 297.198 us, min = 16.887 us, total = 2.987 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 15 total (1 active), Execution time: mean = 47.987 s, total = 719.806 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 15 total (0 active), Execution time: mean = 103.560 us, total = 1.553 ms, Queueing time: mean = 231.999 us, max = 332.644 us, min = 150.171 us, total = 3.480 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 14 total (0 active), Execution time: mean = 255.006 us, total = 3.570 ms, Queueing time: mean = 101.809 us, max = 592.878 us, min = 21.940 us, total = 1.425 ms
	CoreWorker.PrintEventStats - 12 total (1 active, 1 running), Execution time: mean = 595.999 us, total = 7.152 ms, Queueing time: mean = 93.422 us, max = 348.672 us, min = 40.395 us, total = 1.121 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 53.915 us, total = 161.745 us, Queueing time: mean = 36.838 us, max = 93.561 us, min = 16.954 us, total = 110.515 us
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling - 3 total (1 active), Execution time: mean = 198.914 s, total = 596.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 3 total (0 active), Execution time: mean = 16.414 us, total = 49.242 us, Queueing time: mean = 48.201 us, max = 54.867 us, min = 36.430 us, total = 144.603 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2161 total (1 active)
Queueing time: mean = 46.785 us, max = 6.908 ms, min = -0.000 s, total = 101.102 ms
Execution time:  mean = 451.894 us, total = 976.543 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 720 total (0 active), Execution time: mean = 37.523 us, total = 27.016 ms, Queueing time: mean = 37.722 us, max = 730.783 us, min = 9.715 us, total = 27.160 ms
	CoreWorker.deadline_timer.flush_task_events - 720 total (1 active), Execution time: mean = 310.334 us, total = 223.441 ms, Queueing time: mean = 102.664 us, max = 6.908 ms, min = -0.000 s, total = 73.918 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 720 total (0 active), Execution time: mean = 1.008 ms, total = 725.758 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0347509 MiB
	total number of task attempts sent: 260
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:00:25,133 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=107d8554c9ce898888fbcca7095612438976ed120c786ea7a3b82940
[2025-06-26 01:00:25,133 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=107d8554c9ce898888fbcca7095612438976ed120c786ea7a3b82940
[2025-06-26 01:01:20,804 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 30823 total (8 active)
Queueing time: mean = 204.192 us, max = 10.340 ms, min = -0.002 s, total = 6.294 s
Execution time:  mean = 107.388 ms, total = 3310.013 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 9820 total (0 active), Execution time: mean = 176.625 us, total = 1.734 s, Queueing time: mean = 531.282 us, max = 10.309 ms, min = 2.673 us, total = 5.217 s
	NodeManagerService.grpc_client.RequestWorkerLease - 9820 total (0 active), Execution time: mean = 125.320 ms, total = 1230.639 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 7791 total (1 active), Execution time: mean = 16.242 us, total = 126.539 ms, Queueing time: mean = 106.498 us, max = 10.340 ms, min = -0.002 s, total = 829.725 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 821 total (0 active), Execution time: mean = 1.055 ms, total = 866.095 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 821 total (0 active), Execution time: mean = 25.070 us, total = 20.583 ms, Queueing time: mean = 71.921 us, max = 3.551 ms, min = 9.776 us, total = 59.047 ms
	CoreWorker.InternalHeartbeat - 780 total (1 active), Execution time: mean = 268.088 us, total = 209.109 ms, Queueing time: mean = 135.472 us, max = 7.858 ms, min = -0.000 s, total = 105.668 ms
	CoreWorker.RecordMetrics - 156 total (1 active), Execution time: mean = 50.343 us, total = 7.853 ms, Queueing time: mean = 100.943 us, max = 1.781 ms, min = 13.207 us, total = 15.747 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 91 total (0 active), Execution time: mean = 127.868 ns, total = 11.636 us, Queueing time: mean = 102.117 us, max = 1.093 ms, min = 36.158 us, total = 9.293 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 90 total (0 active), Execution time: mean = 362.633 us, total = 32.637 ms, Queueing time: mean = 53.895 us, max = 578.210 us, min = 17.763 us, total = 4.851 ms
	CoreWorker.SubmitTask - 90 total (0 active), Execution time: mean = 179.163 us, total = 16.125 ms, Queueing time: mean = 276.300 us, max = 3.347 ms, min = 13.805 us, total = 24.867 ms
	NodeManagerService.grpc_client.ReturnWorker - 90 total (0 active), Execution time: mean = 1.607 ms, total = 144.637 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 90 total (0 active), Execution time: mean = 25.382 us, total = 2.284 ms, Queueing time: mean = 60.783 us, max = 856.342 us, min = 13.090 us, total = 5.470 ms
	CoreWorkerService.grpc_client.PushTask - 90 total (0 active), Execution time: mean = 8.390 s, total = 755.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 78 total (1 active), Execution time: mean = 13.180 us, total = 1.028 ms, Queueing time: mean = 114.486 us, max = 1.806 ms, min = -0.000 s, total = 8.930 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 52 total (0 active), Execution time: mean = 423.734 us, total = 22.034 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 52 total (0 active), Execution time: mean = 44.294 us, total = 2.303 ms, Queueing time: mean = 61.595 us, max = 297.198 us, min = 16.887 us, total = 3.203 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 16 total (1 active), Execution time: mean = 45.271 s, total = 724.338 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 16 total (0 active), Execution time: mean = 103.768 us, total = 1.660 ms, Queueing time: mean = 230.435 us, max = 332.644 us, min = 150.171 us, total = 3.687 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 15 total (0 active), Execution time: mean = 254.027 us, total = 3.810 ms, Queueing time: mean = 97.441 us, max = 592.878 us, min = 21.940 us, total = 1.462 ms
	CoreWorker.PrintEventStats - 13 total (1 active, 1 running), Execution time: mean = 591.626 us, total = 7.691 ms, Queueing time: mean = 99.144 us, max = 348.672 us, min = 40.395 us, total = 1.289 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 53.915 us, total = 161.745 us, Queueing time: mean = 36.838 us, max = 93.561 us, min = 16.954 us, total = 110.515 us
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling - 3 total (1 active), Execution time: mean = 198.914 s, total = 596.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 3 total (0 active), Execution time: mean = 16.414 us, total = 49.242 us, Queueing time: mean = 48.201 us, max = 54.867 us, min = 36.430 us, total = 144.603 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2341 total (1 active)
Queueing time: mean = 54.203 us, max = 11.213 ms, min = -0.000 s, total = 126.888 ms
Execution time:  mean = 472.005 us, total = 1.105 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 780 total (0 active), Execution time: mean = 38.165 us, total = 29.769 ms, Queueing time: mean = 59.613 us, max = 11.213 ms, min = 9.715 us, total = 46.498 ms
	CoreWorker.deadline_timer.flush_task_events - 780 total (1 active), Execution time: mean = 327.064 us, total = 255.110 ms, Queueing time: mean = 103.033 us, max = 6.908 ms, min = -0.000 s, total = 80.366 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 780 total (0 active), Execution time: mean = 1.051 ms, total = 819.756 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0347509 MiB
	total number of task attempts sent: 260
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:02:20,805 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 31630 total (8 active)
Queueing time: mean = 200.965 us, max = 10.340 ms, min = -0.002 s, total = 6.357 s
Execution time:  mean = 104.651 ms, total = 3310.104 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 9820 total (0 active), Execution time: mean = 176.625 us, total = 1.734 s, Queueing time: mean = 531.282 us, max = 10.309 ms, min = 2.673 us, total = 5.217 s
	NodeManagerService.grpc_client.RequestWorkerLease - 9820 total (0 active), Execution time: mean = 125.320 ms, total = 1230.639 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 8391 total (1 active), Execution time: mean = 16.165 us, total = 135.639 ms, Queueing time: mean = 105.150 us, max = 10.340 ms, min = -0.002 s, total = 882.318 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 881 total (0 active), Execution time: mean = 1.051 ms, total = 925.893 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 881 total (0 active), Execution time: mean = 25.322 us, total = 22.308 ms, Queueing time: mean = 69.314 us, max = 3.551 ms, min = 9.776 us, total = 61.066 ms
	CoreWorker.InternalHeartbeat - 840 total (1 active), Execution time: mean = 269.030 us, total = 225.986 ms, Queueing time: mean = 132.364 us, max = 7.858 ms, min = -0.000 s, total = 111.186 ms
	CoreWorker.RecordMetrics - 168 total (1 active), Execution time: mean = 49.891 us, total = 8.382 ms, Queueing time: mean = 102.355 us, max = 1.781 ms, min = 13.207 us, total = 17.196 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 91 total (0 active), Execution time: mean = 127.868 ns, total = 11.636 us, Queueing time: mean = 102.117 us, max = 1.093 ms, min = 36.158 us, total = 9.293 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 90 total (0 active), Execution time: mean = 362.633 us, total = 32.637 ms, Queueing time: mean = 53.895 us, max = 578.210 us, min = 17.763 us, total = 4.851 ms
	CoreWorker.SubmitTask - 90 total (0 active), Execution time: mean = 179.163 us, total = 16.125 ms, Queueing time: mean = 276.300 us, max = 3.347 ms, min = 13.805 us, total = 24.867 ms
	NodeManagerService.grpc_client.ReturnWorker - 90 total (0 active), Execution time: mean = 1.607 ms, total = 144.637 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 90 total (0 active), Execution time: mean = 25.382 us, total = 2.284 ms, Queueing time: mean = 60.783 us, max = 856.342 us, min = 13.090 us, total = 5.470 ms
	CoreWorkerService.grpc_client.PushTask - 90 total (0 active), Execution time: mean = 8.390 s, total = 755.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 84 total (1 active), Execution time: mean = 13.133 us, total = 1.103 ms, Queueing time: mean = 112.224 us, max = 1.806 ms, min = -0.000 s, total = 9.427 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 56 total (0 active), Execution time: mean = 423.212 us, total = 23.700 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 56 total (0 active), Execution time: mean = 44.205 us, total = 2.475 ms, Queueing time: mean = 66.674 us, max = 342.485 us, min = 16.887 us, total = 3.734 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 16 total (1 active), Execution time: mean = 45.271 s, total = 724.338 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 16 total (0 active), Execution time: mean = 103.768 us, total = 1.660 ms, Queueing time: mean = 230.435 us, max = 332.644 us, min = 150.171 us, total = 3.687 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 15 total (0 active), Execution time: mean = 254.027 us, total = 3.810 ms, Queueing time: mean = 97.441 us, max = 592.878 us, min = 21.940 us, total = 1.462 ms
	CoreWorker.PrintEventStats - 14 total (1 active, 1 running), Execution time: mean = 585.809 us, total = 8.201 ms, Queueing time: mean = 100.017 us, max = 348.672 us, min = 40.395 us, total = 1.400 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 53.915 us, total = 161.745 us, Queueing time: mean = 36.838 us, max = 93.561 us, min = 16.954 us, total = 110.515 us
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling - 3 total (1 active), Execution time: mean = 198.914 s, total = 596.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 3 total (0 active), Execution time: mean = 16.414 us, total = 49.242 us, Queueing time: mean = 48.201 us, max = 54.867 us, min = 36.430 us, total = 144.603 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2521 total (1 active)
Queueing time: mean = 53.686 us, max = 11.213 ms, min = -0.000 s, total = 135.342 ms
Execution time:  mean = 470.297 us, total = 1.186 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 840 total (0 active), Execution time: mean = 38.279 us, total = 32.154 ms, Queueing time: mean = 57.308 us, max = 11.213 ms, min = 9.715 us, total = 48.139 ms
	CoreWorker.deadline_timer.flush_task_events - 840 total (1 active), Execution time: mean = 326.496 us, total = 274.257 ms, Queueing time: mean = 103.784 us, max = 6.908 ms, min = -0.000 s, total = 87.179 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 840 total (0 active), Execution time: mean = 1.046 ms, total = 878.879 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0347509 MiB
	total number of task attempts sent: 260
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:03:20,805 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 32437 total (8 active)
Queueing time: mean = 197.819 us, max = 10.340 ms, min = -0.002 s, total = 6.417 s
Execution time:  mean = 102.050 ms, total = 3310.192 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 9820 total (0 active), Execution time: mean = 176.625 us, total = 1.734 s, Queueing time: mean = 531.282 us, max = 10.309 ms, min = 2.673 us, total = 5.217 s
	NodeManagerService.grpc_client.RequestWorkerLease - 9820 total (0 active), Execution time: mean = 125.320 ms, total = 1230.639 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 8990 total (1 active), Execution time: mean = 16.114 us, total = 144.864 ms, Queueing time: mean = 103.554 us, max = 10.340 ms, min = -0.002 s, total = 930.948 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 941 total (0 active), Execution time: mean = 1.047 ms, total = 985.301 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 941 total (0 active), Execution time: mean = 25.621 us, total = 24.109 ms, Queueing time: mean = 67.701 us, max = 3.551 ms, min = 9.776 us, total = 63.706 ms
	CoreWorker.InternalHeartbeat - 900 total (1 active), Execution time: mean = 268.599 us, total = 241.739 ms, Queueing time: mean = 130.127 us, max = 7.858 ms, min = -0.000 s, total = 117.114 ms
	CoreWorker.RecordMetrics - 180 total (1 active), Execution time: mean = 49.541 us, total = 8.917 ms, Queueing time: mean = 100.537 us, max = 1.781 ms, min = 13.207 us, total = 18.097 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 91 total (0 active), Execution time: mean = 127.868 ns, total = 11.636 us, Queueing time: mean = 102.117 us, max = 1.093 ms, min = 36.158 us, total = 9.293 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 90 total (0 active), Execution time: mean = 362.633 us, total = 32.637 ms, Queueing time: mean = 53.895 us, max = 578.210 us, min = 17.763 us, total = 4.851 ms
	CoreWorker.SubmitTask - 90 total (0 active), Execution time: mean = 179.163 us, total = 16.125 ms, Queueing time: mean = 276.300 us, max = 3.347 ms, min = 13.805 us, total = 24.867 ms
	NodeManagerService.grpc_client.ReturnWorker - 90 total (0 active), Execution time: mean = 1.607 ms, total = 144.637 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 90 total (1 active), Execution time: mean = 12.998 us, total = 1.170 ms, Queueing time: mean = 123.710 us, max = 1.806 ms, min = -0.000 s, total = 11.134 ms
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 90 total (0 active), Execution time: mean = 25.382 us, total = 2.284 ms, Queueing time: mean = 60.783 us, max = 856.342 us, min = 13.090 us, total = 5.470 ms
	CoreWorkerService.grpc_client.PushTask - 90 total (0 active), Execution time: mean = 8.390 s, total = 755.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 60 total (0 active), Execution time: mean = 415.251 us, total = 24.915 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 60 total (0 active), Execution time: mean = 43.604 us, total = 2.616 ms, Queueing time: mean = 64.923 us, max = 342.485 us, min = 16.887 us, total = 3.895 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 16 total (1 active), Execution time: mean = 45.271 s, total = 724.338 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 16 total (0 active), Execution time: mean = 103.768 us, total = 1.660 ms, Queueing time: mean = 230.435 us, max = 332.644 us, min = 150.171 us, total = 3.687 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 15 total (0 active), Execution time: mean = 254.027 us, total = 3.810 ms, Queueing time: mean = 97.441 us, max = 592.878 us, min = 21.940 us, total = 1.462 ms
	CoreWorker.PrintEventStats - 15 total (1 active, 1 running), Execution time: mean = 597.291 us, total = 8.959 ms, Queueing time: mean = 98.481 us, max = 348.672 us, min = 40.395 us, total = 1.477 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 43.636 us, total = 174.544 us, Queueing time: mean = 49.319 us, max = 93.561 us, min = 16.954 us, total = 197.276 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling - 3 total (1 active), Execution time: mean = 198.914 s, total = 596.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 3 total (0 active), Execution time: mean = 16.414 us, total = 49.242 us, Queueing time: mean = 48.201 us, max = 54.867 us, min = 36.430 us, total = 144.603 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2701 total (1 active)
Queueing time: mean = 52.731 us, max = 11.213 ms, min = -0.000 s, total = 142.428 ms
Execution time:  mean = 468.647 us, total = 1.266 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 900 total (0 active), Execution time: mean = 38.543 us, total = 34.689 ms, Queueing time: mean = 56.178 us, max = 11.213 ms, min = 9.715 us, total = 50.560 ms
	CoreWorker.deadline_timer.flush_task_events - 900 total (1 active), Execution time: mean = 325.528 us, total = 292.975 ms, Queueing time: mean = 102.048 us, max = 6.908 ms, min = -0.000 s, total = 91.843 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 900 total (0 active), Execution time: mean = 1.042 ms, total = 937.823 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0347509 MiB
	total number of task attempts sent: 260
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:04:20,806 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 33244 total (8 active)
Queueing time: mean = 194.845 us, max = 10.340 ms, min = -0.002 s, total = 6.477 s
Execution time:  mean = 99.575 ms, total = 3310.276 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 9820 total (0 active), Execution time: mean = 176.625 us, total = 1.734 s, Queueing time: mean = 531.282 us, max = 10.309 ms, min = 2.673 us, total = 5.217 s
	NodeManagerService.grpc_client.RequestWorkerLease - 9820 total (0 active), Execution time: mean = 125.320 ms, total = 1230.639 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 9590 total (1 active), Execution time: mean = 16.052 us, total = 153.938 ms, Queueing time: mean = 102.527 us, max = 10.340 ms, min = -0.002 s, total = 983.230 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1001 total (0 active), Execution time: mean = 1.039 ms, total = 1.040 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1001 total (0 active), Execution time: mean = 25.910 us, total = 25.936 ms, Queueing time: mean = 65.549 us, max = 3.551 ms, min = 9.776 us, total = 65.615 ms
	CoreWorker.InternalHeartbeat - 960 total (1 active), Execution time: mean = 267.801 us, total = 257.089 ms, Queueing time: mean = 126.914 us, max = 7.858 ms, min = -0.000 s, total = 121.837 ms
	CoreWorker.RecordMetrics - 192 total (1 active), Execution time: mean = 49.171 us, total = 9.441 ms, Queueing time: mean = 99.770 us, max = 1.781 ms, min = 13.207 us, total = 19.156 ms
	CoreWorker.TryDelPendingObjectRefStreams - 96 total (1 active), Execution time: mean = 12.950 us, total = 1.243 ms, Queueing time: mean = 121.245 us, max = 1.806 ms, min = -0.000 s, total = 11.640 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 91 total (0 active), Execution time: mean = 127.868 ns, total = 11.636 us, Queueing time: mean = 102.117 us, max = 1.093 ms, min = 36.158 us, total = 9.293 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 90 total (0 active), Execution time: mean = 362.633 us, total = 32.637 ms, Queueing time: mean = 53.895 us, max = 578.210 us, min = 17.763 us, total = 4.851 ms
	CoreWorker.SubmitTask - 90 total (0 active), Execution time: mean = 179.163 us, total = 16.125 ms, Queueing time: mean = 276.300 us, max = 3.347 ms, min = 13.805 us, total = 24.867 ms
	NodeManagerService.grpc_client.ReturnWorker - 90 total (0 active), Execution time: mean = 1.607 ms, total = 144.637 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 90 total (0 active), Execution time: mean = 25.382 us, total = 2.284 ms, Queueing time: mean = 60.783 us, max = 856.342 us, min = 13.090 us, total = 5.470 ms
	CoreWorkerService.grpc_client.PushTask - 90 total (0 active), Execution time: mean = 8.390 s, total = 755.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 64 total (0 active), Execution time: mean = 409.089 us, total = 26.182 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 64 total (0 active), Execution time: mean = 43.139 us, total = 2.761 ms, Queueing time: mean = 64.164 us, max = 342.485 us, min = 16.887 us, total = 4.106 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 16 total (1 active), Execution time: mean = 45.271 s, total = 724.338 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 16 total (0 active), Execution time: mean = 103.768 us, total = 1.660 ms, Queueing time: mean = 230.435 us, max = 332.644 us, min = 150.171 us, total = 3.687 ms
	CoreWorker.PrintEventStats - 16 total (1 active, 1 running), Execution time: mean = 593.264 us, total = 9.492 ms, Queueing time: mean = 98.198 us, max = 348.672 us, min = 40.395 us, total = 1.571 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 15 total (0 active), Execution time: mean = 254.027 us, total = 3.810 ms, Queueing time: mean = 97.441 us, max = 592.878 us, min = 21.940 us, total = 1.462 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 43.636 us, total = 174.544 us, Queueing time: mean = 49.319 us, max = 93.561 us, min = 16.954 us, total = 197.276 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 3 total (1 active), Execution time: mean = 198.914 s, total = 596.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 3 total (0 active), Execution time: mean = 16.414 us, total = 49.242 us, Queueing time: mean = 48.201 us, max = 54.867 us, min = 36.430 us, total = 144.603 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2881 total (1 active)
Queueing time: mean = 51.855 us, max = 11.213 ms, min = -0.000 s, total = 149.393 ms
Execution time:  mean = 466.098 us, total = 1.343 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 960 total (0 active), Execution time: mean = 38.764 us, total = 37.213 ms, Queueing time: mean = 54.773 us, max = 11.213 ms, min = 9.715 us, total = 52.582 ms
	CoreWorker.deadline_timer.flush_task_events - 960 total (1 active), Execution time: mean = 323.889 us, total = 310.934 ms, Queueing time: mean = 100.820 us, max = 6.908 ms, min = -0.000 s, total = 96.787 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 960 total (0 active), Execution time: mean = 1.036 ms, total = 994.354 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0347509 MiB
	total number of task attempts sent: 260
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:05:10,996 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 71f196cf3e35c23d0086046ebee4d8b57afb729e123bf51e32afab43
[2025-06-26 01:05:20,807 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 34056 total (9 active)
Queueing time: mean = 192.448 us, max = 10.340 ms, min = -0.002 s, total = 6.554 s
Execution time:  mean = 97.224 ms, total = 3311.068 s
Event stats:
	CoreWorker.RecoverObjects - 10189 total (1 active), Execution time: mean = 16.035 us, total = 163.385 ms, Queueing time: mean = 103.076 us, max = 10.340 ms, min = -0.002 s, total = 1.050 s
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 9822 total (0 active), Execution time: mean = 176.770 us, total = 1.736 s, Queueing time: mean = 531.179 us, max = 10.309 ms, min = 2.673 us, total = 5.217 s
	NodeManagerService.grpc_client.RequestWorkerLease - 9822 total (0 active), Execution time: mean = 125.365 ms, total = 1231.334 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1061 total (0 active), Execution time: mean = 1.039 ms, total = 1.102 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1061 total (0 active), Execution time: mean = 26.133 us, total = 27.727 ms, Queueing time: mean = 63.998 us, max = 3.551 ms, min = 9.776 us, total = 67.901 ms
	CoreWorker.InternalHeartbeat - 1020 total (1 active), Execution time: mean = 268.609 us, total = 273.981 ms, Queueing time: mean = 124.874 us, max = 7.858 ms, min = -0.000 s, total = 127.371 ms
	CoreWorker.RecordMetrics - 204 total (1 active), Execution time: mean = 48.835 us, total = 9.962 ms, Queueing time: mean = 98.808 us, max = 1.781 ms, min = 13.207 us, total = 20.157 ms
	CoreWorker.TryDelPendingObjectRefStreams - 102 total (1 active), Execution time: mean = 12.742 us, total = 1.300 ms, Queueing time: mean = 118.011 us, max = 1.806 ms, min = -0.000 s, total = 12.037 ms
	CoreWorkerService.grpc_client.PushTask - 91 total (1 active), Execution time: mean = 8.298 s, total = 755.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerMemoryStore.Put.get_async_callbacks - 91 total (0 active), Execution time: mean = 127.868 ns, total = 11.636 us, Queueing time: mean = 102.117 us, max = 1.093 ms, min = 36.158 us, total = 9.293 ms
	CoreWorker.SubmitTask - 91 total (0 active), Execution time: mean = 180.525 us, total = 16.428 ms, Queueing time: mean = 273.612 us, max = 3.347 ms, min = 13.805 us, total = 24.899 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 90 total (0 active), Execution time: mean = 362.633 us, total = 32.637 ms, Queueing time: mean = 53.895 us, max = 578.210 us, min = 17.763 us, total = 4.851 ms
	NodeManagerService.grpc_client.ReturnWorker - 90 total (0 active), Execution time: mean = 1.607 ms, total = 144.637 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 90 total (0 active), Execution time: mean = 25.382 us, total = 2.284 ms, Queueing time: mean = 60.783 us, max = 856.342 us, min = 13.090 us, total = 5.470 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 68 total (0 active), Execution time: mean = 446.518 us, total = 30.363 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 68 total (0 active), Execution time: mean = 42.808 us, total = 2.911 ms, Queueing time: mean = 62.633 us, max = 342.485 us, min = 16.887 us, total = 4.259 ms
	CoreWorker.PrintEventStats - 17 total (1 active, 1 running), Execution time: mean = 590.609 us, total = 10.040 ms, Queueing time: mean = 98.856 us, max = 348.672 us, min = 40.395 us, total = 1.681 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 16 total (1 active), Execution time: mean = 45.271 s, total = 724.338 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 16 total (0 active), Execution time: mean = 103.768 us, total = 1.660 ms, Queueing time: mean = 230.435 us, max = 332.644 us, min = 150.171 us, total = 3.687 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 15 total (0 active), Execution time: mean = 254.027 us, total = 3.810 ms, Queueing time: mean = 97.441 us, max = 592.878 us, min = 21.940 us, total = 1.462 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 43.636 us, total = 174.544 us, Queueing time: mean = 49.319 us, max = 93.561 us, min = 16.954 us, total = 197.276 us
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 3 total (0 active), Execution time: mean = 16.414 us, total = 49.242 us, Queueing time: mean = 48.201 us, max = 54.867 us, min = 36.430 us, total = 144.603 us
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling - 3 total (1 active), Execution time: mean = 198.914 s, total = 596.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3061 total (1 active)
Queueing time: mean = 52.384 us, max = 11.213 ms, min = -0.000 s, total = 160.346 ms
Execution time:  mean = 469.220 us, total = 1.436 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1020 total (0 active), Execution time: mean = 38.816 us, total = 39.592 ms, Queueing time: mean = 54.372 us, max = 11.213 ms, min = 9.715 us, total = 55.460 ms
	CoreWorker.deadline_timer.flush_task_events - 1020 total (1 active), Execution time: mean = 323.880 us, total = 330.357 ms, Queueing time: mean = 102.806 us, max = 6.908 ms, min = -0.000 s, total = 104.862 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1020 total (0 active), Execution time: mean = 1.045 ms, total = 1.066 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0351734 MiB
	total number of task attempts sent: 263
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:06:20,807 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 34934 total (20 active)
Queueing time: mean = 189.615 us, max = 10.340 ms, min = -0.002 s, total = 6.624 s
Execution time:  mean = 95.337 ms, total = 3330.502 s
Event stats:
	CoreWorker.RecoverObjects - 10788 total (1 active), Execution time: mean = 16.031 us, total = 172.939 ms, Queueing time: mean = 102.306 us, max = 10.340 ms, min = -0.002 s, total = 1.104 s
	NodeManagerService.grpc_client.RequestWorkerLease - 9848 total (10 active), Execution time: mean = 125.036 ms, total = 1231.352 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 9838 total (0 active), Execution time: mean = 176.916 us, total = 1.740 s, Queueing time: mean = 530.659 us, max = 10.309 ms, min = 2.673 us, total = 5.221 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1121 total (0 active), Execution time: mean = 1.038 ms, total = 1.163 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1121 total (0 active), Execution time: mean = 26.353 us, total = 29.541 ms, Queueing time: mean = 62.208 us, max = 3.551 ms, min = 9.776 us, total = 69.735 ms
	CoreWorker.InternalHeartbeat - 1080 total (1 active), Execution time: mean = 269.654 us, total = 291.226 ms, Queueing time: mean = 123.609 us, max = 7.858 ms, min = -0.000 s, total = 133.497 ms
	CoreWorker.RecordMetrics - 216 total (1 active), Execution time: mean = 48.534 us, total = 10.483 ms, Queueing time: mean = 99.360 us, max = 1.781 ms, min = 13.207 us, total = 21.462 ms
	CoreWorker.SubmitTask - 110 total (0 active), Execution time: mean = 198.838 us, total = 21.872 ms, Queueing time: mean = 250.474 us, max = 3.347 ms, min = 13.805 us, total = 27.552 ms
	CoreWorker.TryDelPendingObjectRefStreams - 108 total (1 active), Execution time: mean = 12.653 us, total = 1.366 ms, Queueing time: mean = 116.181 us, max = 1.806 ms, min = -0.000 s, total = 12.548 ms
	CoreWorkerService.grpc_client.PushTask - 94 total (2 active), Execution time: mean = 8.238 s, total = 774.395 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerMemoryStore.Put.get_async_callbacks - 93 total (0 active), Execution time: mean = 127.269 ns, total = 11.836 us, Queueing time: mean = 101.629 us, max = 1.093 ms, min = 36.158 us, total = 9.451 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 92 total (0 active), Execution time: mean = 363.970 us, total = 33.485 ms, Queueing time: mean = 53.592 us, max = 578.210 us, min = 17.763 us, total = 4.930 ms
	NodeManagerService.grpc_client.ReturnWorker - 92 total (0 active), Execution time: mean = 1.602 ms, total = 147.370 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 92 total (0 active), Execution time: mean = 25.297 us, total = 2.327 ms, Queueing time: mean = 60.481 us, max = 856.342 us, min = 13.090 us, total = 5.564 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 72 total (0 active), Execution time: mean = 439.108 us, total = 31.616 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 72 total (0 active), Execution time: mean = 43.079 us, total = 3.102 ms, Queueing time: mean = 62.743 us, max = 342.485 us, min = 16.887 us, total = 4.517 ms
	CoreWorker.PrintEventStats - 18 total (1 active, 1 running), Execution time: mean = 586.269 us, total = 10.553 ms, Queueing time: mean = 100.895 us, max = 348.672 us, min = 40.395 us, total = 1.816 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 16 total (1 active), Execution time: mean = 45.271 s, total = 724.338 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 16 total (0 active), Execution time: mean = 103.768 us, total = 1.660 ms, Queueing time: mean = 230.435 us, max = 332.644 us, min = 150.171 us, total = 3.687 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 15 total (0 active), Execution time: mean = 254.027 us, total = 3.810 ms, Queueing time: mean = 97.441 us, max = 592.878 us, min = 21.940 us, total = 1.462 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 43.636 us, total = 174.544 us, Queueing time: mean = 49.319 us, max = 93.561 us, min = 16.954 us, total = 197.276 us
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 3 total (0 active), Execution time: mean = 16.414 us, total = 49.242 us, Queueing time: mean = 48.201 us, max = 54.867 us, min = 36.430 us, total = 144.603 us
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling - 3 total (1 active), Execution time: mean = 198.914 s, total = 596.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3241 total (1 active)
Queueing time: mean = 52.063 us, max = 11.213 ms, min = -0.000 s, total = 168.737 ms
Execution time:  mean = 468.225 us, total = 1.518 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1080 total (0 active), Execution time: mean = 38.821 us, total = 41.927 ms, Queueing time: mean = 53.158 us, max = 11.213 ms, min = 9.715 us, total = 57.411 ms
	CoreWorker.deadline_timer.flush_task_events - 1080 total (1 active), Execution time: mean = 323.847 us, total = 349.754 ms, Queueing time: mean = 103.056 us, max = 6.908 ms, min = -0.000 s, total = 111.301 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1080 total (0 active), Execution time: mean = 1.042 ms, total = 1.126 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0400896 MiB
	total number of task attempts sent: 286
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:06:48,333 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=77967f0e6d34a5fa26747c3c66d5a8d7b80570e71ff0bfb0320af5c8
[2025-06-26 01:06:48,333 I 693801 693872] core_worker.cc:5107: Number of alive nodes:3
[2025-06-26 01:06:48,336 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 77967f0e6d34a5fa26747c3c66d5a8d7b80570e71ff0bfb0320af5c8
[2025-06-26 01:06:49,503 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=cc56480aa1d01d213e5567748e50a120eecea0f10356a110b2b49300
[2025-06-26 01:06:49,509 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet cc56480aa1d01d213e5567748e50a120eecea0f10356a110b2b49300
[2025-06-26 01:06:51,334 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=8c4d80205327b4f323d879c99025b7243851b812f3c5de83998be538
[2025-06-26 01:06:51,337 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 8c4d80205327b4f323d879c99025b7243851b812f3c5de83998be538
[2025-06-26 01:07:20,808 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 38408 total (8 active)
Queueing time: mean = 183.540 us, max = 10.340 ms, min = -0.002 s, total = 7.049 s
Execution time:  mean = 109.680 ms, total = 4212.594 s
Event stats:
	CoreWorker.RecoverObjects - 11388 total (1 active), Execution time: mean = 16.004 us, total = 182.259 ms, Queueing time: mean = 101.500 us, max = 10.340 ms, min = -0.002 s, total = 1.156 s
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 11122 total (0 active), Execution time: mean = 177.551 us, total = 1.975 s, Queueing time: mean = 501.644 us, max = 10.309 ms, min = 2.673 us, total = 5.579 s
	NodeManagerService.grpc_client.RequestWorkerLease - 11122 total (0 active), Execution time: mean = 140.600 ms, total = 1563.755 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1187 total (0 active), Execution time: mean = 1.048 ms, total = 1.244 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1187 total (0 active), Execution time: mean = 26.579 us, total = 31.550 ms, Queueing time: mean = 61.446 us, max = 3.551 ms, min = 9.776 us, total = 72.936 ms
	CoreWorker.InternalHeartbeat - 1140 total (1 active), Execution time: mean = 274.396 us, total = 312.811 ms, Queueing time: mean = 121.345 us, max = 7.858 ms, min = -0.000 s, total = 138.333 ms
	CoreWorker.RecordMetrics - 228 total (1 active), Execution time: mean = 49.431 us, total = 11.270 ms, Queueing time: mean = 97.723 us, max = 1.781 ms, min = 13.207 us, total = 22.281 ms
	CoreWorker.TryDelPendingObjectRefStreams - 114 total (1 active), Execution time: mean = 12.554 us, total = 1.431 ms, Queueing time: mean = 114.109 us, max = 1.806 ms, min = -0.000 s, total = 13.008 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 111 total (0 active), Execution time: mean = 124.072 ns, total = 13.772 us, Queueing time: mean = 98.894 us, max = 1.093 ms, min = 36.158 us, total = 10.977 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 110 total (0 active), Execution time: mean = 364.904 us, total = 40.139 ms, Queueing time: mean = 53.994 us, max = 578.210 us, min = 17.763 us, total = 5.939 ms
	CoreWorker.SubmitTask - 110 total (0 active), Execution time: mean = 198.838 us, total = 21.872 ms, Queueing time: mean = 250.474 us, max = 3.347 ms, min = 13.805 us, total = 27.552 ms
	NodeManagerService.grpc_client.ReturnWorker - 110 total (0 active), Execution time: mean = 1.601 ms, total = 176.056 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 110 total (0 active), Execution time: mean = 25.233 us, total = 2.776 ms, Queueing time: mean = 57.104 us, max = 856.342 us, min = 10.869 us, total = 6.281 ms
	CoreWorkerService.grpc_client.PushTask - 110 total (0 active), Execution time: mean = 8.523 s, total = 937.496 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 76 total (0 active), Execution time: mean = 435.709 us, total = 33.114 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 76 total (0 active), Execution time: mean = 42.864 us, total = 3.258 ms, Queueing time: mean = 62.122 us, max = 342.485 us, min = 16.887 us, total = 4.721 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 19 total (1 active), Execution time: mean = 58.449 s, total = 1110.539 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 19 total (0 active), Execution time: mean = 101.264 us, total = 1.924 ms, Queueing time: mean = 230.517 us, max = 334.311 us, min = 150.171 us, total = 4.380 ms
	CoreWorker.PrintEventStats - 19 total (1 active, 1 running), Execution time: mean = 597.016 us, total = 11.343 ms, Queueing time: mean = 97.941 us, max = 348.672 us, min = 40.395 us, total = 1.861 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 18 total (0 active), Execution time: mean = 252.735 us, total = 4.549 ms, Queueing time: mean = 138.759 us, max = 629.450 us, min = 21.940 us, total = 2.498 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 43.636 us, total = 174.544 us, Queueing time: mean = 49.319 us, max = 93.561 us, min = 16.954 us, total = 197.276 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 3 total (1 active), Execution time: mean = 198.914 s, total = 596.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 3 total (0 active), Execution time: mean = 16.414 us, total = 49.242 us, Queueing time: mean = 48.201 us, max = 54.867 us, min = 36.430 us, total = 144.603 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3421 total (1 active)
Queueing time: mean = 51.454 us, max = 11.213 ms, min = -0.000 s, total = 176.025 ms
Execution time:  mean = 467.790 us, total = 1.600 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1140 total (0 active), Execution time: mean = 39.009 us, total = 44.470 ms, Queueing time: mean = 52.246 us, max = 11.213 ms, min = 9.715 us, total = 59.561 ms
	CoreWorker.deadline_timer.flush_task_events - 1140 total (1 active), Execution time: mean = 324.332 us, total = 369.739 ms, Queueing time: mean = 102.140 us, max = 6.908 ms, min = -0.000 s, total = 116.440 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1140 total (0 active), Execution time: mean = 1.040 ms, total = 1.186 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0426264 MiB
	total number of task attempts sent: 320
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:08:06,756 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=77967f0e6d34a5fa26747c3c66d5a8d7b80570e71ff0bfb0320af5c8
[2025-06-26 01:08:06,756 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=77967f0e6d34a5fa26747c3c66d5a8d7b80570e71ff0bfb0320af5c8
[2025-06-26 01:08:06,756 I 693801 693872] core_worker.cc:5107: Number of alive nodes:4
[2025-06-26 01:08:06,759 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=8c4d80205327b4f323d879c99025b7243851b812f3c5de83998be538
[2025-06-26 01:08:06,759 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=8c4d80205327b4f323d879c99025b7243851b812f3c5de83998be538
[2025-06-26 01:08:06,759 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=cc56480aa1d01d213e5567748e50a120eecea0f10356a110b2b49300
[2025-06-26 01:08:06,759 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=cc56480aa1d01d213e5567748e50a120eecea0f10356a110b2b49300
[2025-06-26 01:08:20,809 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 39224 total (8 active)
Queueing time: mean = 181.331 us, max = 10.340 ms, min = -0.002 s, total = 7.113 s
Execution time:  mean = 124.620 ms, total = 4888.114 s
Event stats:
	CoreWorker.RecoverObjects - 11987 total (1 active), Execution time: mean = 15.978 us, total = 191.525 ms, Queueing time: mean = 100.789 us, max = 10.340 ms, min = -0.002 s, total = 1.208 s
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 11122 total (0 active), Execution time: mean = 177.551 us, total = 1.975 s, Queueing time: mean = 501.644 us, max = 10.309 ms, min = 2.673 us, total = 5.579 s
	NodeManagerService.grpc_client.RequestWorkerLease - 11122 total (0 active), Execution time: mean = 140.600 ms, total = 1563.755 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1247 total (0 active), Execution time: mean = 1.046 ms, total = 1.305 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1247 total (0 active), Execution time: mean = 26.765 us, total = 33.376 ms, Queueing time: mean = 60.059 us, max = 3.551 ms, min = 9.776 us, total = 74.894 ms
	CoreWorker.InternalHeartbeat - 1200 total (1 active), Execution time: mean = 277.284 us, total = 332.741 ms, Queueing time: mean = 120.657 us, max = 7.858 ms, min = -0.000 s, total = 144.789 ms
	CoreWorker.RecordMetrics - 240 total (1 active), Execution time: mean = 48.955 us, total = 11.749 ms, Queueing time: mean = 96.103 us, max = 1.781 ms, min = 13.207 us, total = 23.065 ms
	CoreWorker.TryDelPendingObjectRefStreams - 120 total (1 active), Execution time: mean = 12.515 us, total = 1.502 ms, Queueing time: mean = 112.470 us, max = 1.806 ms, min = -0.000 s, total = 13.496 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 111 total (0 active), Execution time: mean = 124.072 ns, total = 13.772 us, Queueing time: mean = 98.894 us, max = 1.093 ms, min = 36.158 us, total = 10.977 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 110 total (0 active), Execution time: mean = 364.904 us, total = 40.139 ms, Queueing time: mean = 53.994 us, max = 578.210 us, min = 17.763 us, total = 5.939 ms
	CoreWorker.SubmitTask - 110 total (0 active), Execution time: mean = 198.838 us, total = 21.872 ms, Queueing time: mean = 250.474 us, max = 3.347 ms, min = 13.805 us, total = 27.552 ms
	NodeManagerService.grpc_client.ReturnWorker - 110 total (0 active), Execution time: mean = 1.601 ms, total = 176.056 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 110 total (0 active), Execution time: mean = 25.233 us, total = 2.776 ms, Queueing time: mean = 57.104 us, max = 856.342 us, min = 10.869 us, total = 6.281 ms
	CoreWorkerService.grpc_client.PushTask - 110 total (0 active), Execution time: mean = 8.523 s, total = 937.496 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 80 total (0 active), Execution time: mean = 431.301 us, total = 34.504 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 80 total (0 active), Execution time: mean = 42.792 us, total = 3.423 ms, Queueing time: mean = 61.870 us, max = 342.485 us, min = 16.887 us, total = 4.950 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 22 total (0 active), Execution time: mean = 98.423 us, total = 2.165 ms, Queueing time: mean = 228.959 us, max = 334.311 us, min = 150.171 us, total = 5.037 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 21 total (1 active), Execution time: mean = 56.474 s, total = 1185.964 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 20 total (0 active), Execution time: mean = 247.052 us, total = 4.941 ms, Queueing time: mean = 128.422 us, max = 629.450 us, min = 21.940 us, total = 2.568 ms
	CoreWorker.PrintEventStats - 20 total (1 active, 1 running), Execution time: mean = 599.635 us, total = 11.993 ms, Queueing time: mean = 98.264 us, max = 348.672 us, min = 40.395 us, total = 1.965 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 5 total (1 active), Execution time: mean = 42.188 us, total = 210.938 us, Queueing time: mean = 52.195 us, max = 93.561 us, min = 16.954 us, total = 260.973 us
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 4 total (0 active), Execution time: mean = 18.168 us, total = 72.673 us, Queueing time: mean = 45.852 us, max = 54.867 us, min = 36.430 us, total = 183.406 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 4 total (1 active), Execution time: mean = 299.185 s, total = 1196.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3601 total (1 active)
Queueing time: mean = 51.070 us, max = 11.213 ms, min = -0.000 s, total = 183.902 ms
Execution time:  mean = 466.775 us, total = 1.681 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1200 total (0 active), Execution time: mean = 38.975 us, total = 46.771 ms, Queueing time: mean = 51.251 us, max = 11.213 ms, min = 9.715 us, total = 61.501 ms
	CoreWorker.deadline_timer.flush_task_events - 1200 total (1 active), Execution time: mean = 324.046 us, total = 388.855 ms, Queueing time: mean = 101.981 us, max = 6.908 ms, min = -0.000 s, total = 122.377 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1200 total (0 active), Execution time: mean = 1.037 ms, total = 1.245 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0426264 MiB
	total number of task attempts sent: 320
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:08:33,617 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=374025dd58ceacb2d89386dc3a38408dc32b1456276f0fcd84952fd1
[2025-06-26 01:08:34,173 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 374025dd58ceacb2d89386dc3a38408dc32b1456276f0fcd84952fd1
[2025-06-26 01:09:00,590 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=22dcf4c94eb49e5029d861bbd4193e4e05721c90987ce6702bef89a1
[2025-06-26 01:09:00,594 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 22dcf4c94eb49e5029d861bbd4193e4e05721c90987ce6702bef89a1
[2025-06-26 01:09:03,836 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=45dcdd8fc5701ea50b0cef58c35281c586bc903f5a425e0f3c9ed66e
[2025-06-26 01:09:03,839 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 45dcdd8fc5701ea50b0cef58c35281c586bc903f5a425e0f3c9ed66e
[2025-06-26 01:09:20,809 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 56607 total (26 active)
Queueing time: mean = 226.695 us, max = 10.340 ms, min = -0.002 s, total = 12.833 s
Execution time:  mean = 98.793 ms, total = 5592.357 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease - 19276 total (10 active), Execution time: mean = 103.866 ms, total = 2002.128 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 19266 total (0 active), Execution time: mean = 172.218 us, total = 3.318 s, Queueing time: mean = 579.662 us, max = 10.309 ms, min = 2.673 us, total = 11.168 s
	CoreWorker.RecoverObjects - 12587 total (1 active), Execution time: mean = 16.031 us, total = 201.784 ms, Queueing time: mean = 101.598 us, max = 10.340 ms, min = -0.002 s, total = 1.279 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1344 total (0 active), Execution time: mean = 1.055 ms, total = 1.418 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1344 total (0 active), Execution time: mean = 26.668 us, total = 35.841 ms, Queueing time: mean = 71.851 us, max = 3.551 ms, min = 9.776 us, total = 96.568 ms
	CoreWorker.InternalHeartbeat - 1260 total (1 active), Execution time: mean = 282.543 us, total = 356.004 ms, Queueing time: mean = 120.675 us, max = 7.858 ms, min = -0.000 s, total = 152.051 ms
	CoreWorker.RecordMetrics - 252 total (1 active), Execution time: mean = 50.220 us, total = 12.656 ms, Queueing time: mean = 95.794 us, max = 1.781 ms, min = 13.207 us, total = 24.140 ms
	CoreWorker.SubmitTask - 182 total (0 active), Execution time: mean = 156.391 us, total = 28.463 ms, Queueing time: mean = 275.236 us, max = 3.347 ms, min = 13.805 us, total = 50.093 ms
	CoreWorkerService.grpc_client.PushTask - 141 total (8 active), Execution time: mean = 8.119 s, total = 1144.738 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerMemoryStore.Put.get_async_callbacks - 134 total (0 active), Execution time: mean = 128.022 ns, total = 17.155 us, Queueing time: mean = 99.217 us, max = 1.093 ms, min = 36.158 us, total = 13.295 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 133 total (0 active), Execution time: mean = 373.947 us, total = 49.735 ms, Queueing time: mean = 67.504 us, max = 1.857 ms, min = 17.763 us, total = 8.978 ms
	NodeManagerService.grpc_client.ReturnWorker - 133 total (0 active), Execution time: mean = 1.618 ms, total = 215.260 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 133 total (0 active), Execution time: mean = 25.735 us, total = 3.423 ms, Queueing time: mean = 54.689 us, max = 856.342 us, min = 10.869 us, total = 7.274 ms
	CoreWorker.TryDelPendingObjectRefStreams - 126 total (1 active), Execution time: mean = 12.408 us, total = 1.563 ms, Queueing time: mean = 110.747 us, max = 1.806 ms, min = -0.000 s, total = 13.954 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 84 total (0 active), Execution time: mean = 46.299 us, total = 3.889 ms, Queueing time: mean = 61.074 us, max = 342.485 us, min = 16.887 us, total = 5.130 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 84 total (0 active), Execution time: mean = 430.835 us, total = 36.190 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 25 total (0 active), Execution time: mean = 97.904 us, total = 2.448 ms, Queueing time: mean = 231.795 us, max = 334.311 us, min = 150.171 us, total = 5.795 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 24 total (1 active), Execution time: mean = 51.793 s, total = 1243.040 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 23 total (0 active), Execution time: mean = 257.832 us, total = 5.930 ms, Queueing time: mean = 132.692 us, max = 629.450 us, min = 21.940 us, total = 3.052 ms
	CoreWorker.PrintEventStats - 21 total (1 active, 1 running), Execution time: mean = 595.161 us, total = 12.498 ms, Queueing time: mean = 95.772 us, max = 348.672 us, min = 40.395 us, total = 2.011 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 5 total (1 active), Execution time: mean = 42.188 us, total = 210.938 us, Queueing time: mean = 52.195 us, max = 93.561 us, min = 16.954 us, total = 260.973 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 4 total (1 active), Execution time: mean = 299.185 s, total = 1196.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 4 total (0 active), Execution time: mean = 18.168 us, total = 72.673 us, Queueing time: mean = 45.852 us, max = 54.867 us, min = 36.430 us, total = 183.406 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3781 total (1 active)
Queueing time: mean = 50.549 us, max = 11.213 ms, min = -0.000 s, total = 191.125 ms
Execution time:  mean = 471.942 us, total = 1.784 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1260 total (0 active), Execution time: mean = 39.418 us, total = 49.667 ms, Queueing time: mean = 50.337 us, max = 11.213 ms, min = 9.715 us, total = 63.424 ms
	CoreWorker.deadline_timer.flush_task_events - 1260 total (1 active), Execution time: mean = 331.836 us, total = 418.114 ms, Queueing time: mean = 101.330 us, max = 6.908 ms, min = -0.000 s, total = 127.676 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1260 total (0 active), Execution time: mean = 1.045 ms, total = 1.316 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0639353 MiB
	total number of task attempts sent: 447
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:10:20,810 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 86485 total (8 active)
Queueing time: mean = 267.421 us, max = 19.454 ms, min = -0.002 s, total = 23.128 s
Execution time:  mean = 74.014 ms, total = 6401.115 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 33658 total (0 active), Execution time: mean = 165.878 us, total = 5.583 s, Queueing time: mean = 631.359 us, max = 11.316 ms, min = 2.310 us, total = 21.250 s
	NodeManagerService.grpc_client.RequestWorkerLease - 33658 total (0 active), Execution time: mean = 70.776 ms, total = 2382.185 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 13185 total (1 active), Execution time: mean = 16.065 us, total = 211.817 ms, Queueing time: mean = 107.619 us, max = 19.454 ms, min = -0.002 s, total = 1.419 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1435 total (0 active), Execution time: mean = 1.064 ms, total = 1.527 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1435 total (0 active), Execution time: mean = 26.488 us, total = 38.011 ms, Queueing time: mean = 90.226 us, max = 5.249 ms, min = 9.737 us, total = 129.474 ms
	CoreWorker.InternalHeartbeat - 1320 total (1 active), Execution time: mean = 286.973 us, total = 378.805 ms, Queueing time: mean = 121.959 us, max = 7.858 ms, min = -0.000 s, total = 160.986 ms
	CoreWorker.RecordMetrics - 264 total (1 active), Execution time: mean = 51.846 us, total = 13.687 ms, Queueing time: mean = 94.633 us, max = 1.781 ms, min = 13.207 us, total = 24.983 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 183 total (0 active), Execution time: mean = 133.967 ns, total = 24.516 us, Queueing time: mean = 107.729 us, max = 1.121 ms, min = 36.158 us, total = 19.714 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 182 total (0 active), Execution time: mean = 383.202 us, total = 69.743 ms, Queueing time: mean = 76.219 us, max = 1.857 ms, min = 17.763 us, total = 13.872 ms
	CoreWorker.SubmitTask - 182 total (0 active), Execution time: mean = 156.391 us, total = 28.463 ms, Queueing time: mean = 275.236 us, max = 3.347 ms, min = 13.805 us, total = 50.093 ms
	NodeManagerService.grpc_client.ReturnWorker - 182 total (0 active), Execution time: mean = 1.764 ms, total = 321.043 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 182 total (0 active), Execution time: mean = 26.446 us, total = 4.813 ms, Queueing time: mean = 136.018 us, max = 11.316 ms, min = 10.869 us, total = 24.755 ms
	CoreWorkerService.grpc_client.PushTask - 182 total (0 active), Execution time: mean = 8.631 s, total = 1570.897 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 132 total (1 active), Execution time: mean = 12.386 us, total = 1.635 ms, Queueing time: mean = 109.843 us, max = 1.806 ms, min = -0.000 s, total = 14.499 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 88 total (0 active), Execution time: mean = 435.689 us, total = 38.341 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 88 total (0 active), Execution time: mean = 47.258 us, total = 4.159 ms, Queueing time: mean = 66.420 us, max = 395.873 us, min = 16.887 us, total = 5.845 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 25 total (0 active), Execution time: mean = 97.904 us, total = 2.448 ms, Queueing time: mean = 231.795 us, max = 334.311 us, min = 150.171 us, total = 5.795 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 24 total (1 active), Execution time: mean = 51.793 s, total = 1243.040 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 23 total (0 active), Execution time: mean = 257.832 us, total = 5.930 ms, Queueing time: mean = 132.692 us, max = 629.450 us, min = 21.940 us, total = 3.052 ms
	CoreWorker.PrintEventStats - 22 total (1 active, 1 running), Execution time: mean = 594.426 us, total = 13.077 ms, Queueing time: mean = 93.865 us, max = 348.672 us, min = 40.395 us, total = 2.065 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 5 total (1 active), Execution time: mean = 42.188 us, total = 210.938 us, Queueing time: mean = 52.195 us, max = 93.561 us, min = 16.954 us, total = 260.973 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 4 total (1 active), Execution time: mean = 299.185 s, total = 1196.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 4 total (0 active), Execution time: mean = 18.168 us, total = 72.673 us, Queueing time: mean = 45.852 us, max = 54.867 us, min = 36.430 us, total = 183.406 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3961 total (1 active)
Queueing time: mean = 50.758 us, max = 11.213 ms, min = -0.000 s, total = 201.054 ms
Execution time:  mean = 475.157 us, total = 1.882 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1320 total (0 active), Execution time: mean = 39.782 us, total = 52.513 ms, Queueing time: mean = 50.224 us, max = 11.213 ms, min = 9.715 us, total = 66.295 ms
	CoreWorker.deadline_timer.flush_task_events - 1320 total (1 active), Execution time: mean = 332.964 us, total = 439.513 ms, Queueing time: mean = 102.071 us, max = 6.908 ms, min = -0.000 s, total = 134.734 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1320 total (0 active), Execution time: mean = 1.053 ms, total = 1.390 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0705729 MiB
	total number of task attempts sent: 537
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:11:04,554 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=22dcf4c94eb49e5029d861bbd4193e4e05721c90987ce6702bef89a1
[2025-06-26 01:11:04,554 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=22dcf4c94eb49e5029d861bbd4193e4e05721c90987ce6702bef89a1
[2025-06-26 01:11:04,554 I 693801 693872] core_worker.cc:5107: Number of alive nodes:4
[2025-06-26 01:11:09,673 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=45dcdd8fc5701ea50b0cef58c35281c586bc903f5a425e0f3c9ed66e
[2025-06-26 01:11:09,673 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=45dcdd8fc5701ea50b0cef58c35281c586bc903f5a425e0f3c9ed66e
[2025-06-26 01:11:14,780 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=374025dd58ceacb2d89386dc3a38408dc32b1456276f0fcd84952fd1
[2025-06-26 01:11:14,780 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=374025dd58ceacb2d89386dc3a38408dc32b1456276f0fcd84952fd1
[2025-06-26 01:11:20,811 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 87300 total (8 active)
Queueing time: mean = 265.715 us, max = 19.454 ms, min = -0.002 s, total = 23.197 s
Execution time:  mean = 74.824 ms, total = 6532.146 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 33658 total (0 active), Execution time: mean = 165.878 us, total = 5.583 s, Queueing time: mean = 631.359 us, max = 11.316 ms, min = 2.310 us, total = 21.250 s
	NodeManagerService.grpc_client.RequestWorkerLease - 33658 total (0 active), Execution time: mean = 70.776 ms, total = 2382.185 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 13784 total (1 active), Execution time: mean = 16.056 us, total = 221.321 ms, Queueing time: mean = 107.298 us, max = 19.454 ms, min = -0.002 s, total = 1.479 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1495 total (0 active), Execution time: mean = 1.059 ms, total = 1.584 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1495 total (0 active), Execution time: mean = 26.634 us, total = 39.818 ms, Queueing time: mean = 87.869 us, max = 5.249 ms, min = 9.737 us, total = 131.364 ms
	CoreWorker.InternalHeartbeat - 1380 total (1 active), Execution time: mean = 285.793 us, total = 394.394 ms, Queueing time: mean = 119.813 us, max = 7.858 ms, min = -0.000 s, total = 165.342 ms
	CoreWorker.RecordMetrics - 276 total (1 active), Execution time: mean = 51.262 us, total = 14.148 ms, Queueing time: mean = 94.182 us, max = 1.781 ms, min = 13.207 us, total = 25.994 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 183 total (0 active), Execution time: mean = 133.967 ns, total = 24.516 us, Queueing time: mean = 107.729 us, max = 1.121 ms, min = 36.158 us, total = 19.714 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 182 total (0 active), Execution time: mean = 383.202 us, total = 69.743 ms, Queueing time: mean = 76.219 us, max = 1.857 ms, min = 17.763 us, total = 13.872 ms
	CoreWorker.SubmitTask - 182 total (0 active), Execution time: mean = 156.391 us, total = 28.463 ms, Queueing time: mean = 275.236 us, max = 3.347 ms, min = 13.805 us, total = 50.093 ms
	NodeManagerService.grpc_client.ReturnWorker - 182 total (0 active), Execution time: mean = 1.764 ms, total = 321.043 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 182 total (0 active), Execution time: mean = 26.446 us, total = 4.813 ms, Queueing time: mean = 136.018 us, max = 11.316 ms, min = 10.869 us, total = 24.755 ms
	CoreWorkerService.grpc_client.PushTask - 182 total (0 active), Execution time: mean = 8.631 s, total = 1570.897 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 138 total (1 active), Execution time: mean = 12.330 us, total = 1.701 ms, Queueing time: mean = 108.487 us, max = 1.806 ms, min = -0.000 s, total = 14.971 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 92 total (0 active), Execution time: mean = 431.691 us, total = 39.716 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 92 total (0 active), Execution time: mean = 46.913 us, total = 4.316 ms, Queueing time: mean = 65.928 us, max = 395.873 us, min = 16.757 us, total = 6.065 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 28 total (0 active), Execution time: mean = 106.339 us, total = 2.977 ms, Queueing time: mean = 229.647 us, max = 334.311 us, min = 150.171 us, total = 6.430 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 27 total (1 active), Execution time: mean = 50.888 s, total = 1373.983 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 26 total (0 active), Execution time: mean = 253.890 us, total = 6.601 ms, Queueing time: mean = 130.185 us, max = 629.450 us, min = 21.940 us, total = 3.385 ms
	CoreWorker.PrintEventStats - 23 total (1 active, 1 running), Execution time: mean = 601.974 us, total = 13.845 ms, Queueing time: mean = 92.712 us, max = 348.672 us, min = 40.395 us, total = 2.132 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 5 total (1 active), Execution time: mean = 42.188 us, total = 210.938 us, Queueing time: mean = 52.195 us, max = 93.561 us, min = 16.954 us, total = 260.973 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 4 total (1 active), Execution time: mean = 299.185 s, total = 1196.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 4 total (0 active), Execution time: mean = 18.168 us, total = 72.673 us, Queueing time: mean = 45.852 us, max = 54.867 us, min = 36.430 us, total = 183.406 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4141 total (1 active)
Queueing time: mean = 51.549 us, max = 11.213 ms, min = -0.000 s, total = 213.465 ms
Execution time:  mean = 474.608 us, total = 1.965 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1380 total (0 active), Execution time: mean = 39.804 us, total = 54.930 ms, Queueing time: mean = 49.643 us, max = 11.213 ms, min = 9.715 us, total = 68.508 ms
	CoreWorker.deadline_timer.flush_task_events - 1380 total (1 active), Execution time: mean = 332.410 us, total = 458.725 ms, Queueing time: mean = 105.024 us, max = 6.908 ms, min = -0.000 s, total = 144.933 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1380 total (0 active), Execution time: mean = 1.052 ms, total = 1.451 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0705729 MiB
	total number of task attempts sent: 537
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:11:32,006 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:11:35,098 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=71f196cf3e35c23d0086046ebee4d8b57afb729e123bf51e32afab43
[2025-06-26 01:11:35,098 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=71f196cf3e35c23d0086046ebee4d8b57afb729e123bf51e32afab43
[2025-06-26 01:12:20,812 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 88113 total (8 active)
Queueing time: mean = 263.937 us, max = 19.454 ms, min = -0.002 s, total = 23.256 s
Execution time:  mean = 74.365 ms, total = 6552.551 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 33658 total (0 active), Execution time: mean = 165.878 us, total = 5.583 s, Queueing time: mean = 631.359 us, max = 11.316 ms, min = 2.310 us, total = 21.250 s
	NodeManagerService.grpc_client.RequestWorkerLease - 33658 total (0 active), Execution time: mean = 70.776 ms, total = 2382.185 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 14384 total (1 active), Execution time: mean = 16.010 us, total = 230.282 ms, Queueing time: mean = 106.334 us, max = 19.454 ms, min = -0.002 s, total = 1.530 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1555 total (0 active), Execution time: mean = 1.055 ms, total = 1.641 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1555 total (0 active), Execution time: mean = 26.783 us, total = 41.648 ms, Queueing time: mean = 85.725 us, max = 5.249 ms, min = 9.737 us, total = 133.302 ms
	CoreWorker.InternalHeartbeat - 1440 total (1 active), Execution time: mean = 285.164 us, total = 410.636 ms, Queueing time: mean = 117.947 us, max = 7.858 ms, min = -0.000 s, total = 169.844 ms
	CoreWorker.RecordMetrics - 288 total (1 active), Execution time: mean = 50.769 us, total = 14.622 ms, Queueing time: mean = 94.092 us, max = 1.781 ms, min = 13.207 us, total = 27.098 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 183 total (0 active), Execution time: mean = 133.967 ns, total = 24.516 us, Queueing time: mean = 107.729 us, max = 1.121 ms, min = 36.158 us, total = 19.714 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 182 total (0 active), Execution time: mean = 383.202 us, total = 69.743 ms, Queueing time: mean = 76.219 us, max = 1.857 ms, min = 17.763 us, total = 13.872 ms
	CoreWorker.SubmitTask - 182 total (0 active), Execution time: mean = 156.391 us, total = 28.463 ms, Queueing time: mean = 275.236 us, max = 3.347 ms, min = 13.805 us, total = 50.093 ms
	NodeManagerService.grpc_client.ReturnWorker - 182 total (0 active), Execution time: mean = 1.764 ms, total = 321.043 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 182 total (0 active), Execution time: mean = 26.446 us, total = 4.813 ms, Queueing time: mean = 136.018 us, max = 11.316 ms, min = 10.869 us, total = 24.755 ms
	CoreWorkerService.grpc_client.PushTask - 182 total (0 active), Execution time: mean = 8.631 s, total = 1570.897 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 144 total (1 active), Execution time: mean = 12.249 us, total = 1.764 ms, Queueing time: mean = 107.880 us, max = 1.806 ms, min = -0.000 s, total = 15.535 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 96 total (0 active), Execution time: mean = 426.777 us, total = 40.971 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 96 total (0 active), Execution time: mean = 46.772 us, total = 4.490 ms, Queueing time: mean = 64.564 us, max = 395.873 us, min = 16.757 us, total = 6.198 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 30 total (0 active), Execution time: mean = 105.106 us, total = 3.153 ms, Queueing time: mean = 228.475 us, max = 334.311 us, min = 150.171 us, total = 6.854 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 29 total (1 active), Execution time: mean = 48.079 s, total = 1394.301 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 28 total (0 active), Execution time: mean = 251.614 us, total = 7.045 ms, Queueing time: mean = 122.409 us, max = 629.450 us, min = 16.141 us, total = 3.427 ms
	CoreWorker.PrintEventStats - 24 total (1 active, 1 running), Execution time: mean = 601.573 us, total = 14.438 ms, Queueing time: mean = 92.447 us, max = 348.672 us, min = 40.395 us, total = 2.219 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	Publisher.CheckDeadSubscribers - 5 total (1 active), Execution time: mean = 42.188 us, total = 210.938 us, Queueing time: mean = 52.195 us, max = 93.561 us, min = 16.954 us, total = 260.973 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 4 total (1 active), Execution time: mean = 299.185 s, total = 1196.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 4 total (0 active), Execution time: mean = 18.168 us, total = 72.673 us, Queueing time: mean = 45.852 us, max = 54.867 us, min = 36.430 us, total = 183.406 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4321 total (1 active)
Queueing time: mean = 50.876 us, max = 11.213 ms, min = -0.000 s, total = 219.834 ms
Execution time:  mean = 472.871 us, total = 2.043 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1440 total (0 active), Execution time: mean = 39.662 us, total = 57.113 ms, Queueing time: mean = 48.873 us, max = 11.213 ms, min = 9.715 us, total = 70.377 ms
	CoreWorker.deadline_timer.flush_task_events - 1440 total (1 active), Execution time: mean = 331.594 us, total = 477.496 ms, Queueing time: mean = 103.772 us, max = 6.908 ms, min = -0.000 s, total = 149.432 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1440 total (0 active), Execution time: mean = 1.047 ms, total = 1.508 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0705729 MiB
	total number of task attempts sent: 537
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:13:20,813 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 88920 total (8 active)
Queueing time: mean = 262.302 us, max = 19.454 ms, min = -0.002 s, total = 23.324 s
Execution time:  mean = 73.691 ms, total = 6552.644 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 33658 total (0 active), Execution time: mean = 165.878 us, total = 5.583 s, Queueing time: mean = 631.359 us, max = 11.316 ms, min = 2.310 us, total = 21.250 s
	NodeManagerService.grpc_client.RequestWorkerLease - 33658 total (0 active), Execution time: mean = 70.776 ms, total = 2382.185 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 14983 total (1 active), Execution time: mean = 15.994 us, total = 239.638 ms, Queueing time: mean = 105.866 us, max = 19.454 ms, min = -0.002 s, total = 1.586 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1615 total (0 active), Execution time: mean = 1.053 ms, total = 1.701 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1615 total (0 active), Execution time: mean = 27.005 us, total = 43.613 ms, Queueing time: mean = 83.937 us, max = 5.249 ms, min = 9.737 us, total = 135.559 ms
	CoreWorker.InternalHeartbeat - 1500 total (1 active), Execution time: mean = 285.428 us, total = 428.143 ms, Queueing time: mean = 117.604 us, max = 7.858 ms, min = -0.000 s, total = 176.406 ms
	CoreWorker.RecordMetrics - 300 total (1 active), Execution time: mean = 50.639 us, total = 15.192 ms, Queueing time: mean = 93.943 us, max = 1.781 ms, min = 13.207 us, total = 28.183 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 183 total (0 active), Execution time: mean = 133.967 ns, total = 24.516 us, Queueing time: mean = 107.729 us, max = 1.121 ms, min = 36.158 us, total = 19.714 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 182 total (0 active), Execution time: mean = 383.202 us, total = 69.743 ms, Queueing time: mean = 76.219 us, max = 1.857 ms, min = 17.763 us, total = 13.872 ms
	CoreWorker.SubmitTask - 182 total (0 active), Execution time: mean = 156.391 us, total = 28.463 ms, Queueing time: mean = 275.236 us, max = 3.347 ms, min = 13.805 us, total = 50.093 ms
	NodeManagerService.grpc_client.ReturnWorker - 182 total (0 active), Execution time: mean = 1.764 ms, total = 321.043 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 182 total (0 active), Execution time: mean = 26.446 us, total = 4.813 ms, Queueing time: mean = 136.018 us, max = 11.316 ms, min = 10.869 us, total = 24.755 ms
	CoreWorkerService.grpc_client.PushTask - 182 total (0 active), Execution time: mean = 8.631 s, total = 1570.897 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 150 total (1 active), Execution time: mean = 12.197 us, total = 1.830 ms, Queueing time: mean = 107.387 us, max = 1.806 ms, min = -0.000 s, total = 16.108 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 100 total (0 active), Execution time: mean = 422.873 us, total = 42.287 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 100 total (0 active), Execution time: mean = 50.573 us, total = 5.057 ms, Queueing time: mean = 64.256 us, max = 395.873 us, min = 16.757 us, total = 6.426 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 30 total (0 active), Execution time: mean = 105.106 us, total = 3.153 ms, Queueing time: mean = 228.475 us, max = 334.311 us, min = 150.171 us, total = 6.854 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 29 total (1 active), Execution time: mean = 48.079 s, total = 1394.301 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 28 total (0 active), Execution time: mean = 251.614 us, total = 7.045 ms, Queueing time: mean = 122.409 us, max = 629.450 us, min = 16.141 us, total = 3.427 ms
	CoreWorker.PrintEventStats - 25 total (1 active, 1 running), Execution time: mean = 605.432 us, total = 15.136 ms, Queueing time: mean = 93.949 us, max = 348.672 us, min = 40.395 us, total = 2.349 ms
	Publisher.CheckDeadSubscribers - 6 total (1 active), Execution time: mean = 37.858 us, total = 227.149 us, Queueing time: mean = 66.228 us, max = 136.392 us, min = 16.954 us, total = 397.365 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 4 total (1 active), Execution time: mean = 299.185 s, total = 1196.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 4 total (0 active), Execution time: mean = 18.168 us, total = 72.673 us, Queueing time: mean = 45.852 us, max = 54.867 us, min = 36.430 us, total = 183.406 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4501 total (1 active)
Queueing time: mean = 51.693 us, max = 11.213 ms, min = -0.000 s, total = 232.668 ms
Execution time:  mean = 473.095 us, total = 2.129 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1500 total (0 active), Execution time: mean = 39.689 us, total = 59.533 ms, Queueing time: mean = 51.323 us, max = 11.213 ms, min = 9.715 us, total = 76.984 ms
	CoreWorker.deadline_timer.flush_task_events - 1500 total (1 active), Execution time: mean = 331.264 us, total = 496.896 ms, Queueing time: mean = 103.773 us, max = 6.908 ms, min = -0.000 s, total = 155.660 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1500 total (0 active), Execution time: mean = 1.048 ms, total = 1.573 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0705729 MiB
	total number of task attempts sent: 537
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:14:20,813 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 89727 total (8 active)
Queueing time: mean = 260.610 us, max = 19.454 ms, min = -0.002 s, total = 23.384 s
Execution time:  mean = 73.030 ms, total = 6552.733 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 33658 total (0 active), Execution time: mean = 165.878 us, total = 5.583 s, Queueing time: mean = 631.359 us, max = 11.316 ms, min = 2.310 us, total = 21.250 s
	NodeManagerService.grpc_client.RequestWorkerLease - 33658 total (0 active), Execution time: mean = 70.776 ms, total = 2382.185 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 15583 total (1 active), Execution time: mean = 15.999 us, total = 249.306 ms, Queueing time: mean = 105.028 us, max = 19.454 ms, min = -0.002 s, total = 1.637 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1675 total (0 active), Execution time: mean = 1.050 ms, total = 1.760 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1675 total (0 active), Execution time: mean = 27.109 us, total = 45.407 ms, Queueing time: mean = 82.172 us, max = 5.249 ms, min = 9.737 us, total = 137.638 ms
	CoreWorker.InternalHeartbeat - 1560 total (1 active), Execution time: mean = 285.317 us, total = 445.094 ms, Queueing time: mean = 116.618 us, max = 7.858 ms, min = -0.000 s, total = 181.924 ms
	CoreWorker.RecordMetrics - 312 total (1 active), Execution time: mean = 50.158 us, total = 15.649 ms, Queueing time: mean = 93.380 us, max = 1.781 ms, min = 13.207 us, total = 29.135 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 183 total (0 active), Execution time: mean = 133.967 ns, total = 24.516 us, Queueing time: mean = 107.729 us, max = 1.121 ms, min = 36.158 us, total = 19.714 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 182 total (0 active), Execution time: mean = 383.202 us, total = 69.743 ms, Queueing time: mean = 76.219 us, max = 1.857 ms, min = 17.763 us, total = 13.872 ms
	CoreWorker.SubmitTask - 182 total (0 active), Execution time: mean = 156.391 us, total = 28.463 ms, Queueing time: mean = 275.236 us, max = 3.347 ms, min = 13.805 us, total = 50.093 ms
	NodeManagerService.grpc_client.ReturnWorker - 182 total (0 active), Execution time: mean = 1.764 ms, total = 321.043 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 182 total (0 active), Execution time: mean = 26.446 us, total = 4.813 ms, Queueing time: mean = 136.018 us, max = 11.316 ms, min = 10.869 us, total = 24.755 ms
	CoreWorkerService.grpc_client.PushTask - 182 total (0 active), Execution time: mean = 8.631 s, total = 1570.897 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 156 total (1 active), Execution time: mean = 12.139 us, total = 1.894 ms, Queueing time: mean = 106.920 us, max = 1.806 ms, min = -0.000 s, total = 16.680 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 104 total (0 active), Execution time: mean = 418.299 us, total = 43.503 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 104 total (0 active), Execution time: mean = 50.421 us, total = 5.244 ms, Queueing time: mean = 63.414 us, max = 395.873 us, min = 16.757 us, total = 6.595 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 30 total (0 active), Execution time: mean = 105.106 us, total = 3.153 ms, Queueing time: mean = 228.475 us, max = 334.311 us, min = 150.171 us, total = 6.854 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 29 total (1 active), Execution time: mean = 48.079 s, total = 1394.301 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 28 total (0 active), Execution time: mean = 251.614 us, total = 7.045 ms, Queueing time: mean = 122.409 us, max = 629.450 us, min = 16.141 us, total = 3.427 ms
	CoreWorker.PrintEventStats - 26 total (1 active, 1 running), Execution time: mean = 610.145 us, total = 15.864 ms, Queueing time: mean = 93.210 us, max = 348.672 us, min = 40.395 us, total = 2.423 ms
	Publisher.CheckDeadSubscribers - 6 total (1 active), Execution time: mean = 37.858 us, total = 227.149 us, Queueing time: mean = 66.228 us, max = 136.392 us, min = 16.954 us, total = 397.365 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 4 total (1 active), Execution time: mean = 299.185 s, total = 1196.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 4 total (0 active), Execution time: mean = 18.168 us, total = 72.673 us, Queueing time: mean = 45.852 us, max = 54.867 us, min = 36.430 us, total = 183.406 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4681 total (1 active)
Queueing time: mean = 51.515 us, max = 11.213 ms, min = -0.000 s, total = 241.143 ms
Execution time:  mean = 473.090 us, total = 2.215 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1560 total (0 active), Execution time: mean = 39.730 us, total = 61.979 ms, Queueing time: mean = 50.992 us, max = 11.213 ms, min = 9.715 us, total = 79.547 ms
	CoreWorker.deadline_timer.flush_task_events - 1560 total (1 active), Execution time: mean = 331.895 us, total = 517.756 ms, Queueing time: mean = 103.572 us, max = 6.908 ms, min = -0.000 s, total = 161.572 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1560 total (0 active), Execution time: mean = 1.048 ms, total = 1.634 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0705729 MiB
	total number of task attempts sent: 537
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:15:20,814 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 90533 total (8 active)
Queueing time: mean = 259.098 us, max = 19.454 ms, min = -0.002 s, total = 23.457 s
Execution time:  mean = 72.380 ms, total = 6552.823 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 33658 total (0 active), Execution time: mean = 165.878 us, total = 5.583 s, Queueing time: mean = 631.359 us, max = 11.316 ms, min = 2.310 us, total = 21.250 s
	NodeManagerService.grpc_client.RequestWorkerLease - 33658 total (0 active), Execution time: mean = 70.776 ms, total = 2382.185 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 16182 total (1 active), Execution time: mean = 15.973 us, total = 258.468 ms, Queueing time: mean = 104.886 us, max = 19.454 ms, min = -0.002 s, total = 1.697 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1735 total (0 active), Execution time: mean = 1.048 ms, total = 1.819 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1735 total (0 active), Execution time: mean = 27.241 us, total = 47.264 ms, Queueing time: mean = 82.503 us, max = 5.249 ms, min = 9.737 us, total = 143.143 ms
	CoreWorker.InternalHeartbeat - 1620 total (1 active), Execution time: mean = 284.692 us, total = 461.201 ms, Queueing time: mean = 115.391 us, max = 7.858 ms, min = -0.000 s, total = 186.933 ms
	CoreWorker.RecordMetrics - 324 total (1 active), Execution time: mean = 49.711 us, total = 16.106 ms, Queueing time: mean = 93.117 us, max = 1.781 ms, min = 13.207 us, total = 30.170 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 183 total (0 active), Execution time: mean = 133.967 ns, total = 24.516 us, Queueing time: mean = 107.729 us, max = 1.121 ms, min = 36.158 us, total = 19.714 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 182 total (0 active), Execution time: mean = 383.202 us, total = 69.743 ms, Queueing time: mean = 76.219 us, max = 1.857 ms, min = 17.763 us, total = 13.872 ms
	CoreWorker.SubmitTask - 182 total (0 active), Execution time: mean = 156.391 us, total = 28.463 ms, Queueing time: mean = 275.236 us, max = 3.347 ms, min = 13.805 us, total = 50.093 ms
	NodeManagerService.grpc_client.ReturnWorker - 182 total (0 active), Execution time: mean = 1.764 ms, total = 321.043 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 182 total (0 active), Execution time: mean = 26.446 us, total = 4.813 ms, Queueing time: mean = 136.018 us, max = 11.316 ms, min = 10.869 us, total = 24.755 ms
	CoreWorkerService.grpc_client.PushTask - 182 total (0 active), Execution time: mean = 8.631 s, total = 1570.897 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 162 total (1 active), Execution time: mean = 12.149 us, total = 1.968 ms, Queueing time: mean = 106.755 us, max = 1.806 ms, min = -0.000 s, total = 17.294 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 108 total (0 active), Execution time: mean = 420.861 us, total = 45.453 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 108 total (0 active), Execution time: mean = 50.140 us, total = 5.415 ms, Queueing time: mean = 64.028 us, max = 395.873 us, min = 16.757 us, total = 6.915 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 30 total (0 active), Execution time: mean = 105.106 us, total = 3.153 ms, Queueing time: mean = 228.475 us, max = 334.311 us, min = 150.171 us, total = 6.854 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 29 total (1 active), Execution time: mean = 48.079 s, total = 1394.301 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 28 total (0 active), Execution time: mean = 251.614 us, total = 7.045 ms, Queueing time: mean = 122.409 us, max = 629.450 us, min = 16.141 us, total = 3.427 ms
	CoreWorker.PrintEventStats - 27 total (1 active, 1 running), Execution time: mean = 610.478 us, total = 16.483 ms, Queueing time: mean = 92.538 us, max = 348.672 us, min = 40.395 us, total = 2.499 ms
	Publisher.CheckDeadSubscribers - 6 total (1 active), Execution time: mean = 37.858 us, total = 227.149 us, Queueing time: mean = 66.228 us, max = 136.392 us, min = 16.954 us, total = 397.365 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 4 total (1 active), Execution time: mean = 299.185 s, total = 1196.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 4 total (0 active), Execution time: mean = 18.168 us, total = 72.673 us, Queueing time: mean = 45.852 us, max = 54.867 us, min = 36.430 us, total = 183.406 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4861 total (1 active)
Queueing time: mean = 51.081 us, max = 11.213 ms, min = -0.000 s, total = 248.305 ms
Execution time:  mean = 472.344 us, total = 2.296 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1620 total (0 active), Execution time: mean = 39.665 us, total = 64.257 ms, Queueing time: mean = 50.301 us, max = 11.213 ms, min = 9.715 us, total = 81.488 ms
	CoreWorker.deadline_timer.flush_task_events - 1620 total (1 active), Execution time: mean = 331.036 us, total = 536.278 ms, Queueing time: mean = 102.958 us, max = 6.908 ms, min = -0.000 s, total = 166.792 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1620 total (0 active), Execution time: mean = 1.046 ms, total = 1.695 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0705729 MiB
	total number of task attempts sent: 537
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:16:20,814 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 91339 total (8 active)
Queueing time: mean = 257.509 us, max = 19.454 ms, min = -0.002 s, total = 23.521 s
Execution time:  mean = 71.743 ms, total = 6552.914 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 33658 total (0 active), Execution time: mean = 165.878 us, total = 5.583 s, Queueing time: mean = 631.359 us, max = 11.316 ms, min = 2.310 us, total = 21.250 s
	NodeManagerService.grpc_client.RequestWorkerLease - 33658 total (0 active), Execution time: mean = 70.776 ms, total = 2382.185 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 16781 total (1 active), Execution time: mean = 15.959 us, total = 267.806 ms, Queueing time: mean = 104.320 us, max = 19.454 ms, min = -0.002 s, total = 1.751 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1795 total (0 active), Execution time: mean = 1.047 ms, total = 1.880 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1795 total (0 active), Execution time: mean = 27.269 us, total = 48.948 ms, Queueing time: mean = 80.866 us, max = 5.249 ms, min = 9.737 us, total = 145.154 ms
	CoreWorker.InternalHeartbeat - 1680 total (1 active), Execution time: mean = 284.034 us, total = 477.177 ms, Queueing time: mean = 115.063 us, max = 7.858 ms, min = -0.000 s, total = 193.306 ms
	CoreWorker.RecordMetrics - 336 total (1 active), Execution time: mean = 49.432 us, total = 16.609 ms, Queueing time: mean = 93.212 us, max = 1.781 ms, min = 13.207 us, total = 31.319 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 183 total (0 active), Execution time: mean = 133.967 ns, total = 24.516 us, Queueing time: mean = 107.729 us, max = 1.121 ms, min = 36.158 us, total = 19.714 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 182 total (0 active), Execution time: mean = 383.202 us, total = 69.743 ms, Queueing time: mean = 76.219 us, max = 1.857 ms, min = 17.763 us, total = 13.872 ms
	CoreWorker.SubmitTask - 182 total (0 active), Execution time: mean = 156.391 us, total = 28.463 ms, Queueing time: mean = 275.236 us, max = 3.347 ms, min = 13.805 us, total = 50.093 ms
	NodeManagerService.grpc_client.ReturnWorker - 182 total (0 active), Execution time: mean = 1.764 ms, total = 321.043 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 182 total (0 active), Execution time: mean = 26.446 us, total = 4.813 ms, Queueing time: mean = 136.018 us, max = 11.316 ms, min = 10.869 us, total = 24.755 ms
	CoreWorkerService.grpc_client.PushTask - 182 total (0 active), Execution time: mean = 8.631 s, total = 1570.897 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 168 total (1 active), Execution time: mean = 12.632 us, total = 2.122 ms, Queueing time: mean = 106.692 us, max = 1.806 ms, min = -0.000 s, total = 17.924 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 112 total (0 active), Execution time: mean = 418.856 us, total = 46.912 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 112 total (0 active), Execution time: mean = 49.764 us, total = 5.574 ms, Queueing time: mean = 62.753 us, max = 395.873 us, min = 16.757 us, total = 7.028 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 30 total (0 active), Execution time: mean = 105.106 us, total = 3.153 ms, Queueing time: mean = 228.475 us, max = 334.311 us, min = 150.171 us, total = 6.854 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 29 total (1 active), Execution time: mean = 48.079 s, total = 1394.301 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 28 total (0 active), Execution time: mean = 251.614 us, total = 7.045 ms, Queueing time: mean = 122.409 us, max = 629.450 us, min = 16.141 us, total = 3.427 ms
	CoreWorker.PrintEventStats - 28 total (1 active, 1 running), Execution time: mean = 608.910 us, total = 17.049 ms, Queueing time: mean = 92.404 us, max = 348.672 us, min = 40.395 us, total = 2.587 ms
	Publisher.CheckDeadSubscribers - 6 total (1 active), Execution time: mean = 37.858 us, total = 227.149 us, Queueing time: mean = 66.228 us, max = 136.392 us, min = 16.954 us, total = 397.365 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 4 total (1 active), Execution time: mean = 299.185 s, total = 1196.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 4 total (0 active), Execution time: mean = 18.168 us, total = 72.673 us, Queueing time: mean = 45.852 us, max = 54.867 us, min = 36.430 us, total = 183.406 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 5041 total (1 active)
Queueing time: mean = 50.522 us, max = 11.213 ms, min = -0.000 s, total = 254.682 ms
Execution time:  mean = 471.105 us, total = 2.375 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1680 total (0 active), Execution time: mean = 39.747 us, total = 66.775 ms, Queueing time: mean = 49.643 us, max = 11.213 ms, min = 9.715 us, total = 83.401 ms
	CoreWorker.deadline_timer.flush_task_events - 1680 total (1 active), Execution time: mean = 330.329 us, total = 554.953 ms, Queueing time: mean = 101.938 us, max = 6.908 ms, min = -0.000 s, total = 171.256 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1680 total (0 active), Execution time: mean = 1.043 ms, total = 1.753 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0705729 MiB
	total number of task attempts sent: 537
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:17:20,815 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 92145 total (8 active)
Queueing time: mean = 256.335 us, max = 44.275 ms, min = -0.002 s, total = 23.620 s
Execution time:  mean = 71.116 ms, total = 6552.998 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 33658 total (0 active), Execution time: mean = 165.878 us, total = 5.583 s, Queueing time: mean = 631.359 us, max = 11.316 ms, min = 2.310 us, total = 21.250 s
	NodeManagerService.grpc_client.RequestWorkerLease - 33658 total (0 active), Execution time: mean = 70.776 ms, total = 2382.185 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 17380 total (1 active), Execution time: mean = 15.924 us, total = 276.762 ms, Queueing time: mean = 105.951 us, max = 44.275 ms, min = -0.002 s, total = 1.841 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1855 total (0 active), Execution time: mean = 1.044 ms, total = 1.936 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1855 total (0 active), Execution time: mean = 27.284 us, total = 50.612 ms, Queueing time: mean = 79.449 us, max = 5.249 ms, min = 9.737 us, total = 147.378 ms
	CoreWorker.InternalHeartbeat - 1740 total (1 active), Execution time: mean = 283.126 us, total = 492.640 ms, Queueing time: mean = 113.777 us, max = 7.858 ms, min = -0.000 s, total = 197.972 ms
	CoreWorker.RecordMetrics - 348 total (1 active), Execution time: mean = 49.140 us, total = 17.101 ms, Queueing time: mean = 92.635 us, max = 1.781 ms, min = 13.207 us, total = 32.237 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 183 total (0 active), Execution time: mean = 133.967 ns, total = 24.516 us, Queueing time: mean = 107.729 us, max = 1.121 ms, min = 36.158 us, total = 19.714 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 182 total (0 active), Execution time: mean = 383.202 us, total = 69.743 ms, Queueing time: mean = 76.219 us, max = 1.857 ms, min = 17.763 us, total = 13.872 ms
	CoreWorker.SubmitTask - 182 total (0 active), Execution time: mean = 156.391 us, total = 28.463 ms, Queueing time: mean = 275.236 us, max = 3.347 ms, min = 13.805 us, total = 50.093 ms
	NodeManagerService.grpc_client.ReturnWorker - 182 total (0 active), Execution time: mean = 1.764 ms, total = 321.043 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 182 total (0 active), Execution time: mean = 26.446 us, total = 4.813 ms, Queueing time: mean = 136.018 us, max = 11.316 ms, min = 10.869 us, total = 24.755 ms
	CoreWorkerService.grpc_client.PushTask - 182 total (0 active), Execution time: mean = 8.631 s, total = 1570.897 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 174 total (1 active), Execution time: mean = 12.547 us, total = 2.183 ms, Queueing time: mean = 105.858 us, max = 1.806 ms, min = -0.000 s, total = 18.419 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 116 total (0 active), Execution time: mean = 415.786 us, total = 48.231 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 116 total (0 active), Execution time: mean = 49.330 us, total = 5.722 ms, Queueing time: mean = 62.013 us, max = 395.873 us, min = 16.757 us, total = 7.194 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 30 total (0 active), Execution time: mean = 105.106 us, total = 3.153 ms, Queueing time: mean = 228.475 us, max = 334.311 us, min = 150.171 us, total = 6.854 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 29 total (1 active), Execution time: mean = 48.079 s, total = 1394.301 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 29 total (1 active, 1 running), Execution time: mean = 606.430 us, total = 17.586 ms, Queueing time: mean = 92.110 us, max = 348.672 us, min = 40.395 us, total = 2.671 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 28 total (0 active), Execution time: mean = 251.614 us, total = 7.045 ms, Queueing time: mean = 122.409 us, max = 629.450 us, min = 16.141 us, total = 3.427 ms
	Publisher.CheckDeadSubscribers - 6 total (1 active), Execution time: mean = 37.858 us, total = 227.149 us, Queueing time: mean = 66.228 us, max = 136.392 us, min = 16.954 us, total = 397.365 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 4 total (1 active), Execution time: mean = 299.185 s, total = 1196.742 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 4 total (0 active), Execution time: mean = 18.168 us, total = 72.673 us, Queueing time: mean = 45.852 us, max = 54.867 us, min = 36.430 us, total = 183.406 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 5221 total (1 active)
Queueing time: mean = 50.411 us, max = 11.213 ms, min = -0.000 s, total = 263.196 ms
Execution time:  mean = 469.830 us, total = 2.453 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1740 total (0 active), Execution time: mean = 39.757 us, total = 69.177 ms, Queueing time: mean = 50.317 us, max = 11.213 ms, min = 9.715 us, total = 87.552 ms
	CoreWorker.deadline_timer.flush_task_events - 1740 total (1 active), Execution time: mean = 329.386 us, total = 573.132 ms, Queueing time: mean = 100.931 us, max = 6.908 ms, min = -0.000 s, total = 175.619 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1740 total (0 active), Execution time: mean = 1.040 ms, total = 1.810 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0705729 MiB
	total number of task attempts sent: 537
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:18:08,596 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:18:20,816 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 93106 total (20 active)
Queueing time: mean = 254.402 us, max = 44.275 ms, min = -0.002 s, total = 23.686 s
Execution time:  mean = 80.992 ms, total = 7540.808 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease - 33688 total (10 active), Execution time: mean = 71.468 ms, total = 2407.629 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 33678 total (0 active), Execution time: mean = 166.056 us, total = 5.592 s, Queueing time: mean = 631.035 us, max = 11.316 ms, min = 2.310 us, total = 21.252 s
	CoreWorker.RecoverObjects - 17980 total (1 active), Execution time: mean = 15.854 us, total = 285.054 ms, Queueing time: mean = 105.206 us, max = 44.275 ms, min = -0.002 s, total = 1.892 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1920 total (0 active), Execution time: mean = 1.044 ms, total = 2.005 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1920 total (0 active), Execution time: mean = 27.271 us, total = 52.361 ms, Queueing time: mean = 78.389 us, max = 5.249 ms, min = 9.737 us, total = 150.506 ms
	CoreWorker.InternalHeartbeat - 1800 total (1 active), Execution time: mean = 283.680 us, total = 510.625 ms, Queueing time: mean = 112.882 us, max = 7.858 ms, min = -0.000 s, total = 203.188 ms
	CoreWorker.RecordMetrics - 360 total (1 active), Execution time: mean = 49.237 us, total = 17.725 ms, Queueing time: mean = 91.797 us, max = 1.781 ms, min = 13.207 us, total = 33.047 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	CoreWorkerService.grpc_client.PushTask - 187 total (2 active), Execution time: mean = 8.489 s, total = 1587.422 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerMemoryStore.Put.get_async_callbacks - 186 total (0 active), Execution time: mean = 133.935 ns, total = 24.912 us, Queueing time: mean = 107.407 us, max = 1.121 ms, min = 36.158 us, total = 19.978 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 185 total (0 active), Execution time: mean = 382.658 us, total = 70.792 ms, Queueing time: mean = 75.630 us, max = 1.857 ms, min = 17.763 us, total = 13.992 ms
	NodeManagerService.grpc_client.ReturnWorker - 185 total (0 active), Execution time: mean = 1.762 ms, total = 325.970 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 185 total (0 active), Execution time: mean = 26.425 us, total = 4.889 ms, Queueing time: mean = 134.911 us, max = 11.316 ms, min = 10.869 us, total = 24.959 ms
	CoreWorker.TryDelPendingObjectRefStreams - 180 total (1 active), Execution time: mean = 12.468 us, total = 2.244 ms, Queueing time: mean = 107.674 us, max = 1.806 ms, min = -0.000 s, total = 19.381 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 120 total (0 active), Execution time: mean = 48.619 us, total = 5.834 ms, Queueing time: mean = 62.977 us, max = 395.873 us, min = 16.757 us, total = 7.557 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 120 total (0 active), Execution time: mean = 413.453 us, total = 49.614 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 30 total (1 active), Execution time: mean = 58.001 s, total = 1740.024 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 30 total (0 active), Execution time: mean = 105.106 us, total = 3.153 ms, Queueing time: mean = 228.475 us, max = 334.311 us, min = 150.171 us, total = 6.854 ms
	CoreWorker.PrintEventStats - 30 total (1 active, 1 running), Execution time: mean = 617.707 us, total = 18.531 ms, Queueing time: mean = 91.081 us, max = 348.672 us, min = 40.395 us, total = 2.732 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 29 total (0 active), Execution time: mean = 247.626 us, total = 7.181 ms, Queueing time: mean = 120.297 us, max = 629.450 us, min = 16.141 us, total = 3.489 ms
	Publisher.CheckDeadSubscribers - 7 total (1 active), Execution time: mean = 37.787 us, total = 264.507 us, Queueing time: mean = 76.069 us, max = 136.392 us, min = 16.954 us, total = 532.484 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 5 total (1 active), Execution time: mean = 359.348 s, total = 1796.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 5 total (0 active), Execution time: mean = 17.457 us, total = 87.286 us, Queueing time: mean = 40.735 us, max = 54.867 us, min = 20.269 us, total = 203.675 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 5401 total (1 active)
Queueing time: mean = 49.859 us, max = 11.213 ms, min = -0.000 s, total = 269.288 ms
Execution time:  mean = 468.666 us, total = 2.531 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1800 total (0 active), Execution time: mean = 39.876 us, total = 71.776 ms, Queueing time: mean = 49.768 us, max = 11.213 ms, min = 9.715 us, total = 89.583 ms
	CoreWorker.deadline_timer.flush_task_events - 1800 total (1 active), Execution time: mean = 328.746 us, total = 591.743 ms, Queueing time: mean = 99.823 us, max = 6.908 ms, min = -0.000 s, total = 179.681 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1800 total (0 active), Execution time: mean = 1.037 ms, total = 1.867 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0880804 MiB
	total number of task attempts sent: 616
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:18:34,283 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:18:34,283 I 693801 693872] core_worker.cc:5107: Number of alive nodes:3
[2025-06-26 01:18:34,291 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:18:35,715 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:18:35,718 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:18:36,626 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:18:36,629 I 693801 693872] normal_task_submitter.cc:239: Connecting to raylet 20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:19:20,817 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 129069 total (26 active)
Queueing time: mean = 282.731 us, max = 44.275 ms, min = -0.002 s, total = 36.492 s
Execution time:  mean = 66.998 ms, total = 8647.313 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease - 51092 total (10 active), Execution time: mean = 60.298 ms, total = 3080.738 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 51082 total (0 active), Execution time: mean = 161.473 us, total = 8.248 s, Queueing time: mean = 663.746 us, max = 11.316 ms, min = 2.310 us, total = 33.905 s
	CoreWorker.RecoverObjects - 18579 total (1 active), Execution time: mean = 15.807 us, total = 293.678 ms, Queueing time: mean = 106.453 us, max = 44.275 ms, min = -0.002 s, total = 1.978 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2032 total (0 active), Execution time: mean = 1.041 ms, total = 2.115 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2032 total (0 active), Execution time: mean = 26.845 us, total = 54.549 ms, Queueing time: mean = 90.016 us, max = 5.249 ms, min = 8.744 us, total = 182.912 ms
	CoreWorker.InternalHeartbeat - 1860 total (1 active), Execution time: mean = 285.427 us, total = 530.895 ms, Queueing time: mean = 112.996 us, max = 7.858 ms, min = -0.000 s, total = 210.173 ms
	CoreWorker.RecordMetrics - 372 total (1 active), Execution time: mean = 50.220 us, total = 18.682 ms, Queueing time: mean = 91.569 us, max = 1.781 ms, min = 13.207 us, total = 34.064 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	CoreWorkerService.grpc_client.PushTask - 239 total (8 active), Execution time: mean = 8.126 s, total = 1942.127 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerMemoryStore.Put.get_async_callbacks - 232 total (0 active), Execution time: mean = 170.233 ns, total = 39.494 us, Queueing time: mean = 118.236 us, max = 1.254 ms, min = 36.158 us, total = 27.431 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 231 total (0 active), Execution time: mean = 372.662 us, total = 86.085 ms, Queueing time: mean = 90.476 us, max = 1.857 ms, min = 16.717 us, total = 20.900 ms
	NodeManagerService.grpc_client.ReturnWorker - 231 total (0 active), Execution time: mean = 1.706 ms, total = 394.194 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 231 total (0 active), Execution time: mean = 26.069 us, total = 6.022 ms, Queueing time: mean = 121.667 us, max = 11.316 ms, min = 10.869 us, total = 28.105 ms
	CoreWorker.TryDelPendingObjectRefStreams - 186 total (1 active), Execution time: mean = 12.378 us, total = 2.302 ms, Queueing time: mean = 136.088 us, max = 5.352 ms, min = -0.000 s, total = 25.312 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 124 total (0 active), Execution time: mean = 48.615 us, total = 6.028 ms, Queueing time: mean = 66.111 us, max = 395.873 us, min = 16.757 us, total = 8.198 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 124 total (0 active), Execution time: mean = 417.946 us, total = 51.825 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 33 total (1 active), Execution time: mean = 55.025 s, total = 1815.828 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 33 total (0 active), Execution time: mean = 101.264 us, total = 3.342 ms, Queueing time: mean = 223.887 us, max = 334.311 us, min = 142.411 us, total = 7.388 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 32 total (0 active), Execution time: mean = 242.350 us, total = 7.755 ms, Queueing time: mean = 131.727 us, max = 629.450 us, min = 16.141 us, total = 4.215 ms
	CoreWorker.PrintEventStats - 31 total (1 active, 1 running), Execution time: mean = 622.673 us, total = 19.303 ms, Queueing time: mean = 89.634 us, max = 348.672 us, min = 40.395 us, total = 2.779 ms
	Publisher.CheckDeadSubscribers - 7 total (1 active), Execution time: mean = 37.787 us, total = 264.507 us, Queueing time: mean = 76.069 us, max = 136.392 us, min = 16.954 us, total = 532.484 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 5 total (1 active), Execution time: mean = 359.348 s, total = 1796.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 5 total (0 active), Execution time: mean = 17.457 us, total = 87.286 us, Queueing time: mean = 40.735 us, max = 54.867 us, min = 20.269 us, total = 203.675 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 5581 total (1 active)
Queueing time: mean = 49.383 us, max = 11.213 ms, min = -0.000 s, total = 275.606 ms
Execution time:  mean = 467.139 us, total = 2.607 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1860 total (0 active), Execution time: mean = 39.922 us, total = 74.255 ms, Queueing time: mean = 49.162 us, max = 11.213 ms, min = 9.715 us, total = 91.441 ms
	CoreWorker.deadline_timer.flush_task_events - 1860 total (1 active), Execution time: mean = 327.977 us, total = 610.036 ms, Queueing time: mean = 99.000 us, max = 6.908 ms, min = -0.000 s, total = 184.141 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1860 total (0 active), Execution time: mean = 1.034 ms, total = 1.922 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0957289 MiB
	total number of task attempts sent: 714
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:20:20,817 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 138078 total (8 active)
Queueing time: mean = 281.799 us, max = 44.275 ms, min = -0.002 s, total = 38.910 s
Execution time:  mean = 64.501 ms, total = 8906.107 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 19178 total (1 active), Execution time: mean = 15.785 us, total = 302.716 ms, Queueing time: mean = 106.928 us, max = 44.275 ms, min = -0.002 s, total = 2.051 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2097 total (0 active), Execution time: mean = 1.044 ms, total = 2.189 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2097 total (0 active), Execution time: mean = 26.991 us, total = 56.600 ms, Queueing time: mean = 89.746 us, max = 5.249 ms, min = 8.744 us, total = 188.198 ms
	CoreWorker.InternalHeartbeat - 1920 total (1 active), Execution time: mean = 285.802 us, total = 548.740 ms, Queueing time: mean = 112.840 us, max = 7.858 ms, min = -0.000 s, total = 216.653 ms
	CoreWorker.RecordMetrics - 384 total (1 active), Execution time: mean = 50.589 us, total = 19.426 ms, Queueing time: mean = 90.628 us, max = 1.781 ms, min = 13.207 us, total = 34.801 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 192 total (1 active), Execution time: mean = 12.333 us, total = 2.368 ms, Queueing time: mean = 134.130 us, max = 5.352 ms, min = -0.000 s, total = 25.753 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 128 total (0 active), Execution time: mean = 416.734 us, total = 53.342 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 128 total (0 active), Execution time: mean = 48.624 us, total = 6.224 ms, Queueing time: mean = 67.774 us, max = 395.873 us, min = 16.757 us, total = 8.675 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 33 total (1 active), Execution time: mean = 55.025 s, total = 1815.828 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 33 total (0 active), Execution time: mean = 101.264 us, total = 3.342 ms, Queueing time: mean = 223.887 us, max = 334.311 us, min = 142.411 us, total = 7.388 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 32 total (0 active), Execution time: mean = 242.350 us, total = 7.755 ms, Queueing time: mean = 131.727 us, max = 629.450 us, min = 16.141 us, total = 4.215 ms
	CoreWorker.PrintEventStats - 32 total (1 active, 1 running), Execution time: mean = 618.620 us, total = 19.796 ms, Queueing time: mean = 89.382 us, max = 348.672 us, min = 40.395 us, total = 2.860 ms
	Publisher.CheckDeadSubscribers - 7 total (1 active), Execution time: mean = 37.787 us, total = 264.507 us, Queueing time: mean = 76.069 us, max = 136.392 us, min = 16.954 us, total = 532.484 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 5 total (1 active), Execution time: mean = 359.348 s, total = 1796.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 5 total (0 active), Execution time: mean = 17.457 us, total = 87.286 us, Queueing time: mean = 40.735 us, max = 54.867 us, min = 20.269 us, total = 203.675 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 5761 total (1 active)
Queueing time: mean = 50.345 us, max = 11.213 ms, min = -0.000 s, total = 290.038 ms
Execution time:  mean = 469.716 us, total = 2.706 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1920 total (0 active), Execution time: mean = 40.025 us, total = 76.847 ms, Queueing time: mean = 49.599 us, max = 11.213 ms, min = 9.715 us, total = 95.231 ms
	CoreWorker.deadline_timer.flush_task_events - 1920 total (1 active), Execution time: mean = 327.888 us, total = 629.546 ms, Queueing time: mean = 101.449 us, max = 6.908 ms, min = -0.000 s, total = 194.783 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1920 total (0 active), Execution time: mean = 1.041 ms, total = 1.999 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:20:38,140 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:20:38,140 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:20:38,140 I 693801 693872] core_worker.cc:5107: Number of alive nodes:4
[2025-06-26 01:20:43,257 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:20:43,257 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:20:43,260 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:20:43,261 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:21:05,529 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=1debeedd775f559e844e3f300a626c3ae54f84472517a3fe5853f7f0
[2025-06-26 01:21:08,662 I 693801 693872] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:21:08,662 I 693801 693872] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:21:20,818 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 138900 total (8 active)
Queueing time: mean = 280.632 us, max = 44.275 ms, min = -0.002 s, total = 38.980 s
Execution time:  mean = 65.214 ms, total = 9058.228 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 19778 total (1 active), Execution time: mean = 15.782 us, total = 312.136 ms, Queueing time: mean = 106.518 us, max = 44.275 ms, min = -0.002 s, total = 2.107 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2157 total (0 active), Execution time: mean = 1.042 ms, total = 2.247 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2157 total (0 active), Execution time: mean = 27.104 us, total = 58.464 ms, Queueing time: mean = 88.242 us, max = 5.249 ms, min = 8.744 us, total = 190.338 ms
	CoreWorker.InternalHeartbeat - 1980 total (1 active), Execution time: mean = 285.439 us, total = 565.169 ms, Queueing time: mean = 111.889 us, max = 7.858 ms, min = -0.000 s, total = 221.541 ms
	CoreWorker.RecordMetrics - 396 total (1 active), Execution time: mean = 50.429 us, total = 19.970 ms, Queueing time: mean = 89.992 us, max = 1.781 ms, min = 13.207 us, total = 35.637 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 198 total (1 active), Execution time: mean = 12.299 us, total = 2.435 ms, Queueing time: mean = 132.329 us, max = 5.352 ms, min = -0.000 s, total = 26.201 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 132 total (0 active), Execution time: mean = 413.416 us, total = 54.571 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 132 total (0 active), Execution time: mean = 48.121 us, total = 6.352 ms, Queueing time: mean = 67.819 us, max = 395.873 us, min = 16.757 us, total = 8.952 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 38 total (1 active), Execution time: mean = 51.786 s, total = 1967.860 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 37 total (0 active), Execution time: mean = 233.062 us, total = 8.623 ms, Queueing time: mean = 219.107 us, max = 3.073 ms, min = 16.141 us, total = 8.107 ms
	CoreWorker.PrintEventStats - 33 total (1 active, 1 running), Execution time: mean = 614.729 us, total = 20.286 ms, Queueing time: mean = 89.216 us, max = 348.672 us, min = 40.395 us, total = 2.944 ms
	Publisher.CheckDeadSubscribers - 7 total (1 active), Execution time: mean = 37.787 us, total = 264.507 us, Queueing time: mean = 76.069 us, max = 136.392 us, min = 16.954 us, total = 532.484 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 5 total (1 active), Execution time: mean = 359.348 s, total = 1796.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 5 total (0 active), Execution time: mean = 17.457 us, total = 87.286 us, Queueing time: mean = 40.735 us, max = 54.867 us, min = 20.269 us, total = 203.675 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 5941 total (1 active)
Queueing time: mean = 49.957 us, max = 11.213 ms, min = -0.000 s, total = 296.794 ms
Execution time:  mean = 468.927 us, total = 2.786 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1980 total (0 active), Execution time: mean = 40.063 us, total = 79.324 ms, Queueing time: mean = 49.140 us, max = 11.213 ms, min = 9.715 us, total = 97.297 ms
	CoreWorker.deadline_timer.flush_task_events - 1980 total (1 active), Execution time: mean = 327.679 us, total = 648.804 ms, Queueing time: mean = 100.744 us, max = 6.908 ms, min = -0.000 s, total = 199.473 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1980 total (0 active), Execution time: mean = 1.039 ms, total = 2.057 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:22:20,819 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 139706 total (8 active)
Queueing time: mean = 279.413 us, max = 44.275 ms, min = -0.002 s, total = 39.036 s
Execution time:  mean = 64.838 ms, total = 9058.313 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 20377 total (1 active), Execution time: mean = 15.754 us, total = 321.017 ms, Queueing time: mean = 105.651 us, max = 44.275 ms, min = -0.002 s, total = 2.153 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2217 total (0 active), Execution time: mean = 1.038 ms, total = 2.302 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2217 total (0 active), Execution time: mean = 27.167 us, total = 60.230 ms, Queueing time: mean = 86.971 us, max = 5.249 ms, min = 8.744 us, total = 192.814 ms
	CoreWorker.InternalHeartbeat - 2040 total (1 active), Execution time: mean = 285.128 us, total = 581.661 ms, Queueing time: mean = 111.266 us, max = 7.858 ms, min = -0.000 s, total = 226.983 ms
	CoreWorker.RecordMetrics - 408 total (1 active), Execution time: mean = 50.156 us, total = 20.464 ms, Queueing time: mean = 89.728 us, max = 1.781 ms, min = 13.207 us, total = 36.609 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 204 total (1 active), Execution time: mean = 12.234 us, total = 2.496 ms, Queueing time: mean = 130.076 us, max = 5.352 ms, min = -0.000 s, total = 26.536 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 136 total (0 active), Execution time: mean = 409.664 us, total = 55.714 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 136 total (0 active), Execution time: mean = 47.577 us, total = 6.470 ms, Queueing time: mean = 69.199 us, max = 395.873 us, min = 16.757 us, total = 9.411 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 38 total (1 active), Execution time: mean = 51.786 s, total = 1967.860 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 37 total (0 active), Execution time: mean = 233.062 us, total = 8.623 ms, Queueing time: mean = 219.107 us, max = 3.073 ms, min = 16.141 us, total = 8.107 ms
	CoreWorker.PrintEventStats - 34 total (1 active, 1 running), Execution time: mean = 619.052 us, total = 21.048 ms, Queueing time: mean = 89.106 us, max = 348.672 us, min = 40.395 us, total = 3.030 ms
	Publisher.CheckDeadSubscribers - 7 total (1 active), Execution time: mean = 37.787 us, total = 264.507 us, Queueing time: mean = 76.069 us, max = 136.392 us, min = 16.954 us, total = 532.484 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 5 total (1 active), Execution time: mean = 359.348 s, total = 1796.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 5 total (0 active), Execution time: mean = 17.457 us, total = 87.286 us, Queueing time: mean = 40.735 us, max = 54.867 us, min = 20.269 us, total = 203.675 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 6121 total (1 active)
Queueing time: mean = 49.444 us, max = 11.213 ms, min = -0.000 s, total = 302.648 ms
Execution time:  mean = 467.811 us, total = 2.863 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 2040 total (0 active), Execution time: mean = 39.994 us, total = 81.587 ms, Queueing time: mean = 48.561 us, max = 11.213 ms, min = 9.715 us, total = 99.064 ms
	CoreWorker.deadline_timer.flush_task_events - 2040 total (1 active), Execution time: mean = 326.932 us, total = 666.942 ms, Queueing time: mean = 99.784 us, max = 6.908 ms, min = -0.000 s, total = 203.559 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 2040 total (0 active), Execution time: mean = 1.037 ms, total = 2.115 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:23:20,820 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 140514 total (8 active)
Queueing time: mean = 278.197 us, max = 44.275 ms, min = -0.002 s, total = 39.091 s
Execution time:  mean = 64.466 ms, total = 9058.399 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 20977 total (1 active), Execution time: mean = 15.749 us, total = 330.368 ms, Queueing time: mean = 104.813 us, max = 44.275 ms, min = -0.002 s, total = 2.199 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2277 total (0 active), Execution time: mean = 1.035 ms, total = 2.358 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2277 total (0 active), Execution time: mean = 27.162 us, total = 61.849 ms, Queueing time: mean = 85.505 us, max = 5.249 ms, min = 8.744 us, total = 194.694 ms
	CoreWorker.InternalHeartbeat - 2100 total (1 active), Execution time: mean = 284.649 us, total = 597.763 ms, Queueing time: mean = 110.346 us, max = 7.858 ms, min = -0.000 s, total = 231.726 ms
	CoreWorker.RecordMetrics - 420 total (1 active), Execution time: mean = 49.851 us, total = 20.937 ms, Queueing time: mean = 90.844 us, max = 1.781 ms, min = 13.207 us, total = 38.154 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 210 total (1 active), Execution time: mean = 12.216 us, total = 2.565 ms, Queueing time: mean = 128.921 us, max = 5.352 ms, min = -0.000 s, total = 27.073 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 140 total (0 active), Execution time: mean = 409.974 us, total = 57.396 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 140 total (0 active), Execution time: mean = 47.032 us, total = 6.584 ms, Queueing time: mean = 68.969 us, max = 395.873 us, min = 16.757 us, total = 9.656 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 38 total (1 active), Execution time: mean = 51.786 s, total = 1967.860 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 37 total (0 active), Execution time: mean = 233.062 us, total = 8.623 ms, Queueing time: mean = 219.107 us, max = 3.073 ms, min = 16.141 us, total = 8.107 ms
	CoreWorker.PrintEventStats - 35 total (1 active, 1 running), Execution time: mean = 617.229 us, total = 21.603 ms, Queueing time: mean = 88.618 us, max = 348.672 us, min = 40.395 us, total = 3.102 ms
	Publisher.CheckDeadSubscribers - 8 total (1 active), Execution time: mean = 34.782 us, total = 278.252 us, Queueing time: mean = 76.331 us, max = 136.392 us, min = 16.954 us, total = 610.644 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 5 total (1 active), Execution time: mean = 359.348 s, total = 1796.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 5 total (0 active), Execution time: mean = 17.457 us, total = 87.286 us, Queueing time: mean = 40.735 us, max = 54.867 us, min = 20.269 us, total = 203.675 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 6301 total (1 active)
Queueing time: mean = 49.260 us, max = 11.213 ms, min = -0.000 s, total = 310.386 ms
Execution time:  mean = 466.860 us, total = 2.942 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 2100 total (0 active), Execution time: mean = 40.050 us, total = 84.104 ms, Queueing time: mean = 48.301 us, max = 11.213 ms, min = 9.715 us, total = 101.431 ms
	CoreWorker.deadline_timer.flush_task_events - 2100 total (1 active), Execution time: mean = 326.490 us, total = 685.630 ms, Queueing time: mean = 99.491 us, max = 6.908 ms, min = -0.000 s, total = 208.930 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 2100 total (0 active), Execution time: mean = 1.034 ms, total = 2.172 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:24:20,821 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 141320 total (8 active)
Queueing time: mean = 277.029 us, max = 44.275 ms, min = -0.002 s, total = 39.150 s
Execution time:  mean = 64.099 ms, total = 9058.484 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 21576 total (1 active), Execution time: mean = 15.735 us, total = 339.499 ms, Queueing time: mean = 104.285 us, max = 44.275 ms, min = -0.002 s, total = 2.250 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2337 total (0 active), Execution time: mean = 1.033 ms, total = 2.414 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2337 total (0 active), Execution time: mean = 27.171 us, total = 63.498 ms, Queueing time: mean = 84.080 us, max = 5.249 ms, min = 8.744 us, total = 196.495 ms
	CoreWorker.InternalHeartbeat - 2160 total (1 active), Execution time: mean = 284.288 us, total = 614.062 ms, Queueing time: mean = 109.323 us, max = 7.858 ms, min = -0.000 s, total = 236.138 ms
	CoreWorker.RecordMetrics - 432 total (1 active), Execution time: mean = 49.681 us, total = 21.462 ms, Queueing time: mean = 90.858 us, max = 1.781 ms, min = 13.207 us, total = 39.251 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 216 total (1 active), Execution time: mean = 12.125 us, total = 2.619 ms, Queueing time: mean = 126.775 us, max = 5.352 ms, min = -0.000 s, total = 27.383 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 144 total (0 active), Execution time: mean = 405.564 us, total = 58.401 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 144 total (0 active), Execution time: mean = 46.509 us, total = 6.697 ms, Queueing time: mean = 68.175 us, max = 395.873 us, min = 16.757 us, total = 9.817 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 38 total (1 active), Execution time: mean = 51.786 s, total = 1967.860 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 37 total (0 active), Execution time: mean = 233.062 us, total = 8.623 ms, Queueing time: mean = 219.107 us, max = 3.073 ms, min = 16.141 us, total = 8.107 ms
	CoreWorker.PrintEventStats - 36 total (1 active, 1 running), Execution time: mean = 624.610 us, total = 22.486 ms, Queueing time: mean = 88.592 us, max = 348.672 us, min = 40.395 us, total = 3.189 ms
	Publisher.CheckDeadSubscribers - 8 total (1 active), Execution time: mean = 34.782 us, total = 278.252 us, Queueing time: mean = 76.331 us, max = 136.392 us, min = 16.954 us, total = 610.644 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 5 total (1 active), Execution time: mean = 359.348 s, total = 1796.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 5 total (0 active), Execution time: mean = 17.457 us, total = 87.286 us, Queueing time: mean = 40.735 us, max = 54.867 us, min = 20.269 us, total = 203.675 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 6481 total (1 active)
Queueing time: mean = 48.920 us, max = 11.213 ms, min = -0.000 s, total = 317.052 ms
Execution time:  mean = 466.504 us, total = 3.023 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 2160 total (0 active), Execution time: mean = 40.150 us, total = 86.724 ms, Queueing time: mean = 47.857 us, max = 11.213 ms, min = 9.715 us, total = 103.372 ms
	CoreWorker.deadline_timer.flush_task_events - 2160 total (1 active), Execution time: mean = 326.080 us, total = 704.333 ms, Queueing time: mean = 98.914 us, max = 6.908 ms, min = -0.000 s, total = 213.655 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 2160 total (0 active), Execution time: mean = 1.033 ms, total = 2.232 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:25:20,821 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 142126 total (8 active)
Queueing time: mean = 276.047 us, max = 44.275 ms, min = -0.002 s, total = 39.233 s
Execution time:  mean = 63.736 ms, total = 9058.577 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 22175 total (1 active), Execution time: mean = 15.740 us, total = 349.033 ms, Queueing time: mean = 104.837 us, max = 44.275 ms, min = -0.002 s, total = 2.325 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2397 total (0 active), Execution time: mean = 1.033 ms, total = 2.475 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2397 total (0 active), Execution time: mean = 27.317 us, total = 65.480 ms, Queueing time: mean = 83.030 us, max = 5.249 ms, min = 8.744 us, total = 199.023 ms
	CoreWorker.InternalHeartbeat - 2220 total (1 active), Execution time: mean = 283.706 us, total = 629.827 ms, Queueing time: mean = 108.559 us, max = 7.858 ms, min = -0.000 s, total = 241.001 ms
	CoreWorker.RecordMetrics - 444 total (1 active), Execution time: mean = 49.527 us, total = 21.990 ms, Queueing time: mean = 90.314 us, max = 1.781 ms, min = 13.207 us, total = 40.100 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 222 total (1 active), Execution time: mean = 12.106 us, total = 2.687 ms, Queueing time: mean = 125.439 us, max = 5.352 ms, min = -0.000 s, total = 27.847 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 148 total (0 active), Execution time: mean = 411.086 us, total = 60.841 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 148 total (0 active), Execution time: mean = 46.357 us, total = 6.861 ms, Queueing time: mean = 67.630 us, max = 395.873 us, min = 16.757 us, total = 10.009 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 38 total (1 active), Execution time: mean = 51.786 s, total = 1967.860 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 37 total (0 active), Execution time: mean = 233.062 us, total = 8.623 ms, Queueing time: mean = 219.107 us, max = 3.073 ms, min = 16.141 us, total = 8.107 ms
	CoreWorker.PrintEventStats - 37 total (1 active, 1 running), Execution time: mean = 625.551 us, total = 23.145 ms, Queueing time: mean = 87.433 us, max = 348.672 us, min = 40.395 us, total = 3.235 ms
	Publisher.CheckDeadSubscribers - 8 total (1 active), Execution time: mean = 34.782 us, total = 278.252 us, Queueing time: mean = 76.331 us, max = 136.392 us, min = 16.954 us, total = 610.644 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 5 total (1 active), Execution time: mean = 359.348 s, total = 1796.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 5 total (0 active), Execution time: mean = 17.457 us, total = 87.286 us, Queueing time: mean = 40.735 us, max = 54.867 us, min = 20.269 us, total = 203.675 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 6661 total (1 active)
Queueing time: mean = 48.720 us, max = 11.213 ms, min = -0.000 s, total = 324.522 ms
Execution time:  mean = 467.573 us, total = 3.115 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 2220 total (0 active), Execution time: mean = 40.187 us, total = 89.214 ms, Queueing time: mean = 47.470 us, max = 11.213 ms, min = 9.715 us, total = 105.384 ms
	CoreWorker.deadline_timer.flush_task_events - 2220 total (1 active), Execution time: mean = 325.822 us, total = 723.324 ms, Queueing time: mean = 98.700 us, max = 6.908 ms, min = -0.000 s, total = 219.114 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 2220 total (0 active), Execution time: mean = 1.037 ms, total = 2.302 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:26:20,822 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 142932 total (8 active)
Queueing time: mean = 275.129 us, max = 44.275 ms, min = -0.002 s, total = 39.325 s
Execution time:  mean = 63.377 ms, total = 9058.665 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 22774 total (1 active), Execution time: mean = 15.763 us, total = 358.985 ms, Queueing time: mean = 105.620 us, max = 44.275 ms, min = -0.002 s, total = 2.405 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2457 total (0 active), Execution time: mean = 1.031 ms, total = 2.532 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2457 total (0 active), Execution time: mean = 27.334 us, total = 67.160 ms, Queueing time: mean = 82.362 us, max = 5.249 ms, min = 8.744 us, total = 202.363 ms
	CoreWorker.InternalHeartbeat - 2280 total (1 active), Execution time: mean = 283.633 us, total = 646.682 ms, Queueing time: mean = 108.139 us, max = 7.858 ms, min = -0.000 s, total = 246.557 ms
	CoreWorker.RecordMetrics - 456 total (1 active), Execution time: mean = 49.307 us, total = 22.484 ms, Queueing time: mean = 89.899 us, max = 1.781 ms, min = 13.207 us, total = 40.994 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 228 total (1 active), Execution time: mean = 12.145 us, total = 2.769 ms, Queueing time: mean = 124.134 us, max = 5.352 ms, min = -0.000 s, total = 28.303 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 152 total (0 active), Execution time: mean = 409.323 us, total = 62.217 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 152 total (0 active), Execution time: mean = 45.871 us, total = 6.972 ms, Queueing time: mean = 67.967 us, max = 395.873 us, min = 16.757 us, total = 10.331 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 38 total (1 active), Execution time: mean = 51.786 s, total = 1967.860 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	CoreWorker.PrintEventStats - 38 total (1 active, 1 running), Execution time: mean = 622.435 us, total = 23.653 ms, Queueing time: mean = 86.852 us, max = 348.672 us, min = 40.395 us, total = 3.300 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 37 total (0 active), Execution time: mean = 233.062 us, total = 8.623 ms, Queueing time: mean = 219.107 us, max = 3.073 ms, min = 16.141 us, total = 8.107 ms
	Publisher.CheckDeadSubscribers - 8 total (1 active), Execution time: mean = 34.782 us, total = 278.252 us, Queueing time: mean = 76.331 us, max = 136.392 us, min = 16.954 us, total = 610.644 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 5 total (1 active), Execution time: mean = 359.348 s, total = 1796.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 5 total (0 active), Execution time: mean = 17.457 us, total = 87.286 us, Queueing time: mean = 40.735 us, max = 54.867 us, min = 20.269 us, total = 203.675 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 6841 total (1 active)
Queueing time: mean = 48.893 us, max = 11.213 ms, min = -0.000 s, total = 334.480 ms
Execution time:  mean = 468.074 us, total = 3.202 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 2280 total (0 active), Execution time: mean = 40.179 us, total = 91.609 ms, Queueing time: mean = 47.032 us, max = 11.213 ms, min = 9.715 us, total = 107.233 ms
	CoreWorker.deadline_timer.flush_task_events - 2280 total (1 active), Execution time: mean = 326.840 us, total = 745.196 ms, Queueing time: mean = 99.659 us, max = 6.908 ms, min = -0.000 s, total = 227.222 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 2280 total (0 active), Execution time: mean = 1.037 ms, total = 2.365 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:27:20,822 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 143741 total (8 active)
Queueing time: mean = 274.073 us, max = 44.275 ms, min = -0.002 s, total = 39.395 s
Execution time:  mean = 65.610 ms, total = 9430.916 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 23374 total (1 active), Execution time: mean = 15.782 us, total = 368.881 ms, Queueing time: mean = 105.516 us, max = 44.275 ms, min = -0.002 s, total = 2.466 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2517 total (0 active), Execution time: mean = 1.030 ms, total = 2.592 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2517 total (0 active), Execution time: mean = 28.095 us, total = 70.715 ms, Queueing time: mean = 81.423 us, max = 5.249 ms, min = 8.744 us, total = 204.941 ms
	CoreWorker.InternalHeartbeat - 2340 total (1 active), Execution time: mean = 283.129 us, total = 662.523 ms, Queueing time: mean = 107.588 us, max = 7.858 ms, min = -0.000 s, total = 251.755 ms
	CoreWorker.RecordMetrics - 468 total (1 active), Execution time: mean = 49.032 us, total = 22.947 ms, Queueing time: mean = 89.866 us, max = 1.781 ms, min = 13.207 us, total = 42.058 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 234 total (1 active), Execution time: mean = 12.272 us, total = 2.872 ms, Queueing time: mean = 122.785 us, max = 5.352 ms, min = -0.000 s, total = 28.732 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 156 total (0 active), Execution time: mean = 405.297 us, total = 63.226 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 156 total (0 active), Execution time: mean = 45.518 us, total = 7.101 ms, Queueing time: mean = 67.582 us, max = 395.873 us, min = 16.757 us, total = 10.543 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 39 total (1 active), Execution time: mean = 60.000 s, total = 2340.019 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 39 total (1 active, 1 running), Execution time: mean = 620.160 us, total = 24.186 ms, Queueing time: mean = 86.228 us, max = 348.672 us, min = 40.395 us, total = 3.363 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 38 total (0 active), Execution time: mean = 232.741 us, total = 8.844 ms, Queueing time: mean = 220.956 us, max = 3.073 ms, min = 16.141 us, total = 8.396 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	Publisher.CheckDeadSubscribers - 8 total (1 active), Execution time: mean = 34.782 us, total = 278.252 us, Queueing time: mean = 76.331 us, max = 136.392 us, min = 16.954 us, total = 610.644 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling - 5 total (1 active), Execution time: mean = 359.348 s, total = 1796.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 5 total (0 active), Execution time: mean = 17.457 us, total = 87.286 us, Queueing time: mean = 40.735 us, max = 54.867 us, min = 20.269 us, total = 203.675 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 7021 total (1 active)
Queueing time: mean = 48.969 us, max = 11.213 ms, min = -0.000 s, total = 343.812 ms
Execution time:  mean = 467.197 us, total = 3.280 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 2340 total (0 active), Execution time: mean = 40.265 us, total = 94.220 ms, Queueing time: mean = 47.404 us, max = 11.213 ms, min = 9.715 us, total = 110.924 ms
	CoreWorker.deadline_timer.flush_task_events - 2340 total (1 active), Execution time: mean = 326.451 us, total = 763.895 ms, Queueing time: mean = 99.514 us, max = 6.908 ms, min = -0.000 s, total = 232.863 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 2340 total (0 active), Execution time: mean = 1.035 ms, total = 2.422 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:28:20,823 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 144550 total (8 active)
Queueing time: mean = 272.956 us, max = 44.275 ms, min = -0.002 s, total = 39.456 s
Execution time:  mean = 69.395 ms, total = 10031.005 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 23973 total (1 active), Execution time: mean = 15.735 us, total = 377.210 ms, Queueing time: mean = 105.028 us, max = 44.275 ms, min = -0.002 s, total = 2.518 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2577 total (0 active), Execution time: mean = 1.029 ms, total = 2.651 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2577 total (0 active), Execution time: mean = 28.209 us, total = 72.695 ms, Queueing time: mean = 80.409 us, max = 5.249 ms, min = 8.744 us, total = 207.215 ms
	CoreWorker.InternalHeartbeat - 2400 total (1 active), Execution time: mean = 283.019 us, total = 679.246 ms, Queueing time: mean = 106.821 us, max = 7.858 ms, min = -0.000 s, total = 256.370 ms
	CoreWorker.RecordMetrics - 480 total (1 active), Execution time: mean = 48.776 us, total = 23.413 ms, Queueing time: mean = 89.984 us, max = 1.781 ms, min = 13.207 us, total = 43.192 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 240 total (1 active), Execution time: mean = 12.276 us, total = 2.946 ms, Queueing time: mean = 121.313 us, max = 5.352 ms, min = -0.000 s, total = 29.115 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 160 total (0 active), Execution time: mean = 404.085 us, total = 64.654 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 160 total (0 active), Execution time: mean = 45.231 us, total = 7.237 ms, Queueing time: mean = 67.074 us, max = 395.873 us, min = 16.757 us, total = 10.732 ms
	CoreWorker.PrintEventStats - 40 total (1 active, 1 running), Execution time: mean = 620.939 us, total = 24.838 ms, Queueing time: mean = 85.299 us, max = 348.672 us, min = 40.395 us, total = 3.412 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 39 total (1 active), Execution time: mean = 60.000 s, total = 2340.019 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 38 total (0 active), Execution time: mean = 232.741 us, total = 8.844 ms, Queueing time: mean = 220.956 us, max = 3.073 ms, min = 16.141 us, total = 8.396 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	Publisher.CheckDeadSubscribers - 9 total (1 active), Execution time: mean = 35.284 us, total = 317.553 us, Queueing time: mean = 75.306 us, max = 136.392 us, min = 16.954 us, total = 677.754 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 6 total (1 active), Execution time: mean = 399.457 s, total = 2396.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 6 total (0 active), Execution time: mean = 18.481 us, total = 110.888 us, Queueing time: mean = 42.328 us, max = 54.867 us, min = 20.269 us, total = 253.969 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 7201 total (1 active)
Queueing time: mean = 49.940 us, max = 11.213 ms, min = -0.000 s, total = 359.615 ms
Execution time:  mean = 467.355 us, total = 3.365 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 2400 total (0 active), Execution time: mean = 40.272 us, total = 96.653 ms, Queueing time: mean = 47.866 us, max = 11.213 ms, min = 9.715 us, total = 114.879 ms
	CoreWorker.deadline_timer.flush_task_events - 2400 total (1 active), Execution time: mean = 326.307 us, total = 783.137 ms, Queueing time: mean = 101.963 us, max = 6.908 ms, min = -0.000 s, total = 244.711 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 2400 total (0 active), Execution time: mean = 1.036 ms, total = 2.485 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:29:20,824 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 145357 total (8 active)
Queueing time: mean = 271.810 us, max = 44.275 ms, min = -0.002 s, total = 39.509 s
Execution time:  mean = 69.010 ms, total = 10031.092 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 24573 total (1 active), Execution time: mean = 15.711 us, total = 386.072 ms, Queueing time: mean = 104.314 us, max = 44.275 ms, min = -0.002 s, total = 2.563 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2637 total (0 active), Execution time: mean = 1.027 ms, total = 2.709 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2637 total (0 active), Execution time: mean = 28.241 us, total = 74.471 ms, Queueing time: mean = 79.429 us, max = 5.249 ms, min = 8.744 us, total = 209.455 ms
	CoreWorker.InternalHeartbeat - 2460 total (1 active), Execution time: mean = 282.563 us, total = 695.106 ms, Queueing time: mean = 105.957 us, max = 7.858 ms, min = -0.000 s, total = 260.655 ms
	CoreWorker.RecordMetrics - 492 total (1 active), Execution time: mean = 48.512 us, total = 23.868 ms, Queueing time: mean = 89.590 us, max = 1.781 ms, min = 13.207 us, total = 44.078 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 246 total (1 active), Execution time: mean = 12.246 us, total = 3.013 ms, Queueing time: mean = 119.652 us, max = 5.352 ms, min = -0.000 s, total = 29.434 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 164 total (0 active), Execution time: mean = 403.542 us, total = 66.181 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 164 total (0 active), Execution time: mean = 45.101 us, total = 7.397 ms, Queueing time: mean = 67.869 us, max = 395.873 us, min = 16.680 us, total = 11.130 ms
	CoreWorker.PrintEventStats - 41 total (1 active, 1 running), Execution time: mean = 617.482 us, total = 25.317 ms, Queueing time: mean = 85.007 us, max = 348.672 us, min = 40.395 us, total = 3.485 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 39 total (1 active), Execution time: mean = 60.000 s, total = 2340.019 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 38 total (0 active), Execution time: mean = 232.741 us, total = 8.844 ms, Queueing time: mean = 220.956 us, max = 3.073 ms, min = 16.141 us, total = 8.396 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	Publisher.CheckDeadSubscribers - 9 total (1 active), Execution time: mean = 35.284 us, total = 317.553 us, Queueing time: mean = 75.306 us, max = 136.392 us, min = 16.954 us, total = 677.754 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 6 total (1 active), Execution time: mean = 399.457 s, total = 2396.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 6 total (0 active), Execution time: mean = 18.481 us, total = 110.888 us, Queueing time: mean = 42.328 us, max = 54.867 us, min = 20.269 us, total = 253.969 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 7381 total (1 active)
Queueing time: mean = 49.610 us, max = 11.213 ms, min = -0.000 s, total = 366.169 ms
Execution time:  mean = 466.419 us, total = 3.443 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 2460 total (0 active), Execution time: mean = 40.262 us, total = 99.045 ms, Queueing time: mean = 47.437 us, max = 11.213 ms, min = 9.715 us, total = 116.695 ms
	CoreWorker.deadline_timer.flush_task_events - 2460 total (1 active), Execution time: mean = 326.047 us, total = 802.075 ms, Queueing time: mean = 101.402 us, max = 6.908 ms, min = -0.000 s, total = 249.449 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 2460 total (0 active), Execution time: mean = 1.033 ms, total = 2.541 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:30:20,825 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 146163 total (8 active)
Queueing time: mean = 270.897 us, max = 44.275 ms, min = -0.002 s, total = 39.595 s
Execution time:  mean = 68.630 ms, total = 10031.180 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 25172 total (1 active), Execution time: mean = 15.654 us, total = 394.044 ms, Queueing time: mean = 104.755 us, max = 44.275 ms, min = -0.002 s, total = 2.637 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2697 total (0 active), Execution time: mean = 1.027 ms, total = 2.769 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2697 total (0 active), Execution time: mean = 28.278 us, total = 76.266 ms, Queueing time: mean = 78.484 us, max = 5.249 ms, min = 8.744 us, total = 211.672 ms
	CoreWorker.InternalHeartbeat - 2520 total (1 active), Execution time: mean = 282.423 us, total = 711.707 ms, Queueing time: mean = 106.725 us, max = 7.858 ms, min = -0.000 s, total = 268.947 ms
	CoreWorker.RecordMetrics - 504 total (1 active), Execution time: mean = 48.322 us, total = 24.354 ms, Queueing time: mean = 88.950 us, max = 1.781 ms, min = 13.207 us, total = 44.831 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.TryDelPendingObjectRefStreams - 252 total (1 active), Execution time: mean = 12.253 us, total = 3.088 ms, Queueing time: mean = 118.684 us, max = 5.352 ms, min = -0.000 s, total = 29.908 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 168 total (0 active), Execution time: mean = 400.831 us, total = 67.340 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 168 total (0 active), Execution time: mean = 44.841 us, total = 7.533 ms, Queueing time: mean = 67.913 us, max = 395.873 us, min = 16.680 us, total = 11.409 ms
	CoreWorker.PrintEventStats - 42 total (1 active, 1 running), Execution time: mean = 616.203 us, total = 25.881 ms, Queueing time: mean = 84.599 us, max = 348.672 us, min = 40.395 us, total = 3.553 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 39 total (1 active), Execution time: mean = 60.000 s, total = 2340.019 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 38 total (0 active), Execution time: mean = 232.741 us, total = 8.844 ms, Queueing time: mean = 220.956 us, max = 3.073 ms, min = 16.141 us, total = 8.396 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	Publisher.CheckDeadSubscribers - 9 total (1 active), Execution time: mean = 35.284 us, total = 317.553 us, Queueing time: mean = 75.306 us, max = 136.392 us, min = 16.954 us, total = 677.754 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 6 total (1 active), Execution time: mean = 399.457 s, total = 2396.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 6 total (0 active), Execution time: mean = 18.481 us, total = 110.888 us, Queueing time: mean = 42.328 us, max = 54.867 us, min = 20.269 us, total = 253.969 us
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 7558 total (1 active)
Queueing time: mean = 50.012 us, max = 11.213 ms, min = -0.000 s, total = 377.993 ms
Execution time:  mean = 466.464 us, total = 3.526 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 2519 total (0 active), Execution time: mean = 40.343 us, total = 101.624 ms, Queueing time: mean = 49.084 us, max = 11.213 ms, min = 9.715 us, total = 123.642 ms
	CoreWorker.deadline_timer.flush_task_events - 2519 total (1 active), Execution time: mean = 325.885 us, total = 820.905 ms, Queueing time: mean = 100.963 us, max = 6.908 ms, min = -0.000 s, total = 254.326 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 2519 total (0 active), Execution time: mean = 1.033 ms, total = 2.603 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:31:20,825 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 146967 total (8 active)
Queueing time: mean = 269.829 us, max = 44.275 ms, min = -0.002 s, total = 39.656 s
Execution time:  mean = 68.255 ms, total = 10031.271 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 25771 total (1 active), Execution time: mean = 15.623 us, total = 402.631 ms, Queueing time: mean = 104.295 us, max = 44.275 ms, min = -0.002 s, total = 2.688 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2757 total (0 active), Execution time: mean = 1.027 ms, total = 2.830 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2757 total (0 active), Execution time: mean = 28.380 us, total = 78.243 ms, Queueing time: mean = 77.588 us, max = 5.249 ms, min = 8.744 us, total = 213.910 ms
	CoreWorker.InternalHeartbeat - 2580 total (1 active), Execution time: mean = 282.206 us, total = 728.093 ms, Queueing time: mean = 106.634 us, max = 7.858 ms, min = -0.000 s, total = 275.115 ms
	CoreWorker.RecordMetrics - 516 total (1 active), Execution time: mean = 48.099 us, total = 24.819 ms, Queueing time: mean = 88.591 us, max = 1.781 ms, min = 13.207 us, total = 45.713 ms
	CoreWorker.TryDelPendingObjectRefStreams - 258 total (1 active), Execution time: mean = 12.242 us, total = 3.158 ms, Queueing time: mean = 117.620 us, max = 5.352 ms, min = -0.000 s, total = 30.346 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 171 total (0 active), Execution time: mean = 399.234 us, total = 68.269 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 171 total (0 active), Execution time: mean = 44.589 us, total = 7.625 ms, Queueing time: mean = 67.968 us, max = 395.873 us, min = 16.680 us, total = 11.622 ms
	CoreWorker.PrintEventStats - 43 total (1 active, 1 running), Execution time: mean = 622.213 us, total = 26.755 ms, Queueing time: mean = 84.338 us, max = 348.672 us, min = 40.395 us, total = 3.627 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 39 total (1 active), Execution time: mean = 60.000 s, total = 2340.019 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 38 total (0 active), Execution time: mean = 232.741 us, total = 8.844 ms, Queueing time: mean = 220.956 us, max = 3.073 ms, min = 16.141 us, total = 8.396 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	Publisher.CheckDeadSubscribers - 9 total (1 active), Execution time: mean = 35.284 us, total = 317.553 us, Queueing time: mean = 75.306 us, max = 136.392 us, min = 16.954 us, total = 677.754 us
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 6 total (0 active), Execution time: mean = 18.481 us, total = 110.888 us, Queueing time: mean = 42.328 us, max = 54.867 us, min = 20.269 us, total = 253.969 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 6 total (1 active), Execution time: mean = 399.457 s, total = 2396.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 7738 total (1 active)
Queueing time: mean = 49.680 us, max = 11.213 ms, min = -0.000 s, total = 384.427 ms
Execution time:  mean = 466.309 us, total = 3.608 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 2579 total (0 active), Execution time: mean = 40.392 us, total = 104.172 ms, Queueing time: mean = 48.614 us, max = 11.213 ms, min = 9.715 us, total = 125.376 ms
	CoreWorker.deadline_timer.flush_task_events - 2579 total (1 active), Execution time: mean = 325.706 us, total = 839.995 ms, Queueing time: mean = 100.437 us, max = 6.908 ms, min = -0.000 s, total = 259.027 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 2579 total (0 active), Execution time: mean = 1.033 ms, total = 2.664 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:32:20,826 I 693801 693872] core_worker.cc:902: Event stats:


Global stats: 147774 total (8 active)
Queueing time: mean = 268.755 us, max = 44.275 ms, min = -0.002 s, total = 39.715 s
Execution time:  mean = 67.883 ms, total = 10031.357 s
Event stats:
	NodeManagerService.grpc_client.RequestWorkerLease.OnReplyReceived - 55130 total (0 active), Execution time: mean = 161.169 us, total = 8.885 s, Queueing time: mean = 657.063 us, max = 13.589 ms, min = 2.310 us, total = 36.224 s
	NodeManagerService.grpc_client.RequestWorkerLease - 55130 total (0 active), Execution time: mean = 57.551 ms, total = 3172.785 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.RecoverObjects - 26371 total (1 active), Execution time: mean = 15.599 us, total = 411.366 ms, Queueing time: mean = 103.810 us, max = 44.275 ms, min = -0.002 s, total = 2.738 s
	NodeManagerService.grpc_client.ReportWorkerBacklog - 2817 total (0 active), Execution time: mean = 1.025 ms, total = 2.886 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 2817 total (0 active), Execution time: mean = 28.437 us, total = 80.108 ms, Queueing time: mean = 76.698 us, max = 5.249 ms, min = 8.744 us, total = 216.057 ms
	CoreWorker.InternalHeartbeat - 2640 total (1 active), Execution time: mean = 281.876 us, total = 744.153 ms, Queueing time: mean = 106.192 us, max = 7.858 ms, min = -0.000 s, total = 280.348 ms
	CoreWorker.RecordMetrics - 528 total (1 active), Execution time: mean = 47.854 us, total = 25.267 ms, Queueing time: mean = 88.267 us, max = 1.781 ms, min = 13.207 us, total = 46.605 ms
	CoreWorker.TryDelPendingObjectRefStreams - 264 total (1 active), Execution time: mean = 12.236 us, total = 3.230 ms, Queueing time: mean = 116.669 us, max = 5.352 ms, min = -0.000 s, total = 30.801 ms
	CoreWorkerMemoryStore.Put.get_async_callbacks - 255 total (0 active), Execution time: mean = 169.655 ns, total = 43.262 us, Queueing time: mean = 129.735 us, max = 2.091 ms, min = 36.158 us, total = 33.082 ms
	CoreWorkerService.grpc_client.PushTask.OnReplyReceived - 254 total (0 active), Execution time: mean = 370.443 us, total = 94.093 ms, Queueing time: mean = 100.329 us, max = 2.025 ms, min = 16.717 us, total = 25.484 ms
	CoreWorker.SubmitTask - 254 total (0 active), Execution time: mean = 124.079 us, total = 31.516 ms, Queueing time: mean = 209.391 us, max = 3.347 ms, min = 13.805 us, total = 53.185 ms
	NodeManagerService.grpc_client.ReturnWorker - 254 total (0 active), Execution time: mean = 1.700 ms, total = 431.710 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReturnWorker.OnReplyReceived - 254 total (0 active), Execution time: mean = 25.816 us, total = 6.557 ms, Queueing time: mean = 124.474 us, max = 11.316 ms, min = 10.869 us, total = 31.616 ms
	CoreWorkerService.grpc_client.PushTask - 254 total (0 active), Execution time: mean = 8.300 s, total = 2108.085 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 175 total (0 active), Execution time: mean = 398.727 us, total = 69.777 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 175 total (0 active), Execution time: mean = 44.227 us, total = 7.740 ms, Queueing time: mean = 68.577 us, max = 395.873 us, min = 16.680 us, total = 12.001 ms
	CoreWorker.PrintEventStats - 44 total (1 active, 1 running), Execution time: mean = 621.082 us, total = 27.328 ms, Queueing time: mean = 84.544 us, max = 348.672 us, min = 40.395 us, total = 3.720 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 39 total (1 active), Execution time: mean = 60.000 s, total = 2340.019 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 38 total (0 active), Execution time: mean = 232.741 us, total = 8.844 ms, Queueing time: mean = 220.956 us, max = 3.073 ms, min = 16.141 us, total = 8.396 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 38 total (0 active), Execution time: mean = 105.348 us, total = 4.003 ms, Queueing time: mean = 215.708 us, max = 334.311 us, min = 142.411 us, total = 8.197 ms
	Publisher.CheckDeadSubscribers - 9 total (1 active), Execution time: mean = 35.284 us, total = 317.553 us, Queueing time: mean = 75.306 us, max = 136.392 us, min = 16.954 us, total = 677.754 us
	CoreWorkerService.grpc_server.PubsubLongPolling.HandleRequestImpl - 6 total (0 active), Execution time: mean = 18.481 us, total = 110.888 us, Queueing time: mean = 42.328 us, max = 54.867 us, min = 20.269 us, total = 253.969 us
	CoreWorkerService.grpc_server.PubsubLongPolling - 6 total (1 active), Execution time: mean = 399.457 s, total = 2396.741 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 155.441 us, total = 932.646 us, Queueing time: mean = 461.916 us, max = 897.511 us, min = 44.684 us, total = 2.771 ms
	CoreWorkerService.grpc_server.PubsubCommandBatch - 3 total (0 active), Execution time: mean = 542.911 us, total = 1.629 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.PubsubCommandBatch.HandleRequestImpl - 3 total (0 active), Execution time: mean = 35.825 us, total = 107.474 us, Queueing time: mean = 77.190 us, max = 172.986 us, min = 28.950 us, total = 231.570 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 1.129 ms, total = 1.129 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch - 1 total (0 active), Execution time: mean = 610.595 us, total = 610.595 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 730.774 us, total = 730.774 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 170.187 us, total = 170.187 us, Queueing time: mean = 17.301 us, max = 17.301 us, min = 17.301 us, total = 17.301 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.053 ms, total = 1.053 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs - 1 total (0 active), Execution time: mean = 1.919 ms, total = 1.919 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.PinObjectIDs.OnReplyReceived - 1 total (0 active), Execution time: mean = 83.817 us, total = 83.817 us, Queueing time: mean = 13.293 us, max = 13.293 us, min = 13.293 us, total = 13.293 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 144.744 us, total = 144.744 us, Queueing time: mean = 21.793 us, max = 21.793 us, min = 21.793 us, total = 21.793 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 160.066 us, total = 160.066 us, Queueing time: mean = 22.390 us, max = 22.390 us, min = 22.390 us, total = 22.390 us
	CoreWorkerService.grpc_server.UpdateObjectLocationBatch.HandleRequestImpl - 1 total (0 active), Execution time: mean = 137.874 us, total = 137.874 us, Queueing time: mean = 50.266 us, max = 50.266 us, min = 50.266 us, total = 50.266 us

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 7918 total (1 active)
Queueing time: mean = 49.356 us, max = 11.213 ms, min = -0.000 s, total = 390.800 ms
Execution time:  mean = 466.489 us, total = 3.694 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 2639 total (0 active), Execution time: mean = 40.378 us, total = 106.559 ms, Queueing time: mean = 48.267 us, max = 11.213 ms, min = 9.715 us, total = 127.376 ms
	CoreWorker.deadline_timer.flush_task_events - 2639 total (1 active), Execution time: mean = 325.137 us, total = 858.037 ms, Queueing time: mean = 99.810 us, max = 6.908 ms, min = -0.000 s, total = 263.400 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 2639 total (0 active), Execution time: mean = 1.034 ms, total = 2.729 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 327.910 us, total = 327.910 us, Queueing time: mean = 24.631 us, max = 24.631 us, min = 24.631 us, total = 24.631 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.0983992 MiB
	total number of task attempts sent: 752
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:32:47,594 I 693801 693852] core_worker.cc:1088: Sending disconnect message to the local raylet.
[2025-06-26 01:32:47,594 I 693801 693852] raylet_client.cc:73: RayletClient::Disconnect, exit_type=INTENDED_USER_EXIT, exit_detail=Shutdown by ray.shutdown()., has creation_task_exception_pb_bytes=0
[2025-06-26 01:32:47,595 I 693801 693852] core_worker.cc:1094: Disconnected from the local raylet.
[2025-06-26 01:32:47,595 I 693801 693852] core_worker.cc:1006: Shutting down.
[2025-06-26 01:32:47,596 I 693801 693852] task_event_buffer.cc:298: Shutting down TaskEventBuffer.
[2025-06-26 01:32:47,596 I 693801 693878] task_event_buffer.cc:266: Task event buffer io service stopped.
[2025-06-26 01:32:47,596 I 693801 693852] core_worker.cc:1028: Waiting for joining a core worker io thread. If it hangs here, there might be deadlock or a high load in the core worker io service.
[2025-06-26 01:32:47,596 I 693801 693872] core_worker.cc:1280: Core worker main io service stopped.
[2025-06-26 01:32:47,602 I 693801 693852] core_worker.cc:1040: Disconnecting a GCS client.
[2025-06-26 01:32:47,603 I 693801 693852] core_worker.cc:1047: Core worker ready to be deallocated.
[2025-06-26 01:32:47,603 I 693801 693852] core_worker.cc:997: Core worker is destructed
[2025-06-26 01:32:47,603 I 693801 693852] task_event_buffer.cc:298: Shutting down TaskEventBuffer.
[2025-06-26 01:32:47,604 W 693801 693911] server_call.h:338: [1] Not sending reply because executor stopped.
[2025-06-26 01:32:47,605 I 693801 693852] core_worker_process.cc:237: Destructing CoreWorkerProcessImpl. pid: 693801
[2025-06-26 01:32:47,605 I 693801 693852] io_service_pool.cc:48: IOServicePool is stopped.
[2025-06-26 01:32:47,724 I 693801 693852] stats.h:120: Stats module has shutdown.
