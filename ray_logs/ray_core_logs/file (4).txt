[2025-06-26 01:17:07,328 I 117 117] core_worker_process.cc:192: Constructing CoreWorkerProcess. pid: 117
[2025-06-26 01:17:07,334 I 117 117] io_service_pool.cc:36: IOServicePool is running with 1 io_service.
[2025-06-26 01:17:07,337 I 117 117] grpc_server.cc:141: driver server started, listening on port 10004.
[2025-06-26 01:17:07,339 I 117 117] core_worker.cc:542: Initializing worker at address: ************:10004 worker_id=03000000ffffffffffffffffffffffffffffffffffffffffffffffff node_id=11d1fceddfd4a7d70539f452d9f1c4f91705b1be5f541b4b015ed1f5
[2025-06-26 01:17:07,343 I 117 117] task_event_buffer.cc:287: Reporting task events to GCS every 1000ms.
[2025-06-26 01:17:07,346 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 13 total (8 active)
Queueing time: mean = 22.874 us, max = 244.223 us, min = 19.461 us, total = 297.363 us
Execution time:  mean = 265.170 us, total = 3.447 ms
Event stats:
	PeriodicalRunner.RunFnPeriodically - 6 total (5 active, 1 running), Execution time: mean = 5.543 us, total = 33.255 us, Queueing time: mean = 5.613 us, max = 33.679 us, min = 33.679 us, total = 33.679 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 4 total (1 active)
Queueing time: mean = 12.986 us, max = 35.434 us, min = 16.510 us, total = 51.944 us
Execution time:  mean = 366.212 us, total = 1.465 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1 total (0 active), Execution time: mean = 1.122 ms, total = 1.122 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1 total (0 active), Execution time: mean = 38.518 us, total = 38.518 us, Queueing time: mean = 35.434 us, max = 35.434 us, min = 35.434 us, total = 35.434 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 1
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0 MiB
	total number of task attempts sent: 0
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=11d1fceddfd4a7d70539f452d9f1c4f91705b1be5f541b4b015ed1f5
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:5107: Number of alive nodes:1
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=374025dd58ceacb2d89386dc3a38408dc32b1456276f0fcd84952fd1
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=374025dd58ceacb2d89386dc3a38408dc32b1456276f0fcd84952fd1
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=107d8554c9ce898888fbcca7095612438976ed120c786ea7a3b82940
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=107d8554c9ce898888fbcca7095612438976ed120c786ea7a3b82940
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=6985e19ea27e856c07dc8ab917ef47ca7ac856fdd8daa6e3682941a9
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=6985e19ea27e856c07dc8ab917ef47ca7ac856fdd8daa6e3682941a9
[2025-06-26 01:17:07,348 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=e90de5051af7eab045a5db8c90b1509053a14387b123a1d3416ab7b2
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=e90de5051af7eab045a5db8c90b1509053a14387b123a1d3416ab7b2
[2025-06-26 01:17:07,348 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=aeb88ee76014cf968e47e7c5541354741a624a0d7183bbd80e7cc5f8
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=aeb88ee76014cf968e47e7c5541354741a624a0d7183bbd80e7cc5f8
[2025-06-26 01:17:07,348 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=8c4d80205327b4f323d879c99025b7243851b812f3c5de83998be538
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=8c4d80205327b4f323d879c99025b7243851b812f3c5de83998be538
[2025-06-26 01:17:07,348 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=6d647dce398845972a47ce100b2b655a442fcea0c1e67051f484861f
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=6d647dce398845972a47ce100b2b655a442fcea0c1e67051f484861f
[2025-06-26 01:17:07,348 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=c60c555fc603cb22a64b905e730341251113eb722cf57f73c495e3fb
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=c60c555fc603cb22a64b905e730341251113eb722cf57f73c495e3fb
[2025-06-26 01:17:07,348 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=77967f0e6d34a5fa26747c3c66d5a8d7b80570e71ff0bfb0320af5c8
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=77967f0e6d34a5fa26747c3c66d5a8d7b80570e71ff0bfb0320af5c8
[2025-06-26 01:17:07,348 I 117 117] event.cc:500: Ray Event initialized for CORE_WORKER
[2025-06-26 01:17:07,348 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=71f196cf3e35c23d0086046ebee4d8b57afb729e123bf51e32afab43
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=71f196cf3e35c23d0086046ebee4d8b57afb729e123bf51e32afab43
[2025-06-26 01:17:07,348 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=45dcdd8fc5701ea50b0cef58c35281c586bc903f5a425e0f3c9ed66e
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=45dcdd8fc5701ea50b0cef58c35281c586bc903f5a425e0f3c9ed66e
[2025-06-26 01:17:07,348 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=22dcf4c94eb49e5029d861bbd4193e4e05721c90987ce6702bef89a1
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=22dcf4c94eb49e5029d861bbd4193e4e05721c90987ce6702bef89a1
[2025-06-26 01:17:07,348 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=5ee3f7e3f9d46150aadf44e4c4321a8a15c86ea8af08e6b6653774a2
[2025-06-26 01:17:07,348 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=5ee3f7e3f9d46150aadf44e4c4321a8a15c86ea8af08e6b6653774a2
[2025-06-26 01:17:07,348 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,348 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=f91843d4faa36d81732f614a02a8f8db1a8afd5e9f428b1d4271e85a
[2025-06-26 01:17:07,349 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=f91843d4faa36d81732f614a02a8f8db1a8afd5e9f428b1d4271e85a
[2025-06-26 01:17:07,349 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,349 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=cc56480aa1d01d213e5567748e50a120eecea0f10356a110b2b49300
[2025-06-26 01:17:07,349 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=cc56480aa1d01d213e5567748e50a120eecea0f10356a110b2b49300
[2025-06-26 01:17:07,349 W 117 701550] core_worker.cc:5101: Node change state to DEAD but num_alive_node is 0.
[2025-06-26 01:17:07,349 I 117 117] event.cc:500: Ray Event initialized for EXPORT_TASK
[2025-06-26 01:17:07,349 I 117 117] event.cc:331: Set ray event level to warning
[2025-06-26 01:17:08,840 W 117 117] actor_manager.cc:110: Failed to look up actor with name 'SERVE_CONTROLLER_ACTOR'. This could because 1. You are trying to look up a named actor you didn't create. 2. The named actor died. 3. You did not use a namespace matching the namespace of the actor.
[2025-06-26 01:17:11,272 W 117 117] actor_manager.cc:110: Failed to look up actor with name 'SERVE_CONTROLLER_ACTOR'. This could because 1. You are trying to look up a named actor you didn't create. 2. The named actor died. 3. You did not use a namespace matching the namespace of the actor.
[2025-06-26 01:17:14,282 W 117 117] actor_manager.cc:110: Failed to look up actor with name 'SERVE_CONTROLLER_ACTOR'. This could because 1. You are trying to look up a named actor you didn't create. 2. The named actor died. 3. You did not use a namespace matching the namespace of the actor.
[2025-06-26 01:18:07,346 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 827 total (7 active)
Queueing time: mean = 87.756 us, max = 4.451 ms, min = 2.850 us, total = 72.574 ms
Execution time:  mean = 119.529 us, total = 98.850 ms
Event stats:
	CoreWorker.RecoverObjects - 600 total (1 active), Execution time: mean = 14.452 us, total = 8.671 ms, Queueing time: mean = 83.277 us, max = 2.755 ms, min = 2.850 us, total = 49.966 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 60 total (0 active), Execution time: mean = 972.254 us, total = 58.335 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 60 total (1 active), Execution time: mean = 231.431 us, total = 13.886 ms, Queueing time: mean = 139.349 us, max = 4.451 ms, min = 24.951 us, total = 8.361 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 60 total (0 active), Execution time: mean = 24.379 us, total = 1.463 ms, Queueing time: mean = 52.867 us, max = 712.527 us, min = 14.092 us, total = 3.172 ms
	CoreWorker.RecordMetrics - 12 total (1 active), Execution time: mean = 47.184 us, total = 566.210 us, Queueing time: mean = 38.456 us, max = 106.026 us, min = 8.171 us, total = 461.466 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	CoreWorker.TryDelPendingObjectRefStreams - 6 total (1 active), Execution time: mean = 7.613 us, total = 45.680 us, Queueing time: mean = 53.766 us, max = 90.190 us, min = 25.662 us, total = 322.597 us
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 4 total (0 active), Execution time: mean = 54.986 us, total = 219.942 us, Queueing time: mean = 44.988 us, max = 106.445 us, min = 14.635 us, total = 179.952 us
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 4 total (0 active), Execution time: mean = 524.143 us, total = 2.097 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 3 total (0 active), Execution time: mean = 54.474 us, total = 163.423 us, Queueing time: mean = 49.157 us, max = 59.075 us, min = 39.025 us, total = 147.470 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 3 total (0 active), Execution time: mean = 1.241 ms, total = 3.722 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 1 total (1 active, 1 running), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 181 total (1 active)
Queueing time: mean = 59.949 us, max = 5.086 ms, min = 12.774 us, total = 10.851 ms
Execution time:  mean = 412.897 us, total = 74.734 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 60 total (0 active), Execution time: mean = 909.720 us, total = 54.583 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 60 total (1 active), Execution time: mean = 294.811 us, total = 17.689 ms, Queueing time: mean = 153.866 us, max = 5.086 ms, min = 27.494 us, total = 9.232 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 60 total (0 active), Execution time: mean = 35.966 us, total = 2.158 ms, Queueing time: mean = 26.704 us, max = 107.901 us, min = 12.774 us, total = 1.602 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:18:34,283 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:18:34,283 I 117 701550] core_worker.cc:5107: Number of alive nodes:1
[2025-06-26 01:18:35,716 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:18:36,627 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:18:47,044 W 117 117] actor_manager.cc:110: Failed to look up actor with name 'SERVE_CONTROLLER_ACTOR'. This could because 1. You are trying to look up a named actor you didn't create. 2. The named actor died. 3. You did not use a namespace matching the namespace of the actor.
[2025-06-26 01:19:07,347 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 1644 total (7 active)
Queueing time: mean = 78.417 us, max = 4.451 ms, min = 2.850 us, total = 128.917 ms
Execution time:  mean = 54.419 ms, total = 89.464 s
Event stats:
	CoreWorker.RecoverObjects - 1199 total (1 active), Execution time: mean = 14.281 us, total = 17.123 ms, Queueing time: mean = 79.749 us, max = 2.755 ms, min = 2.850 us, total = 95.618 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 120 total (0 active), Execution time: mean = 921.573 us, total = 110.589 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 120 total (1 active), Execution time: mean = 235.511 us, total = 28.261 ms, Queueing time: mean = 119.656 us, max = 4.451 ms, min = 22.917 us, total = 14.359 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 120 total (0 active), Execution time: mean = 23.763 us, total = 2.852 ms, Queueing time: mean = 42.194 us, max = 712.527 us, min = 12.519 us, total = 5.063 ms
	CoreWorker.RecordMetrics - 24 total (1 active), Execution time: mean = 54.755 us, total = 1.314 ms, Queueing time: mean = 47.249 us, max = 106.026 us, min = 8.171 us, total = 1.134 ms
	CoreWorker.TryDelPendingObjectRefStreams - 12 total (1 active), Execution time: mean = 8.530 us, total = 102.361 us, Queueing time: mean = 66.044 us, max = 98.905 us, min = 25.662 us, total = 792.527 us
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 8 total (0 active), Execution time: mean = 544.671 us, total = 4.357 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 8 total (0 active), Execution time: mean = 60.077 us, total = 480.614 us, Queueing time: mean = 116.505 us, max = 275.612 us, min = 14.635 us, total = 932.039 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 4 total (1 active), Execution time: mean = 22.321 s, total = 89.283 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 3 total (0 active), Execution time: mean = 285.306 us, total = 855.918 us, Queueing time: mean = 24.243 us, max = 30.146 us, min = 19.329 us, total = 72.728 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 3 total (0 active), Execution time: mean = 62.154 us, total = 186.461 us, Queueing time: mean = 218.413 us, max = 289.825 us, min = 173.154 us, total = 655.238 us
	CoreWorker.PrintEventStats - 2 total (1 active, 1 running), Execution time: mean = 242.812 us, total = 485.625 us, Queueing time: mean = 60.408 us, max = 120.816 us, min = 120.816 us, total = 120.816 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 361 total (1 active)
Queueing time: mean = 51.233 us, max = 5.086 ms, min = 12.774 us, total = 18.495 ms
Execution time:  mean = 404.994 us, total = 146.203 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 120 total (0 active), Execution time: mean = 905.515 us, total = 108.662 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 120 total (1 active), Execution time: mean = 275.570 us, total = 33.068 ms, Queueing time: mean = 121.587 us, max = 5.086 ms, min = 27.494 us, total = 14.590 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 120 total (0 active), Execution time: mean = 34.733 us, total = 4.168 ms, Queueing time: mean = 32.402 us, max = 393.317 us, min = 12.774 us, total = 3.888 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:20:07,347 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 2451 total (7 active)
Queueing time: mean = 76.996 us, max = 4.451 ms, min = 2.850 us, total = 188.717 ms
Execution time:  mean = 36.539 ms, total = 89.557 s
Event stats:
	CoreWorker.RecoverObjects - 1799 total (1 active), Execution time: mean = 13.999 us, total = 25.185 ms, Queueing time: mean = 81.883 us, max = 2.755 ms, min = 2.850 us, total = 147.307 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 180 total (0 active), Execution time: mean = 964.097 us, total = 173.537 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 180 total (1 active), Execution time: mean = 245.331 us, total = 44.160 ms, Queueing time: mean = 103.408 us, max = 4.451 ms, min = 22.917 us, total = 18.613 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 180 total (0 active), Execution time: mean = 24.314 us, total = 4.376 ms, Queueing time: mean = 39.394 us, max = 712.527 us, min = 11.997 us, total = 7.091 ms
	CoreWorker.RecordMetrics - 36 total (1 active), Execution time: mean = 52.490 us, total = 1.890 ms, Queueing time: mean = 63.366 us, max = 356.986 us, min = 8.171 us, total = 2.281 ms
	CoreWorker.TryDelPendingObjectRefStreams - 18 total (1 active), Execution time: mean = 8.883 us, total = 159.896 us, Queueing time: mean = 66.553 us, max = 98.905 us, min = 25.662 us, total = 1.198 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 12 total (0 active), Execution time: mean = 578.811 us, total = 6.946 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 12 total (0 active), Execution time: mean = 53.985 us, total = 647.821 us, Queueing time: mean = 95.664 us, max = 275.612 us, min = 14.635 us, total = 1.148 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 4 total (1 active), Execution time: mean = 22.321 s, total = 89.283 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 3 total (0 active), Execution time: mean = 285.306 us, total = 855.918 us, Queueing time: mean = 24.243 us, max = 30.146 us, min = 19.329 us, total = 72.728 us
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 3 total (0 active), Execution time: mean = 62.154 us, total = 186.461 us, Queueing time: mean = 218.413 us, max = 289.825 us, min = 173.154 us, total = 655.238 us
	CoreWorker.PrintEventStats - 3 total (1 active, 1 running), Execution time: mean = 295.858 us, total = 887.575 us, Queueing time: mean = 60.525 us, max = 120.816 us, min = 60.758 us, total = 181.574 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 541 total (1 active)
Queueing time: mean = 58.105 us, max = 5.086 ms, min = 9.694 us, total = 31.435 ms
Execution time:  mean = 409.365 us, total = 221.466 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 180 total (0 active), Execution time: mean = 913.671 us, total = 164.461 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 180 total (1 active), Execution time: mean = 281.135 us, total = 50.604 ms, Queueing time: mean = 143.488 us, max = 5.086 ms, min = 27.494 us, total = 25.828 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 180 total (0 active), Execution time: mean = 33.871 us, total = 6.097 ms, Queueing time: mean = 31.059 us, max = 393.317 us, min = 9.694 us, total = 5.591 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:20:38,137 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:20:38,137 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=1650ddb6aa1e4838b134a687bc933a7eda6db34fa7187c2893bc237d
[2025-06-26 01:20:38,137 I 117 701550] core_worker.cc:5107: Number of alive nodes:2
[2025-06-26 01:20:43,258 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:20:43,258 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=5485124c2c81d311eeda7ef6b6eb883d2d4e1170852cc0f99ca1aeab
[2025-06-26 01:20:43,261 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:20:43,261 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=ebb9778a9187e3faa28f6b6f1f9bc8d9b1be7671e41cae1e48c6adf0
[2025-06-26 01:21:05,529 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 1 node_id=1debeedd775f559e844e3f300a626c3ae54f84472517a3fe5853f7f0
[2025-06-26 01:21:07,348 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 3269 total (7 active)
Queueing time: mean = 79.509 us, max = 7.256 ms, min = -0.000 s, total = 259.916 ms
Execution time:  mean = 72.972 ms, total = 238.545 s
Event stats:
	CoreWorker.RecoverObjects - 2398 total (1 active), Execution time: mean = 14.191 us, total = 34.029 ms, Queueing time: mean = 86.950 us, max = 7.256 ms, min = -0.000 s, total = 208.506 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 240 total (0 active), Execution time: mean = 958.812 us, total = 230.115 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 240 total (1 active), Execution time: mean = 252.223 us, total = 60.534 ms, Queueing time: mean = 96.618 us, max = 4.451 ms, min = 15.612 us, total = 23.188 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 240 total (0 active), Execution time: mean = 25.551 us, total = 6.132 ms, Queueing time: mean = 38.815 us, max = 712.527 us, min = 11.997 us, total = 9.316 ms
	CoreWorker.RecordMetrics - 48 total (1 active), Execution time: mean = 48.528 us, total = 2.329 ms, Queueing time: mean = 65.008 us, max = 356.986 us, min = 8.171 us, total = 3.120 ms
	CoreWorker.TryDelPendingObjectRefStreams - 24 total (1 active), Execution time: mean = 8.792 us, total = 211.011 us, Queueing time: mean = 65.095 us, max = 98.905 us, min = 25.662 us, total = 1.562 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 16 total (0 active), Execution time: mean = 508.832 us, total = 8.141 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 16 total (0 active), Execution time: mean = 49.785 us, total = 796.565 us, Queueing time: mean = 90.195 us, max = 275.612 us, min = 14.635 us, total = 1.443 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 8 total (1 active), Execution time: mean = 29.773 s, total = 238.185 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 7 total (0 active), Execution time: mean = 220.748 us, total = 1.545 ms, Queueing time: mean = 153.506 us, max = 752.341 us, min = 19.329 us, total = 1.075 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 7 total (0 active), Execution time: mean = 63.586 us, total = 445.105 us, Queueing time: mean = 184.745 us, max = 289.825 us, min = 134.991 us, total = 1.293 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 4 total (1 active, 1 running), Execution time: mean = 330.808 us, total = 1.323 ms, Queueing time: mean = 60.699 us, max = 120.816 us, min = 60.758 us, total = 242.797 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 1 total (1 active), Execution time: mean = 0.000 s, total = 0.000 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 721 total (1 active)
Queueing time: mean = 52.342 us, max = 5.086 ms, min = 9.694 us, total = 37.739 ms
Execution time:  mean = 412.983 us, total = 297.761 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 240 total (0 active), Execution time: mean = 919.880 us, total = 220.771 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 240 total (1 active), Execution time: mean = 284.832 us, total = 68.360 ms, Queueing time: mean = 125.966 us, max = 5.086 ms, min = 27.494 us, total = 30.232 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 240 total (0 active), Execution time: mean = 34.689 us, total = 8.325 ms, Queueing time: mean = 31.210 us, max = 393.317 us, min = 9.694 us, total = 7.490 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:21:08,663 I 117 701550] accessor.cc:768: Received notification for node, IsAlive = 0 node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:21:08,663 I 117 701550] core_worker.cc:625: Node failure. All objects pinned on that node will be lost if object reconstruction is not enabled. node_id=20d2949d50e964ed8eace80cbb6acd646272451f421b74b2e2e0fbf8
[2025-06-26 01:22:07,348 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 4080 total (7 active)
Queueing time: mean = 77.911 us, max = 7.256 ms, min = -0.000 s, total = 317.878 ms
Execution time:  mean = 59.256 ms, total = 241.763 s
Event stats:
	CoreWorker.RecoverObjects - 2998 total (1 active), Execution time: mean = 14.147 us, total = 42.414 ms, Queueing time: mean = 85.677 us, max = 7.256 ms, min = -0.000 s, total = 256.860 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 300 total (0 active), Execution time: mean = 955.491 us, total = 286.647 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 300 total (1 active), Execution time: mean = 254.463 us, total = 76.339 ms, Queueing time: mean = 92.336 us, max = 4.451 ms, min = 15.612 us, total = 27.701 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 300 total (0 active), Execution time: mean = 26.299 us, total = 7.890 ms, Queueing time: mean = 37.864 us, max = 712.527 us, min = 11.997 us, total = 11.359 ms
	CoreWorker.RecordMetrics - 60 total (1 active), Execution time: mean = 46.422 us, total = 2.785 ms, Queueing time: mean = 70.988 us, max = 356.986 us, min = 8.171 us, total = 4.259 ms
	CoreWorker.TryDelPendingObjectRefStreams - 30 total (1 active), Execution time: mean = 8.959 us, total = 268.776 us, Queueing time: mean = 64.559 us, max = 98.905 us, min = 25.662 us, total = 1.937 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 20 total (0 active), Execution time: mean = 470.449 us, total = 9.409 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 20 total (0 active), Execution time: mean = 48.021 us, total = 960.424 us, Queueing time: mean = 90.450 us, max = 275.612 us, min = 14.635 us, total = 1.809 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.813 s, total = 241.317 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 214.389 us, total = 1.715 ms, Queueing time: mean = 245.938 us, max = 892.963 us, min = 19.329 us, total = 1.968 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	CoreWorker.PrintEventStats - 5 total (1 active, 1 running), Execution time: mean = 347.418 us, total = 1.737 ms, Queueing time: mean = 61.131 us, max = 120.816 us, min = 60.758 us, total = 305.657 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.465 us, total = 8.931 us, Queueing time: mean = 32.258 us, max = 64.515 us, min = 64.515 us, total = 64.515 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 901 total (1 active)
Queueing time: mean = 53.069 us, max = 5.086 ms, min = 9.694 us, total = 47.815 ms
Execution time:  mean = 417.629 us, total = 376.284 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 300 total (0 active), Execution time: mean = 928.147 us, total = 278.444 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 300 total (1 active), Execution time: mean = 290.269 us, total = 87.081 ms, Queueing time: mean = 127.922 us, max = 5.086 ms, min = 27.202 us, total = 38.377 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 300 total (0 active), Execution time: mean = 34.849 us, total = 10.455 ms, Queueing time: mean = 31.407 us, max = 393.317 us, min = 9.694 us, total = 9.422 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:23:07,349 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 4886 total (7 active)
Queueing time: mean = 77.811 us, max = 7.256 ms, min = -0.000 s, total = 380.183 ms
Execution time:  mean = 49.498 ms, total = 241.848 s
Event stats:
	CoreWorker.RecoverObjects - 3597 total (1 active), Execution time: mean = 14.085 us, total = 50.663 ms, Queueing time: mean = 85.808 us, max = 7.256 ms, min = -0.000 s, total = 308.651 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 360 total (0 active), Execution time: mean = 954.869 us, total = 343.753 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 360 total (1 active), Execution time: mean = 254.616 us, total = 91.662 ms, Queueing time: mean = 88.535 us, max = 4.451 ms, min = 11.049 us, total = 31.873 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 360 total (0 active), Execution time: mean = 26.629 us, total = 9.586 ms, Queueing time: mean = 37.103 us, max = 712.527 us, min = 11.997 us, total = 13.357 ms
	CoreWorker.RecordMetrics - 72 total (1 active), Execution time: mean = 44.284 us, total = 3.188 ms, Queueing time: mean = 108.419 us, max = 2.712 ms, min = 8.171 us, total = 7.806 ms
	CoreWorker.TryDelPendingObjectRefStreams - 36 total (1 active), Execution time: mean = 9.092 us, total = 327.330 us, Queueing time: mean = 64.562 us, max = 100.954 us, min = 25.662 us, total = 2.324 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 24 total (0 active), Execution time: mean = 470.091 us, total = 11.282 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 24 total (0 active), Execution time: mean = 47.182 us, total = 1.132 ms, Queueing time: mean = 89.418 us, max = 275.612 us, min = 14.635 us, total = 2.146 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.813 s, total = 241.317 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 214.389 us, total = 1.715 ms, Queueing time: mean = 245.938 us, max = 892.963 us, min = 19.329 us, total = 1.968 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	CoreWorker.PrintEventStats - 6 total (1 active, 1 running), Execution time: mean = 376.971 us, total = 2.262 ms, Queueing time: mean = 63.048 us, max = 120.816 us, min = 60.758 us, total = 378.289 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.465 us, total = 8.931 us, Queueing time: mean = 32.258 us, max = 64.515 us, min = 64.515 us, total = 64.515 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1081 total (1 active)
Queueing time: mean = 49.366 us, max = 5.086 ms, min = 9.694 us, total = 53.365 ms
Execution time:  mean = 417.422 us, total = 451.233 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 360 total (0 active), Execution time: mean = 928.018 us, total = 334.086 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 360 total (1 active), Execution time: mean = 289.377 us, total = 104.176 ms, Queueing time: mean = 116.821 us, max = 5.086 ms, min = 17.643 us, total = 42.056 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 360 total (0 active), Execution time: mean = 35.184 us, total = 12.666 ms, Queueing time: mean = 31.369 us, max = 393.317 us, min = 9.694 us, total = 11.293 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:24:07,350 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 5693 total (7 active)
Queueing time: mean = 76.148 us, max = 7.256 ms, min = -0.000 s, total = 433.508 ms
Execution time:  mean = 42.496 ms, total = 241.930 s
Event stats:
	CoreWorker.RecoverObjects - 4197 total (1 active), Execution time: mean = 14.039 us, total = 58.921 ms, Queueing time: mean = 83.976 us, max = 7.256 ms, min = -0.000 s, total = 352.447 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 420 total (0 active), Execution time: mean = 947.538 us, total = 397.966 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 420 total (1 active), Execution time: mean = 254.323 us, total = 106.816 ms, Queueing time: mean = 88.590 us, max = 4.451 ms, min = 11.049 us, total = 37.208 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 420 total (0 active), Execution time: mean = 26.843 us, total = 11.274 ms, Queueing time: mean = 36.817 us, max = 712.527 us, min = 11.997 us, total = 15.463 ms
	CoreWorker.RecordMetrics - 84 total (1 active), Execution time: mean = 42.882 us, total = 3.602 ms, Queueing time: mean = 103.494 us, max = 2.712 ms, min = 8.171 us, total = 8.693 ms
	CoreWorker.TryDelPendingObjectRefStreams - 42 total (1 active), Execution time: mean = 9.099 us, total = 382.171 us, Queueing time: mean = 66.034 us, max = 143.234 us, min = 25.662 us, total = 2.773 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 28 total (0 active), Execution time: mean = 459.630 us, total = 12.870 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 28 total (0 active), Execution time: mean = 46.381 us, total = 1.299 ms, Queueing time: mean = 101.480 us, max = 455.227 us, min = 14.635 us, total = 2.841 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.813 s, total = 241.317 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 214.389 us, total = 1.715 ms, Queueing time: mean = 245.938 us, max = 892.963 us, min = 19.329 us, total = 1.968 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	CoreWorker.PrintEventStats - 7 total (1 active, 1 running), Execution time: mean = 391.482 us, total = 2.740 ms, Queueing time: mean = 61.971 us, max = 120.816 us, min = 55.506 us, total = 433.795 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.465 us, total = 8.931 us, Queueing time: mean = 32.258 us, max = 64.515 us, min = 64.515 us, total = 64.515 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1261 total (1 active)
Queueing time: mean = 46.937 us, max = 5.086 ms, min = 9.694 us, total = 59.188 ms
Execution time:  mean = 416.171 us, total = 524.791 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 420 total (0 active), Execution time: mean = 924.229 us, total = 388.176 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 420 total (1 active), Execution time: mean = 289.177 us, total = 121.455 ms, Queueing time: mean = 109.926 us, max = 5.086 ms, min = 17.643 us, total = 46.169 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 420 total (0 active), Execution time: mean = 35.372 us, total = 14.856 ms, Queueing time: mean = 30.959 us, max = 393.317 us, min = 9.694 us, total = 13.003 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:25:07,350 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 6499 total (7 active)
Queueing time: mean = 74.795 us, max = 7.256 ms, min = -0.000 s, total = 486.094 ms
Execution time:  mean = 37.239 ms, total = 242.017 s
Event stats:
	CoreWorker.RecoverObjects - 4796 total (1 active), Execution time: mean = 14.175 us, total = 67.986 ms, Queueing time: mean = 82.592 us, max = 7.256 ms, min = -0.000 s, total = 396.111 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 480 total (0 active), Execution time: mean = 950.913 us, total = 456.438 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 480 total (1 active), Execution time: mean = 252.508 us, total = 121.204 ms, Queueing time: mean = 86.780 us, max = 4.451 ms, min = 11.049 us, total = 41.654 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 480 total (0 active), Execution time: mean = 27.429 us, total = 13.166 ms, Queueing time: mean = 37.426 us, max = 712.527 us, min = 11.997 us, total = 17.965 ms
	CoreWorker.RecordMetrics - 96 total (1 active), Execution time: mean = 42.253 us, total = 4.056 ms, Queueing time: mean = 100.122 us, max = 2.712 ms, min = 8.171 us, total = 9.612 ms
	CoreWorker.TryDelPendingObjectRefStreams - 48 total (1 active), Execution time: mean = 9.160 us, total = 439.679 us, Queueing time: mean = 67.641 us, max = 143.234 us, min = 25.662 us, total = 3.247 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 32 total (0 active), Execution time: mean = 462.550 us, total = 14.802 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 32 total (0 active), Execution time: mean = 44.811 us, total = 1.434 ms, Queueing time: mean = 104.149 us, max = 455.227 us, min = 14.635 us, total = 3.333 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.813 s, total = 241.317 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 214.389 us, total = 1.715 ms, Queueing time: mean = 245.938 us, max = 892.963 us, min = 19.329 us, total = 1.968 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	CoreWorker.PrintEventStats - 8 total (1 active, 1 running), Execution time: mean = 416.380 us, total = 3.331 ms, Queueing time: mean = 65.550 us, max = 120.816 us, min = 55.506 us, total = 524.400 us
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.465 us, total = 8.931 us, Queueing time: mean = 32.258 us, max = 64.515 us, min = 64.515 us, total = 64.515 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1441 total (1 active)
Queueing time: mean = 45.197 us, max = 5.086 ms, min = 9.694 us, total = 65.129 ms
Execution time:  mean = 414.754 us, total = 597.660 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 480 total (0 active), Execution time: mean = 920.026 us, total = 441.612 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 480 total (1 active), Execution time: mean = 288.678 us, total = 138.565 ms, Queueing time: mean = 104.357 us, max = 5.086 ms, min = 17.643 us, total = 50.091 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 480 total (0 active), Execution time: mean = 35.787 us, total = 17.178 ms, Queueing time: mean = 31.293 us, max = 393.317 us, min = 9.694 us, total = 15.021 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:26:07,351 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 7305 total (7 active)
Queueing time: mean = 75.904 us, max = 7.256 ms, min = -0.000 s, total = 554.477 ms
Execution time:  mean = 33.142 ms, total = 242.106 s
Event stats:
	CoreWorker.RecoverObjects - 5395 total (1 active), Execution time: mean = 14.242 us, total = 76.834 ms, Queueing time: mean = 84.358 us, max = 7.256 ms, min = -0.000 s, total = 455.114 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 540 total (0 active), Execution time: mean = 957.163 us, total = 516.868 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 540 total (1 active), Execution time: mean = 251.765 us, total = 135.953 ms, Queueing time: mean = 86.314 us, max = 4.451 ms, min = 11.049 us, total = 46.609 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 540 total (0 active), Execution time: mean = 27.248 us, total = 14.714 ms, Queueing time: mean = 37.561 us, max = 712.527 us, min = 11.997 us, total = 20.283 ms
	CoreWorker.RecordMetrics - 108 total (1 active), Execution time: mean = 41.507 us, total = 4.483 ms, Queueing time: mean = 96.194 us, max = 2.712 ms, min = 8.171 us, total = 10.389 ms
	CoreWorker.TryDelPendingObjectRefStreams - 54 total (1 active), Execution time: mean = 9.324 us, total = 503.518 us, Queueing time: mean = 68.394 us, max = 143.234 us, min = 25.662 us, total = 3.693 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 36 total (0 active), Execution time: mean = 464.597 us, total = 16.725 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 36 total (0 active), Execution time: mean = 43.746 us, total = 1.575 ms, Queueing time: mean = 115.005 us, max = 577.868 us, min = 14.635 us, total = 4.140 ms
	CoreWorker.PrintEventStats - 9 total (1 active, 1 running), Execution time: mean = 419.812 us, total = 3.778 ms, Queueing time: mean = 66.767 us, max = 120.816 us, min = 55.506 us, total = 600.901 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.813 s, total = 241.317 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 214.389 us, total = 1.715 ms, Queueing time: mean = 245.938 us, max = 892.963 us, min = 19.329 us, total = 1.968 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 2 total (1 active), Execution time: mean = 4.465 us, total = 8.931 us, Queueing time: mean = 32.258 us, max = 64.515 us, min = 64.515 us, total = 64.515 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1621 total (1 active)
Queueing time: mean = 44.765 us, max = 5.086 ms, min = 9.694 us, total = 72.564 ms
Execution time:  mean = 417.654 us, total = 677.018 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 540 total (0 active), Execution time: mean = 926.922 us, total = 500.538 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 540 total (1 active), Execution time: mean = 290.016 us, total = 156.609 ms, Queueing time: mean = 102.105 us, max = 5.086 ms, min = 17.643 us, total = 55.137 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 540 total (0 active), Execution time: mean = 36.234 us, total = 19.566 ms, Queueing time: mean = 32.242 us, max = 483.449 us, min = 9.694 us, total = 17.411 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:27:07,352 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 8113 total (7 active)
Queueing time: mean = 79.306 us, max = 7.256 ms, min = -0.000 s, total = 643.408 ms
Execution time:  mean = 29.854 ms, total = 242.203 s
Event stats:
	CoreWorker.RecoverObjects - 5995 total (1 active), Execution time: mean = 14.519 us, total = 87.042 ms, Queueing time: mean = 88.791 us, max = 7.256 ms, min = -0.000 s, total = 532.304 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 600 total (0 active), Execution time: mean = 967.999 us, total = 580.800 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 600 total (1 active), Execution time: mean = 255.875 us, total = 153.525 ms, Queueing time: mean = 89.099 us, max = 4.451 ms, min = 11.049 us, total = 53.459 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 600 total (0 active), Execution time: mean = 27.543 us, total = 16.526 ms, Queueing time: mean = 37.575 us, max = 712.527 us, min = 11.997 us, total = 22.545 ms
	CoreWorker.RecordMetrics - 120 total (1 active), Execution time: mean = 41.162 us, total = 4.939 ms, Queueing time: mean = 93.970 us, max = 2.712 ms, min = 8.171 us, total = 11.276 ms
	CoreWorker.TryDelPendingObjectRefStreams - 60 total (1 active), Execution time: mean = 9.276 us, total = 556.582 us, Queueing time: mean = 70.007 us, max = 143.234 us, min = 25.662 us, total = 4.200 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 40 total (0 active), Execution time: mean = 469.096 us, total = 18.764 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 40 total (0 active), Execution time: mean = 43.017 us, total = 1.721 ms, Queueing time: mean = 129.784 us, max = 581.047 us, min = 14.635 us, total = 5.191 ms
	CoreWorker.PrintEventStats - 10 total (1 active, 1 running), Execution time: mean = 457.981 us, total = 4.580 ms, Queueing time: mean = 71.182 us, max = 120.816 us, min = 55.506 us, total = 711.816 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 9 total (1 active), Execution time: mean = 26.813 s, total = 241.317 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 8 total (0 active), Execution time: mean = 214.389 us, total = 1.715 ms, Queueing time: mean = 245.938 us, max = 892.963 us, min = 19.329 us, total = 1.968 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 6.574 us, total = 19.721 us, Queueing time: mean = 45.190 us, max = 71.056 us, min = 64.515 us, total = 135.571 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1801 total (1 active)
Queueing time: mean = 45.438 us, max = 5.086 ms, min = 9.694 us, total = 81.835 ms
Execution time:  mean = 421.651 us, total = 759.394 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 600 total (0 active), Execution time: mean = 932.521 us, total = 559.513 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 600 total (1 active), Execution time: mean = 296.414 us, total = 177.848 ms, Queueing time: mean = 104.058 us, max = 5.086 ms, min = 17.643 us, total = 62.435 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 600 total (0 active), Execution time: mean = 36.215 us, total = 21.729 ms, Queueing time: mean = 32.306 us, max = 483.449 us, min = 9.694 us, total = 19.384 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:28:07,352 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 8921 total (7 active)
Queueing time: mean = 78.879 us, max = 7.256 ms, min = -0.000 s, total = 703.677 ms
Execution time:  mean = 68.877 ms, total = 614.450 s
Event stats:
	CoreWorker.RecoverObjects - 6594 total (1 active), Execution time: mean = 14.453 us, total = 95.300 ms, Queueing time: mean = 88.557 us, max = 7.256 ms, min = -0.000 s, total = 583.944 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 660 total (0 active), Execution time: mean = 968.244 us, total = 639.041 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 660 total (1 active), Execution time: mean = 257.170 us, total = 169.732 ms, Queueing time: mean = 88.264 us, max = 4.451 ms, min = 11.049 us, total = 58.254 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 660 total (0 active), Execution time: mean = 27.573 us, total = 18.198 ms, Queueing time: mean = 36.901 us, max = 712.527 us, min = 11.997 us, total = 24.354 ms
	CoreWorker.RecordMetrics - 132 total (1 active), Execution time: mean = 41.009 us, total = 5.413 ms, Queueing time: mean = 93.110 us, max = 2.712 ms, min = 8.171 us, total = 12.291 ms
	CoreWorker.TryDelPendingObjectRefStreams - 66 total (1 active), Execution time: mean = 9.301 us, total = 613.862 us, Queueing time: mean = 72.601 us, max = 163.144 us, min = 25.662 us, total = 4.792 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 44 total (0 active), Execution time: mean = 487.732 us, total = 21.460 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 44 total (0 active), Execution time: mean = 44.054 us, total = 1.938 ms, Queueing time: mean = 127.560 us, max = 581.047 us, min = 14.635 us, total = 5.613 ms
	CoreWorker.PrintEventStats - 11 total (1 active, 1 running), Execution time: mean = 466.465 us, total = 5.131 ms, Queueing time: mean = 52.232 us, max = 120.816 us, min = -0.000 s, total = 574.557 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 61.348 s, total = 613.476 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.563 us, total = 1.913 ms, Queueing time: mean = 233.667 us, max = 892.963 us, min = 19.329 us, total = 2.103 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 6.574 us, total = 19.721 us, Queueing time: mean = 45.190 us, max = 71.056 us, min = 64.515 us, total = 135.571 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 1981 total (1 active)
Queueing time: mean = 45.252 us, max = 5.086 ms, min = 9.694 us, total = 89.644 ms
Execution time:  mean = 423.811 us, total = 839.571 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 660 total (0 active), Execution time: mean = 937.809 us, total = 618.954 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 660 total (1 active), Execution time: mean = 297.499 us, total = 196.349 ms, Queueing time: mean = 103.671 us, max = 5.086 ms, min = 17.643 us, total = 68.423 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 660 total (0 active), Execution time: mean = 36.308 us, total = 23.963 ms, Queueing time: mean = 32.128 us, max = 483.449 us, min = 9.694 us, total = 21.205 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:29:07,353 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 9728 total (7 active)
Queueing time: mean = 78.388 us, max = 7.256 ms, min = -0.000 s, total = 762.563 ms
Execution time:  mean = 63.172 ms, total = 614.537 s
Event stats:
	CoreWorker.RecoverObjects - 7194 total (1 active), Execution time: mean = 14.417 us, total = 103.714 ms, Queueing time: mean = 88.041 us, max = 7.256 ms, min = -0.000 s, total = 633.368 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 720 total (0 active), Execution time: mean = 968.232 us, total = 697.127 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 720 total (1 active), Execution time: mean = 256.717 us, total = 184.836 ms, Queueing time: mean = 88.230 us, max = 4.451 ms, min = 11.049 us, total = 63.526 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 720 total (0 active), Execution time: mean = 27.487 us, total = 19.790 ms, Queueing time: mean = 36.198 us, max = 712.527 us, min = 11.997 us, total = 26.062 ms
	CoreWorker.RecordMetrics - 144 total (1 active), Execution time: mean = 40.810 us, total = 5.877 ms, Queueing time: mean = 91.505 us, max = 2.712 ms, min = 8.171 us, total = 13.177 ms
	CoreWorker.TryDelPendingObjectRefStreams - 72 total (1 active), Execution time: mean = 9.309 us, total = 670.223 us, Queueing time: mean = 71.404 us, max = 163.144 us, min = 25.662 us, total = 5.141 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 48 total (0 active), Execution time: mean = 499.718 us, total = 23.986 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 48 total (0 active), Execution time: mean = 46.994 us, total = 2.256 ms, Queueing time: mean = 139.008 us, max = 893.660 us, min = 14.635 us, total = 6.672 ms
	CoreWorker.PrintEventStats - 12 total (1 active, 1 running), Execution time: mean = 470.311 us, total = 5.644 ms, Queueing time: mean = 63.542 us, max = 187.950 us, min = -0.000 s, total = 762.507 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 61.348 s, total = 613.476 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.563 us, total = 1.913 ms, Queueing time: mean = 233.667 us, max = 892.963 us, min = 19.329 us, total = 2.103 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 6.574 us, total = 19.721 us, Queueing time: mean = 45.190 us, max = 71.056 us, min = 64.515 us, total = 135.571 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2161 total (1 active)
Queueing time: mean = 44.753 us, max = 5.086 ms, min = 9.694 us, total = 96.712 ms
Execution time:  mean = 425.354 us, total = 919.189 ms
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 720 total (0 active), Execution time: mean = 940.289 us, total = 677.008 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 720 total (1 active), Execution time: mean = 299.461 us, total = 215.612 ms, Queueing time: mean = 102.403 us, max = 5.086 ms, min = 17.643 us, total = 73.730 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 720 total (0 active), Execution time: mean = 36.479 us, total = 26.265 ms, Queueing time: mean = 31.896 us, max = 483.449 us, min = 9.694 us, total = 22.965 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:30:07,353 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 10534 total (7 active)
Queueing time: mean = 79.683 us, max = 8.336 ms, min = -0.000 s, total = 839.378 ms
Execution time:  mean = 58.349 ms, total = 614.649 s
Event stats:
	CoreWorker.RecoverObjects - 7793 total (1 active), Execution time: mean = 14.389 us, total = 112.133 ms, Queueing time: mean = 88.849 us, max = 7.256 ms, min = -0.000 s, total = 692.398 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 780 total (0 active), Execution time: mean = 997.491 us, total = 778.043 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 780 total (1 active), Execution time: mean = 258.760 us, total = 201.833 ms, Queueing time: mean = 98.403 us, max = 8.336 ms, min = 11.049 us, total = 76.754 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 780 total (0 active), Execution time: mean = 27.754 us, total = 21.648 ms, Queueing time: mean = 36.077 us, max = 712.527 us, min = 11.997 us, total = 28.140 ms
	CoreWorker.RecordMetrics - 156 total (1 active), Execution time: mean = 40.605 us, total = 6.334 ms, Queueing time: mean = 93.019 us, max = 2.712 ms, min = 8.171 us, total = 14.511 ms
	CoreWorker.TryDelPendingObjectRefStreams - 78 total (1 active), Execution time: mean = 9.314 us, total = 726.463 us, Queueing time: mean = 71.192 us, max = 163.144 us, min = 25.424 us, total = 5.553 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 52 total (0 active), Execution time: mean = 502.617 us, total = 26.136 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 52 total (0 active), Execution time: mean = 47.043 us, total = 2.446 ms, Queueing time: mean = 140.876 us, max = 893.660 us, min = 14.635 us, total = 7.326 ms
	CoreWorker.PrintEventStats - 13 total (1 active, 1 running), Execution time: mean = 471.448 us, total = 6.129 ms, Queueing time: mean = 64.720 us, max = 187.950 us, min = -0.000 s, total = 841.358 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 61.348 s, total = 613.476 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.563 us, total = 1.913 ms, Queueing time: mean = 233.667 us, max = 892.963 us, min = 19.329 us, total = 2.103 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 6.574 us, total = 19.721 us, Queueing time: mean = 45.190 us, max = 71.056 us, min = 64.515 us, total = 135.571 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2341 total (1 active)
Queueing time: mean = 44.347 us, max = 5.086 ms, min = 9.694 us, total = 103.816 ms
Execution time:  mean = 429.380 us, total = 1.005 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 780 total (0 active), Execution time: mean = 951.886 us, total = 742.471 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 780 total (1 active), Execution time: mean = 299.848 us, total = 233.882 ms, Queueing time: mean = 101.016 us, max = 5.086 ms, min = 17.643 us, total = 78.792 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 780 total (0 active), Execution time: mean = 36.566 us, total = 28.521 ms, Queueing time: mean = 32.060 us, max = 483.449 us, min = 9.694 us, total = 25.007 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:31:07,354 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 11340 total (7 active)
Queueing time: mean = 79.737 us, max = 8.336 ms, min = -0.000 s, total = 904.215 ms
Execution time:  mean = 54.210 ms, total = 614.738 s
Event stats:
	CoreWorker.RecoverObjects - 8392 total (1 active), Execution time: mean = 14.409 us, total = 120.918 ms, Queueing time: mean = 89.201 us, max = 7.256 ms, min = -0.000 s, total = 748.577 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 840 total (0 active), Execution time: mean = 997.087 us, total = 837.553 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 840 total (1 active), Execution time: mean = 260.357 us, total = 218.700 ms, Queueing time: mean = 97.197 us, max = 8.336 ms, min = 11.049 us, total = 81.645 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 840 total (0 active), Execution time: mean = 27.878 us, total = 23.418 ms, Queueing time: mean = 35.908 us, max = 712.527 us, min = 11.997 us, total = 30.163 ms
	CoreWorker.RecordMetrics - 168 total (1 active), Execution time: mean = 40.241 us, total = 6.761 ms, Queueing time: mean = 91.636 us, max = 2.712 ms, min = 8.171 us, total = 15.395 ms
	CoreWorker.TryDelPendingObjectRefStreams - 84 total (1 active), Execution time: mean = 9.364 us, total = 786.571 us, Queueing time: mean = 70.941 us, max = 163.144 us, min = 25.424 us, total = 5.959 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 56 total (0 active), Execution time: mean = 496.663 us, total = 27.813 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 56 total (0 active), Execution time: mean = 46.146 us, total = 2.584 ms, Queueing time: mean = 137.618 us, max = 893.660 us, min = 14.635 us, total = 7.707 ms
	CoreWorker.PrintEventStats - 14 total (1 active, 1 running), Execution time: mean = 469.150 us, total = 6.568 ms, Queueing time: mean = 65.332 us, max = 187.950 us, min = -0.000 s, total = 914.642 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 61.348 s, total = 613.476 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.563 us, total = 1.913 ms, Queueing time: mean = 233.667 us, max = 892.963 us, min = 19.329 us, total = 2.103 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 3 total (1 active), Execution time: mean = 6.574 us, total = 19.721 us, Queueing time: mean = 45.190 us, max = 71.056 us, min = 64.515 us, total = 135.571 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2521 total (1 active)
Queueing time: mean = 43.946 us, max = 5.086 ms, min = 9.694 us, total = 110.788 ms
Execution time:  mean = 429.552 us, total = 1.083 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 840 total (0 active), Execution time: mean = 951.724 us, total = 799.448 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 840 total (1 active), Execution time: mean = 300.527 us, total = 252.443 ms, Queueing time: mean = 99.759 us, max = 5.086 ms, min = 17.643 us, total = 83.798 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 840 total (0 active), Execution time: mean = 36.555 us, total = 30.706 ms, Queueing time: mean = 32.111 us, max = 483.449 us, min = 9.694 us, total = 26.974 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:32:07,354 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 12148 total (7 active)
Queueing time: mean = 79.048 us, max = 8.336 ms, min = -0.000 s, total = 960.273 ms
Execution time:  mean = 50.611 ms, total = 614.822 s
Event stats:
	CoreWorker.RecoverObjects - 8992 total (1 active), Execution time: mean = 14.365 us, total = 129.170 ms, Queueing time: mean = 88.337 us, max = 7.256 ms, min = -0.000 s, total = 794.328 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 900 total (0 active), Execution time: mean = 992.653 us, total = 893.388 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.InternalHeartbeat - 900 total (1 active), Execution time: mean = 260.173 us, total = 234.155 ms, Queueing time: mean = 96.034 us, max = 8.336 ms, min = 11.049 us, total = 86.431 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 900 total (0 active), Execution time: mean = 27.968 us, total = 25.171 ms, Queueing time: mean = 35.568 us, max = 712.527 us, min = 11.997 us, total = 32.011 ms
	CoreWorker.RecordMetrics - 180 total (1 active), Execution time: mean = 40.671 us, total = 7.321 ms, Queueing time: mean = 90.963 us, max = 2.712 ms, min = 8.171 us, total = 16.373 ms
	CoreWorker.TryDelPendingObjectRefStreams - 90 total (1 active), Execution time: mean = 9.304 us, total = 837.341 us, Queueing time: mean = 91.027 us, max = 1.934 ms, min = 18.851 us, total = 8.192 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 60 total (0 active), Execution time: mean = 486.586 us, total = 29.195 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 60 total (0 active), Execution time: mean = 47.848 us, total = 2.871 ms, Queueing time: mean = 134.160 us, max = 893.660 us, min = 14.635 us, total = 8.050 ms
	CoreWorker.PrintEventStats - 15 total (1 active, 1 running), Execution time: mean = 463.041 us, total = 6.946 ms, Queueing time: mean = 63.752 us, max = 187.950 us, min = -0.000 s, total = 956.274 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 61.348 s, total = 613.476 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.563 us, total = 1.913 ms, Queueing time: mean = 233.667 us, max = 892.963 us, min = 19.329 us, total = 2.103 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 7.876 us, total = 31.503 us, Queueing time: mean = 53.196 us, max = 77.212 us, min = 64.515 us, total = 212.783 us
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2701 total (1 active)
Queueing time: mean = 43.731 us, max = 5.086 ms, min = 9.694 us, total = 118.116 ms
Execution time:  mean = 433.377 us, total = 1.171 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 900 total (0 active), Execution time: mean = 957.220 us, total = 861.498 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 900 total (1 active), Execution time: mean = 306.340 us, total = 275.706 ms, Queueing time: mean = 98.412 us, max = 5.086 ms, min = 17.643 us, total = 88.571 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 900 total (0 active), Execution time: mean = 36.713 us, total = 33.042 ms, Queueing time: mean = 32.810 us, max = 483.449 us, min = 9.694 us, total = 29.529 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:33:07,355 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 12955 total (7 active)
Queueing time: mean = 86.284 us, max = 103.669 ms, min = -0.000 s, total = 1.118 s
Execution time:  mean = 47.489 ms, total = 615.214 s
Event stats:
	CoreWorker.RecoverObjects - 9590 total (1 active), Execution time: mean = 14.364 us, total = 137.752 ms, Queueing time: mean = 98.366 us, max = 103.669 ms, min = -0.000 s, total = 943.334 ms
	CoreWorker.InternalHeartbeat - 960 total (1 active), Execution time: mean = 261.053 us, total = 250.611 ms, Queueing time: mean = 94.845 us, max = 8.336 ms, min = 11.049 us, total = 91.051 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 960 total (0 active), Execution time: mean = 991.658 us, total = 951.991 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 960 total (0 active), Execution time: mean = 28.290 us, total = 27.158 ms, Queueing time: mean = 35.413 us, max = 712.527 us, min = 11.997 us, total = 33.996 ms
	CoreWorker.RecordMetrics - 192 total (1 active), Execution time: mean = 40.341 us, total = 7.745 ms, Queueing time: mean = 90.846 us, max = 2.712 ms, min = 8.171 us, total = 17.443 ms
	CoreWorker.TryDelPendingObjectRefStreams - 96 total (1 active), Execution time: mean = 9.401 us, total = 902.459 us, Queueing time: mean = 89.546 us, max = 1.934 ms, min = 18.851 us, total = 8.596 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 64 total (0 active), Execution time: mean = 47.502 us, total = 3.040 ms, Queueing time: mean = 130.458 us, max = 893.660 us, min = 14.635 us, total = 8.349 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 64 total (0 active), Execution time: mean = 488.685 us, total = 31.276 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 16 total (1 active, 1 running), Execution time: mean = 461.710 us, total = 7.387 ms, Queueing time: mean = 66.001 us, max = 187.950 us, min = -0.000 s, total = 1.056 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 61.348 s, total = 613.476 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.563 us, total = 1.913 ms, Queueing time: mean = 233.667 us, max = 892.963 us, min = 19.329 us, total = 2.103 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 7.876 us, total = 31.503 us, Queueing time: mean = 53.196 us, max = 77.212 us, min = 64.515 us, total = 212.783 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	CoreWorkerService.grpc_server.LocalGC.HandleRequestImpl - 1 total (0 active), Execution time: mean = 151.593 ms, total = 151.593 ms, Queueing time: mean = 47.855 us, max = 47.855 us, min = 47.855 us, total = 47.855 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	CoreWorkerService.grpc_server.LocalGC - 1 total (0 active), Execution time: mean = 151.610 ms, total = 151.610 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 2881 total (1 active)
Queueing time: mean = 43.498 us, max = 5.086 ms, min = 9.694 us, total = 125.316 ms
Execution time:  mean = 433.543 us, total = 1.249 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 960 total (0 active), Execution time: mean = 958.034 us, total = 919.712 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 960 total (1 active), Execution time: mean = 306.060 us, total = 293.818 ms, Queueing time: mean = 97.130 us, max = 5.086 ms, min = 17.643 us, total = 93.244 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 960 total (0 active), Execution time: mean = 36.669 us, total = 35.202 ms, Queueing time: mean = 33.391 us, max = 483.449 us, min = 9.694 us, total = 32.056 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


[2025-06-26 01:34:07,356 I 117 701550] core_worker.cc:902: Event stats:


Global stats: 13762 total (7 active)
Queueing time: mean = 85.701 us, max = 103.669 ms, min = -0.000 s, total = 1.179 s
Execution time:  mean = 44.710 ms, total = 615.304 s
Event stats:
	CoreWorker.RecoverObjects - 10190 total (1 active), Execution time: mean = 14.354 us, total = 146.266 ms, Queueing time: mean = 97.710 us, max = 103.669 ms, min = -0.000 s, total = 995.661 ms
	CoreWorker.InternalHeartbeat - 1020 total (1 active), Execution time: mean = 261.533 us, total = 266.763 ms, Queueing time: mean = 94.108 us, max = 8.336 ms, min = 11.049 us, total = 95.990 ms
	NodeManagerService.grpc_client.ReportWorkerBacklog - 1020 total (0 active), Execution time: mean = 991.985 us, total = 1.012 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	NodeManagerService.grpc_client.ReportWorkerBacklog.OnReplyReceived - 1020 total (0 active), Execution time: mean = 28.631 us, total = 29.204 ms, Queueing time: mean = 35.486 us, max = 712.527 us, min = 11.997 us, total = 36.196 ms
	CoreWorker.RecordMetrics - 204 total (1 active), Execution time: mean = 40.052 us, total = 8.171 ms, Queueing time: mean = 90.181 us, max = 2.712 ms, min = 8.171 us, total = 18.397 ms
	CoreWorker.TryDelPendingObjectRefStreams - 102 total (1 active), Execution time: mean = 9.449 us, total = 963.764 us, Queueing time: mean = 89.530 us, max = 1.934 ms, min = 18.851 us, total = 9.132 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats.HandleRequestImpl - 68 total (0 active), Execution time: mean = 46.507 us, total = 3.162 ms, Queueing time: mean = 131.026 us, max = 893.660 us, min = 14.635 us, total = 8.910 ms
	CoreWorkerService.grpc_server.GetCoreWorkerStats - 68 total (0 active), Execution time: mean = 481.726 us, total = 32.757 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.PrintEventStats - 17 total (1 active, 1 running), Execution time: mean = 471.664 us, total = 8.018 ms, Queueing time: mean = 67.440 us, max = 187.950 us, min = -0.000 s, total = 1.146 ms
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll - 10 total (1 active), Execution time: mean = 61.348 s, total = 613.476 s, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberPoll.OnReplyReceived - 9 total (0 active), Execution time: mean = 212.563 us, total = 1.913 ms, Queueing time: mean = 233.667 us, max = 892.963 us, min = 19.329 us, total = 2.103 ms
	Subscriber.HandlePublishedMessage_GCS_NODE_INFO_CHANNEL - 8 total (0 active), Execution time: mean = 63.394 us, total = 507.149 us, Queueing time: mean = 180.858 us, max = 289.825 us, min = 134.991 us, total = 1.447 ms
	PeriodicalRunner.RunFnPeriodically - 6 total (0 active), Execution time: mean = 320.300 us, total = 1.922 ms, Queueing time: mean = 1.611 ms, max = 2.904 ms, min = 33.679 us, total = 9.665 ms
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo - 4 total (0 active), Execution time: mean = 1.188 ms, total = 4.752 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::ActorInfoGcsService.grpc_client.GetNamedActorInfo.OnReplyReceived - 4 total (0 active), Execution time: mean = 50.780 us, total = 203.120 us, Queueing time: mean = 51.452 us, max = 59.075 us, min = 39.025 us, total = 205.809 us
	Publisher.CheckDeadSubscribers - 4 total (1 active), Execution time: mean = 7.876 us, total = 31.503 us, Queueing time: mean = 53.196 us, max = 77.212 us, min = 64.515 us, total = 212.783 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.162 ms, total = 1.162 ms, Queueing time: mean = 34.328 us, max = 34.328 us, min = 34.328 us, total = 34.328 us
	CoreWorkerService.grpc_server.LocalGC.HandleRequestImpl - 1 total (0 active), Execution time: mean = 151.593 ms, total = 151.593 ms, Queueing time: mean = 47.855 us, max = 47.855 us, min = 47.855 us, total = 47.855 us
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch - 1 total (0 active), Execution time: mean = 1.044 ms, total = 1.044 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo - 1 total (0 active), Execution time: mean = 978.025 us, total = 978.025 us, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::WorkerInfoGcsService.grpc_client.AddWorkerInfo.OnReplyReceived - 1 total (0 active), Execution time: mean = 24.210 us, total = 24.210 us, Queueing time: mean = 19.461 us, max = 19.461 us, min = 19.461 us, total = 19.461 us
	ray::rpc::NodeInfoGcsService.grpc_client.GetAllNodeInfo - 1 total (0 active), Execution time: mean = 3.184 ms, total = 3.184 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	ray::rpc::InternalPubSubGcsService.grpc_client.GcsSubscriberCommandBatch.OnReplyReceived - 1 total (0 active), Execution time: mean = 1.368 ms, total = 1.368 ms, Queueing time: mean = 244.223 us, max = 244.223 us, min = 244.223 us, total = 244.223 us
	CoreWorkerService.grpc_server.LocalGC - 1 total (0 active), Execution time: mean = 151.610 ms, total = 151.610 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s

-----------------
Task execution event stats:

Global stats: 0 total (0 active)
Queueing time: mean = -nan s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
Execution time:  mean = -nan s, total = 0.000 s
Event stats:

-----------------
Task Event stats:

IO Service Stats:

Global stats: 3061 total (1 active)
Queueing time: mean = 43.348 us, max = 5.086 ms, min = 9.694 us, total = 132.689 ms
Execution time:  mean = 433.923 us, total = 1.328 s
Event stats:
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData - 1020 total (0 active), Execution time: mean = 958.822 us, total = 977.998 ms, Queueing time: mean = 0.000 s, max = -0.000 s, min = 9223372036.855 s, total = 0.000 s
	CoreWorker.deadline_timer.flush_task_events - 1020 total (1 active), Execution time: mean = 306.349 us, total = 312.476 ms, Queueing time: mean = 96.470 us, max = 5.086 ms, min = 17.643 us, total = 98.399 ms
	ray::rpc::TaskInfoGcsService.grpc_client.AddTaskEventData.OnReplyReceived - 1020 total (0 active), Execution time: mean = 36.725 us, total = 37.460 ms, Queueing time: mean = 33.601 us, max = 483.449 us, min = 9.694 us, total = 34.273 ms
	PeriodicalRunner.RunFnPeriodically - 1 total (0 active), Execution time: mean = 304.507 us, total = 304.507 us, Queueing time: mean = 16.510 us, max = 16.510 us, min = 16.510 us, total = 16.510 us
Other Stats:
	grpc_in_progress:0
	current number of task status events in buffer: 0
	current number of profile events in buffer: 0
	current number of dropped task attempts tracked: 0
	total task events sent: 0.000109673 MiB
	total number of task attempts sent: 1
	total number of task attempts dropped reported: 0
	total number of sent failure: 0
	num status task events dropped: 0
	num profile task events dropped: 0


