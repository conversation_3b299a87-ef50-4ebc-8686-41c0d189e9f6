# Flight Server Refactor Implementation Plan

## Overview
This plan implements the comprehensive refactor outlined in `refactor_plan.md` to fix race conditions and architectural issues in the Ray + Flight Server integration.

## Current Issues
- **Race Conditions**: Multiple Ray workers overwrite results in Flight Server
- **Data Bottleneck**: Driver aggregates all worker results through Ray
- **Missing Completion Signaling**: No way for driver to signal job completion to Flight Server
- **Test Failure**: `test_ndvi.py` expects 2 rows but only gets 1 due to data loss

## Architecture Changes

### Before (Current)
```
Workers → Ray Driver (aggregates data) → Flight Server → SDK Client
```

### After (Target)
```
Workers → Flight Server (accumulates data)
Driver → Flight Server (signals completion)
Flight Server → SDK Client (serves complete results)
```

## Implementation Phases

### Phase 1: Flight Server Refactor ⏳
**Goal**: Make Flight Server stateful and thread-safe with proper completion signaling

**Key Changes**:
- Replace global caches with instance-level ExecutionState management
- Add `completion_event` to ExecutionState for efficient waiting
- Implement completion/failure signaling via special do_put commands (`{execution_id}_COMPLETE`, `{execution_id}_FAILED`)
- Fix do_get to wait for completion signal instead of polling
- Ensure thread-safe batch accumulation with proper locking

### Phase 2: Driver Refactor ⏳
**Goal**: Remove data aggregation bottleneck and implement completion signaling

**Key Changes**:
- Remove result aggregation logic from `execute_workflow`
- Add `_signal_completion_to_flight_server` and `_signal_failure_to_flight_server` methods
- Change `execute_workflow` to not return data (fire-and-forget)
- Use `ray.get()` only for synchronization and error detection

### Phase 3: Worker Refactor ⏳
**Goal**: Eliminate data return bottleneck and ensure reliable uploads

**Key Changes**:
- Change return type from `Optional[pa.RecordBatch]` to `bool`
- Ensure `_upload_batch_to_flight_server` is always called
- Return `True` on success, raise exception on failure
- Remove data passing through Ray driver

### Phase 4: Testing & Validation ⏳
**Goal**: Verify the refactor fixes the issues and doesn't break existing functionality

**Key Changes**:
- Run `test_ndvi.py` to verify 2 rows are returned
- Test concurrent worker scenarios
- Ensure no regressions in existing functionality
- Validate end-to-end data flow

## File Modifications

### 1. `services/processing_engine/app/flight_server.py`
- **ExecutionState Class**: Add `completion_event = threading.Event()`
- **FlightServer Class**: Replace global caches with `self._executions: Dict[str, ExecutionState]`
- **do_put Method**: Handle completion signals (`_COMPLETE`, `_FAILED`) and batch accumulation
- **do_get Method**: Use `completion_event.wait()` instead of polling

### 2. `libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/driver.py`
- **execute_workflow Method**: Remove result aggregation, add completion signaling
- **New Methods**: `_signal_completion_to_flight_server`, `_signal_failure_to_flight_server`
- **Return Type**: Change from returning data to fire-and-forget

### 3. `libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/worker.py`
- **process_batch_on_worker Function**: Change return type to `bool`
- **Upload Logic**: Ensure reliable upload to Flight Server
- **Error Handling**: Raise exceptions instead of returning None

## Success Criteria
- [ ] `test_ndvi.py` passes with `assert result.num_rows == 2`
- [ ] No race conditions in concurrent worker scenarios
- [ ] No data loss during multi-worker processing
- [ ] Improved performance due to eliminated bottlenecks
- [ ] Existing tests continue to pass

## Risk Mitigation
- **Backward Compatibility**: Maintain existing API contracts where possible
- **Incremental Testing**: Test each phase independently
- **Rollback Plan**: Keep original implementation available for comparison
- **Performance Monitoring**: Verify no performance regressions

## Next Steps
1. Create detailed task breakdown
2. Implement Phase 1 (Flight Server refactor)
3. Implement Phase 2 (Driver refactor)
4. Implement Phase 3 (Worker refactor)
5. Run comprehensive testing (Phase 4)
