# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Analyze Ray Timeline Data

This script analyzes the Ray timeline JSON to understand task execution patterns,
worker utilization, and identify performance bottlenecks.
"""

import json
import statistics
from datetime import datetime
from typing import List, Dict, Any


def parse_ray_timeline(timeline_file: str) -> List[Dict[str, Any]]:
    """Parse Ray timeline JSON file."""
    with open(timeline_file, 'r') as f:
        content = f.read()
        # The file is a JSON array
        return json.loads(content)


def analyze_task_execution_patterns(events: List[Dict[str, Any]]) -> None:
    """Analyze task execution patterns from Ray timeline."""
    print("🔍 RAY TASK EXECUTION ANALYSIS")
    print("=" * 60)
    
    # Filter for task execution events
    task_events = [e for e in events if e.get('cat') == 'task:execute']
    
    if not task_events:
        print("❌ No task execution events found")
        return
    
    print(f"📊 Total task executions: {len(task_events)}")
    
    # Analyze execution durations
    durations = []
    worker_tasks = {}
    
    for event in task_events:
        duration_us = event.get('dur', 0)
        duration_s = duration_us / 1_000_000  # Convert microseconds to seconds
        durations.append(duration_s)
        
        # Track tasks per worker (pid)
        pid = event.get('pid', 0)
        if pid not in worker_tasks:
            worker_tasks[pid] = []
        worker_tasks[pid].append({
            'task_id': event['args']['task_id'],
            'duration': duration_s,
            'start_time': event['ts'],
            'end_time': event['ts'] + event['dur']
        })
    
    # Duration statistics
    print(f"\n⏱️ Task Duration Statistics:")
    print(f"  Average: {statistics.mean(durations):.2f}s")
    print(f"  Median:  {statistics.median(durations):.2f}s")
    print(f"  Min:     {min(durations):.2f}s")
    print(f"  Max:     {max(durations):.2f}s")
    print(f"  Std Dev: {statistics.stdev(durations):.2f}s")
    
    # Worker utilization analysis
    print(f"\n👷 Worker Utilization:")
    print(f"  Total workers: {len(worker_tasks)}")
    
    for pid, tasks in worker_tasks.items():
        task_count = len(tasks)
        total_time = sum(t['duration'] for t in tasks)
        avg_duration = total_time / task_count if task_count > 0 else 0
        
        print(f"  Worker {pid}: {task_count:>2} tasks, {total_time:>6.1f}s total, {avg_duration:>5.2f}s avg")
    
    # Identify longest running tasks
    print(f"\n🐌 Longest Running Tasks:")
    sorted_events = sorted(task_events, key=lambda x: x.get('dur', 0), reverse=True)
    
    for i, event in enumerate(sorted_events[:5]):
        duration_s = event.get('dur', 0) / 1_000_000
        task_id = event['args']['task_id'][:8]  # Short task ID
        pid = event.get('pid', 0)
        print(f"  {i+1}. Task {task_id} on Worker {pid}: {duration_s:.2f}s")


def analyze_worker_parallelism(events: List[Dict[str, Any]]) -> None:
    """Analyze worker parallelism and task overlap."""
    print(f"\n🔄 WORKER PARALLELISM ANALYSIS")
    print("=" * 60)
    
    # Filter for task execution events
    task_events = [e for e in events if e.get('cat') == 'task:execute']
    
    if not task_events:
        return
    
    # Create timeline of task execution
    timeline = []
    for event in task_events:
        start_time = event['ts']
        end_time = event['ts'] + event['dur']
        pid = event.get('pid', 0)
        
        timeline.append({
            'start': start_time,
            'end': end_time,
            'pid': pid,
            'task_id': event['args']['task_id'][:8]
        })
    
    # Sort by start time
    timeline.sort(key=lambda x: x['start'])
    
    # Find overlapping tasks (parallelism)
    max_concurrent = 0
    concurrent_tasks = []
    
    for i, task in enumerate(timeline):
        # Count how many tasks are running at this task's start time
        concurrent = 1  # This task itself
        
        for other_task in timeline:
            if other_task != task:
                # Check if other task overlaps with this task
                if (other_task['start'] <= task['start'] <= other_task['end'] or
                    task['start'] <= other_task['start'] <= task['end']):
                    concurrent += 1
        
        concurrent_tasks.append(concurrent)
        max_concurrent = max(max_concurrent, concurrent)
    
    avg_concurrent = statistics.mean(concurrent_tasks)
    
    print(f"📊 Parallelism Statistics:")
    print(f"  Maximum concurrent tasks: {max_concurrent}")
    print(f"  Average concurrent tasks: {avg_concurrent:.1f}")
    print(f"  Total unique workers used: {len(set(t['pid'] for t in timeline))}")
    
    # Show task execution timeline (first 10 tasks)
    print(f"\n📅 Task Execution Timeline (first 10 tasks):")
    print("Time (relative) | Worker | Task ID  | Duration")
    print("-" * 50)
    
    start_time = timeline[0]['start']
    
    for i, task in enumerate(timeline[:10]):
        relative_start = (task['start'] - start_time) / 1_000_000  # Convert to seconds
        duration = (task['end'] - task['start']) / 1_000_000
        
        print(f"{relative_start:>11.1f}s | {task['pid']:>6} | {task['task_id']} | {duration:>6.1f}s")


def analyze_task_scheduling_gaps(events: List[Dict[str, Any]]) -> None:
    """Analyze gaps between task scheduling and execution."""
    print(f"\n⏰ TASK SCHEDULING ANALYSIS")
    print("=" * 60)
    
    # Group events by task ID
    task_groups = {}
    
    for event in events:
        task_id = event['args'].get('task_id')
        if task_id:
            if task_id not in task_groups:
                task_groups[task_id] = []
            task_groups[task_id].append(event)
    
    # Analyze scheduling delays
    scheduling_delays = []
    
    for task_id, task_events in task_groups.items():
        # Sort events by timestamp
        task_events.sort(key=lambda x: x['ts'])
        
        deserialize_event = None
        execute_event = None
        
        for event in task_events:
            if event.get('cat') == 'task:deserialize_arguments':
                deserialize_event = event
            elif event.get('cat') == 'task:execute':
                execute_event = event
                break
        
        if deserialize_event and execute_event:
            # Calculate delay between deserialization and execution
            delay_us = execute_event['ts'] - (deserialize_event['ts'] + deserialize_event['dur'])
            delay_s = delay_us / 1_000_000
            
            if delay_s >= 0:  # Valid delay
                scheduling_delays.append(delay_s)
    
    if scheduling_delays:
        print(f"📊 Scheduling Delay Statistics:")
        print(f"  Average delay: {statistics.mean(scheduling_delays):.3f}s")
        print(f"  Median delay:  {statistics.median(scheduling_delays):.3f}s")
        print(f"  Max delay:     {max(scheduling_delays):.3f}s")
        print(f"  Min delay:     {min(scheduling_delays):.3f}s")
    else:
        print("❌ No valid scheduling delays found")


def main():
    """Main analysis function."""
    timeline_file = "/home/<USER>/Work/platform-build/ray_logs/timeline-02000000-2025-06-26_01-39-16.json"
    
    print("🔍 RAY TIMELINE ANALYSIS")
    print("=" * 80)
    print(f"📁 Analyzing: {timeline_file}")
    print("=" * 80)
    
    try:
        events = parse_ray_timeline(timeline_file)
        print(f"📊 Total events in timeline: {len(events)}")
        
        analyze_task_execution_patterns(events)
        analyze_worker_parallelism(events)
        analyze_task_scheduling_gaps(events)
        
        print("\n" + "=" * 80)
        print("✅ Ray Timeline Analysis Complete!")
        print("=" * 80)
        
    except FileNotFoundError:
        print(f"❌ Timeline file not found: {timeline_file}")
    except Exception as e:
        print(f"❌ Error analyzing timeline: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
